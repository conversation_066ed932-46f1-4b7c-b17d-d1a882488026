/*! This file is auto-generated */
!function(J){var a,s,t,e,n,i,Y=wp.customize,o=window.matchMedia("(prefers-reduced-motion: reduce)"),r=o.matches;o.addEventListener("change",function(e){r=e.matches}),Y.OverlayNotification=Y.Notification.extend({loading:!1,initialize:function(e,t){var n=this;Y.Notification.prototype.initialize.call(n,e,t),n.containerClasses+=" notification-overlay",n.loading&&(n.containerClasses+=" notification-loading")},render:function(){var e=Y.Notification.prototype.render.call(this);return e.on("keydown",_.bind(this.handleEscape,this)),e},handleEscape:function(e){var t=this;27===e.which&&(e.stopPropagation(),t.dismissible)&&t.parent&&t.parent.remove(t.code)}}),Y.Notifications=Y.Values.extend({alt:!1,defaultConstructor:Y.Notification,initialize:function(e){var t=this;Y.Values.prototype.initialize.call(t,e),_.bindAll(t,"constrainFocus"),t._addedIncrement=0,t._addedOrder={},t.bind("add",function(e){t.trigger("change",e)}),t.bind("removed",function(e){t.trigger("change",e)})},count:function(){return _.size(this._value)},add:function(e,t){var n,i=this,t="string"==typeof e?(n=e,t):(n=e.code,e);return i.has(n)||(i._addedIncrement+=1,i._addedOrder[n]=i._addedIncrement),Y.Values.prototype.add.call(i,n,t)},remove:function(e){return delete this._addedOrder[e],Y.Values.prototype.remove.call(this,e)},get:function(e){var a,o=this,t=_.values(o._value);return _.extend({sort:!1},e).sort&&(a={error:4,warning:3,success:2,info:1},t.sort(function(e,t){var n=0,i=0;return(n=_.isUndefined(a[e.type])?n:a[e.type])!==(i=_.isUndefined(a[t.type])?i:a[t.type])?i-n:o._addedOrder[t.code]-o._addedOrder[e.code]})),t},render:function(){var e,t,n,i=this,a=!1,o=[],s={};i.container&&i.container.length&&(e=i.get({sort:!0}),i.container.toggle(0!==e.length),i.container.is(i.previousContainer)&&_.isEqual(e,i.previousNotifications)||((n=i.container.children("ul").first()).length||(n=J("<ul></ul>"),i.container.append(n)),n.find("> [data-code]").remove(),_.each(i.previousNotifications,function(e){s[e.code]=e}),_.each(e,function(e){var t;!wp.a11y||s[e.code]&&_.isEqual(e.message,s[e.code].message)||wp.a11y.speak(e.message,"assertive"),t=J(e.render()),e.container=t,n.append(t),e.extended(Y.OverlayNotification)&&o.push(e)}),(t=Boolean(o.length))!==(a=i.previousNotifications?Boolean(_.find(i.previousNotifications,function(e){return e.extended(Y.OverlayNotification)})):a)&&(J(document.body).toggleClass("customize-loading",t),i.container.toggleClass("has-overlay-notifications",t),t?(i.previousActiveElement=document.activeElement,J(document).on("keydown",i.constrainFocus)):J(document).off("keydown",i.constrainFocus)),t?(i.focusContainer=o[o.length-1].container,i.focusContainer.prop("tabIndex",-1),((a=i.focusContainer.find(":focusable")).length?a.first():i.focusContainer).focus()):i.previousActiveElement&&(J(i.previousActiveElement).trigger("focus"),i.previousActiveElement=null),i.previousNotifications=e,i.previousContainer=i.container,i.trigger("rendered")))},constrainFocus:function(e){var t,n=this;e.stopPropagation(),9===e.which&&(0===(t=n.focusContainer.find(":focusable")).length&&(t=n.focusContainer),!J.contains(n.focusContainer[0],e.target)||!J.contains(n.focusContainer[0],document.activeElement)||t.last().is(e.target)&&!e.shiftKey?(e.preventDefault(),t.first().focus()):t.first().is(e.target)&&e.shiftKey&&(e.preventDefault(),t.last().focus()))}}),Y.Setting=Y.Value.extend({defaults:{transport:"refresh",dirty:!1},initialize:function(e,t,n){var i=this,n=_.extend({previewer:Y.previewer},i.defaults,n||{});Y.Value.prototype.initialize.call(i,t,n),i.id=e,i._dirty=n.dirty,i.notifications=new Y.Notifications,i.bind(i.preview)},preview:function(){var e=this,t=e.transport;"postMessage"===(t="postMessage"!==t||Y.state("previewerAlive").get()?t:"refresh")?e.previewer.send("setting",[e.id,e()]):"refresh"===t&&e.previewer.refresh()},findControls:function(){var n=this,i=[];return Y.control.each(function(t){_.each(t.settings,function(e){e.id===n.id&&i.push(t)})}),i}}),Y._latestRevision=0,Y._lastSavedRevision=0,Y._latestSettingRevisions={},Y.bind("change",function(e){Y._latestRevision+=1,Y._latestSettingRevisions[e.id]=Y._latestRevision}),Y.bind("ready",function(){Y.bind("add",function(e){e._dirty&&(Y._latestRevision+=1,Y._latestSettingRevisions[e.id]=Y._latestRevision)})}),Y.dirtyValues=function(n){var i={};return Y.each(function(e){var t;e._dirty&&(t=Y._latestSettingRevisions[e.id],Y.state("changesetStatus").get()&&n&&n.unsaved&&(_.isUndefined(t)||t<=Y._lastSavedRevision)||(i[e.id]=e.get()))}),i},Y.requestChangesetUpdate=function(n,e){var t,i={},a=new J.Deferred;if(0!==Y.state("processing").get())a.reject("already_processing");else if(e=_.extend({title:null,date:null,autosave:!1,force:!1},e),n&&_.extend(i,n),_.each(Y.dirtyValues({unsaved:!0}),function(e,t){n&&null===n[t]||(i[t]=_.extend({},i[t]||{},{value:e}))}),Y.trigger("changeset-save",i,e),!e.force&&_.isEmpty(i)&&null===e.title&&null===e.date)a.resolve({});else{if(e.status)return a.reject({code:"illegal_status_in_changeset_update"}).promise();if(e.date&&e.autosave)return a.reject({code:"illegal_autosave_with_date_gmt"}).promise();Y.state("processing").set(Y.state("processing").get()+1),a.always(function(){Y.state("processing").set(Y.state("processing").get()-1)}),delete(t=Y.previewer.query({excludeCustomizedSaved:!0})).customized,_.extend(t,{nonce:Y.settings.nonce.save,customize_theme:Y.settings.theme.stylesheet,customize_changeset_data:JSON.stringify(i)}),null!==e.title&&(t.customize_changeset_title=e.title),null!==e.date&&(t.customize_changeset_date=e.date),!1!==e.autosave&&(t.customize_changeset_autosave="true"),Y.trigger("save-request-params",t),(e=wp.ajax.post("customize_save",t)).done(function(e){var n={};Y._lastSavedRevision=Math.max(Y._latestRevision,Y._lastSavedRevision),Y.state("changesetStatus").set(e.changeset_status),e.changeset_date&&Y.state("changesetDate").set(e.changeset_date),a.resolve(e),Y.trigger("changeset-saved",e),e.setting_validities&&_.each(e.setting_validities,function(e,t){!0===e&&_.isObject(i[t])&&!_.isUndefined(i[t].value)&&(n[t]=i[t].value)}),Y.previewer.send("changeset-saved",_.extend({},e,{saved_changeset_values:n}))}),e.fail(function(e){a.reject(e),Y.trigger("changeset-error",e)}),e.always(function(e){e.setting_validities&&Y._handleSettingValidities({settingValidities:e.setting_validities})})}return a.promise()},Y.utils.bubbleChildValueChanges=function(n,e){J.each(e,function(e,t){n[t].bind(function(e,t){n.parent&&e!==t&&n.parent.trigger("change",n)})})},o=function(e){var t,n,i=this,a=function(){var e;i.extended(Y.Panel)&&1<(n=i.sections()).length&&n.forEach(function(e){e.expanded()&&e.collapse()}),e=(i.extended(Y.Panel)||i.extended(Y.Section))&&i.expanded&&i.expanded()?i.contentContainer:i.container,(n=0===(n=e.find(".control-focus:first")).length?e.find("input, select, textarea, button, object, a[href], [tabindex]").filter(":visible").first():n).focus()};(e=e||{}).completeCallback?(t=e.completeCallback,e.completeCallback=function(){a(),t()}):e.completeCallback=a,Y.state("paneVisible").set(!0),i.expand?i.expand(e):e.completeCallback()},Y.utils.prioritySort=function(e,t){return e.priority()===t.priority()&&"number"==typeof e.params.instanceNumber&&"number"==typeof t.params.instanceNumber?e.params.instanceNumber-t.params.instanceNumber:e.priority()-t.priority()},Y.utils.isKeydownButNotEnterEvent=function(e){return"keydown"===e.type&&13!==e.which},Y.utils.areElementListsEqual=function(e,t){return e.length===t.length&&-1===_.indexOf(_.map(_.zip(e,t),function(e){return J(e[0]).is(e[1])}),!1)},Y.utils.highlightButton=function(e,t){var n,i="button-see-me",a=!1;function o(){a=!0}return(n=_.extend({delay:0,focusTarget:e},t)).focusTarget.on("focusin",o),setTimeout(function(){n.focusTarget.off("focusin",o),a||(e.addClass(i),e.one("animationend",function(){e.removeClass(i)}))},n.delay),o},Y.utils.getCurrentTimestamp=function(){var e=_.now(),t=new Date(Y.settings.initialServerDate.replace(/-/g,"/")),e=e-Y.settings.initialClientTimestamp;return e+=Y.settings.initialClientTimestamp-Y.settings.initialServerTimestamp,t.setTime(t.getTime()+e),t.getTime()},Y.utils.getRemainingTime=function(e){e=e instanceof Date?e.getTime():"string"==typeof e?new Date(e.replace(/-/g,"/")).getTime():e,e-=Y.utils.getCurrentTimestamp();return Math.ceil(e/1e3)},t=document.createElement("div"),e={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"},n=_.find(_.keys(e),function(e){return!_.isUndefined(t.style[e])}),s=n?e[n]:null,a=Y.Class.extend({defaultActiveArguments:{duration:"fast",completeCallback:J.noop},defaultExpandedArguments:{duration:"fast",completeCallback:J.noop},containerType:"container",defaults:{title:"",description:"",priority:100,type:"default",content:null,active:!0,instanceNumber:null},initialize:function(e,t){var n=this;n.id=e,a.instanceCounter||(a.instanceCounter=0),a.instanceCounter++,J.extend(n,{params:_.defaults(t.params||t,n.defaults)}),n.params.instanceNumber||(n.params.instanceNumber=a.instanceCounter),n.notifications=new Y.Notifications,n.templateSelector=n.params.templateId||"customize-"+n.containerType+"-"+n.params.type,n.container=J(n.params.content),0===n.container.length&&(n.container=J(n.getContainer())),n.headContainer=n.container,n.contentContainer=n.getContent(),n.container=n.container.add(n.contentContainer),n.deferred={embedded:new J.Deferred},n.priority=new Y.Value,n.active=new Y.Value,n.activeArgumentsQueue=[],n.expanded=new Y.Value,n.expandedArgumentsQueue=[],n.active.bind(function(e){var t=n.activeArgumentsQueue.shift(),t=J.extend({},n.defaultActiveArguments,t);e=e&&n.isContextuallyActive(),n.onChangeActive(e,t)}),n.expanded.bind(function(e){var t=n.expandedArgumentsQueue.shift(),t=J.extend({},n.defaultExpandedArguments,t);n.onChangeExpanded(e,t)}),n.deferred.embedded.done(function(){n.setupNotifications(),n.attachEvents()}),Y.utils.bubbleChildValueChanges(n,["priority","active"]),n.priority.set(n.params.priority),n.active.set(n.params.active),n.expanded.set(!1)},getNotificationsContainerElement:function(){return this.contentContainer.find(".customize-control-notifications-container:first")},setupNotifications:function(){var e,t=this;t.notifications.container=t.getNotificationsContainerElement(),t.expanded.bind(e=function(){t.expanded.get()&&t.notifications.render()}),e(),t.notifications.bind("change",_.debounce(e))},ready:function(){},_children:function(t,e){var n=this,i=[];return Y[e].each(function(e){e[t].get()===n.id&&i.push(e)}),i.sort(Y.utils.prioritySort),i},isContextuallyActive:function(){throw new Error("Container.isContextuallyActive() must be overridden in a subclass.")},onChangeActive:function(e,t){var n,i=this,a=i.headContainer;t.unchanged?t.completeCallback&&t.completeCallback():(n="resolved"===Y.previewer.deferred.active.state()?t.duration:0,i.extended(Y.Panel)&&(Y.panel.each(function(e){e!==i&&e.expanded()&&(n=0)}),e||_.each(i.sections(),function(e){e.collapse({duration:0})})),J.contains(document,a.get(0))?e?a.slideDown(n,t.completeCallback):i.expanded()?i.collapse({duration:n,completeCallback:function(){a.slideUp(n,t.completeCallback)}}):a.slideUp(n,t.completeCallback):(a.toggle(e),t.completeCallback&&t.completeCallback()))},_toggleActive:function(e,t){return t=t||{},e&&this.active.get()||!e&&!this.active.get()?(t.unchanged=!0,this.onChangeActive(this.active.get(),t),!1):(t.unchanged=!1,this.activeArgumentsQueue.push(t),this.active.set(e),!0)},activate:function(e){return this._toggleActive(!0,e)},deactivate:function(e){return this._toggleActive(!1,e)},onChangeExpanded:function(){throw new Error("Must override with subclass.")},_toggleExpanded:function(e,t){var n,i=this;return n=(t=t||{}).completeCallback,!(e&&!i.active()||(Y.state("paneVisible").set(!0),t.completeCallback=function(){n&&n.apply(i,arguments),e?i.container.trigger("expanded"):i.container.trigger("collapsed")},e&&i.expanded.get()||!e&&!i.expanded.get()?(t.unchanged=!0,i.onChangeExpanded(i.expanded.get(),t),1):(t.unchanged=!1,i.expandedArgumentsQueue.push(t),i.expanded.set(e),0)))},expand:function(e){return this._toggleExpanded(!0,e)},collapse:function(e){return this._toggleExpanded(!1,e)},_animateChangeExpanded:function(t){var a,o,n,i;!s||r?_.defer(function(){t&&t()}):(o=(a=this).contentContainer,i=o.closest(".wp-full-overlay").add(o),a.panel&&""!==a.panel()&&!Y.panel(a.panel()).contentContainer.hasClass("skip-transition")||(i=i.add("#customize-info, .customize-pane-parent")),n=function(e){2===e.eventPhase&&J(e.target).is(o)&&(o.off(s,n),i.removeClass("busy"),t)&&t()},o.on(s,n),i.addClass("busy"),_.defer(function(){var e=o.closest(".wp-full-overlay-sidebar-content"),t=e.scrollTop(),n=o.data("previous-scrollTop")||0,i=a.expanded();i&&0<t?(o.css("top",t+"px"),o.data("previous-scrollTop",t)):!i&&0<t+n&&(o.css("top",n-t+"px"),e.scrollTop(n))}))},focus:o,getContainer:function(){var e=this,t=0!==J("#tmpl-"+e.templateSelector).length?wp.template(e.templateSelector):wp.template("customize-"+e.containerType+"-default");return t&&e.container?t(_.extend({id:e.id},e.params)).toString().trim():"<li></li>"},getContent:function(){var e=this.container,t=e.find(".accordion-section-content, .control-panel-content").first(),n="sub-"+e.attr("id"),i=n,a=e.attr("aria-owns");return e.attr("aria-owns",i=a?i+" "+a:i),t.detach().attr({id:n,class:"customize-pane-child "+t.attr("class")+" "+e.attr("class")})}}),Y.Section=a.extend({containerType:"section",containerParent:"#customize-theme-controls",containerPaneParent:".customize-pane-parent",defaults:{title:"",description:"",priority:100,type:"default",content:null,active:!0,instanceNumber:null,panel:null,customizeAction:""},initialize:function(e,t){var n=this,i=t.params||t;i.type||_.find(Y.sectionConstructor,function(e,t){return e===n.constructor&&(i.type=t,!0)}),a.prototype.initialize.call(n,e,i),n.id=e,n.panel=new Y.Value,n.panel.bind(function(e){J(n.headContainer).toggleClass("control-subsection",!!e)}),n.panel.set(n.params.panel||""),Y.utils.bubbleChildValueChanges(n,["panel"]),n.embed(),n.deferred.embedded.done(function(){n.ready()})},embed:function(){var e,n=this;n.containerParent=Y.ensure(n.containerParent),n.panel.bind(e=function(e){var t;e?Y.panel(e,function(e){e.deferred.embedded.done(function(){t=e.contentContainer,n.headContainer.parent().is(t)||t.append(n.headContainer),n.contentContainer.parent().is(n.headContainer)||n.containerParent.append(n.contentContainer),n.deferred.embedded.resolve()})}):(t=Y.ensure(n.containerPaneParent),n.headContainer.parent().is(t)||t.append(n.headContainer),n.contentContainer.parent().is(n.headContainer)||n.containerParent.append(n.contentContainer),n.deferred.embedded.resolve())}),e(n.panel.get())},attachEvents:function(){var e,t,n=this;n.container.hasClass("cannot-expand")||(n.container.find(".accordion-section-title button, .customize-section-back, .accordion-section-title[tabindex]").on("click keydown",function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.expanded()?n.collapse():n.expand())}),n.container.find(".customize-section-title .customize-help-toggle").on("click",function(){(e=n.container.find(".section-meta")).hasClass("cannot-expand")||((t=e.find(".customize-section-description:first")).toggleClass("open"),t.slideToggle(n.defaultExpandedArguments.duration,function(){t.trigger("toggled")}),J(this).attr("aria-expanded",function(e,t){return"true"===t?"false":"true"}))}))},isContextuallyActive:function(){var e=this.controls(),t=0;return _(e).each(function(e){e.active()&&(t+=1)}),0!==t},controls:function(){return this._children("section","control")},onChangeExpanded:function(e,t){var n,i=this,a=i.headContainer.closest(".wp-full-overlay-sidebar-content"),o=i.contentContainer,s=i.headContainer.closest(".wp-full-overlay"),r=o.find(".customize-section-back"),c=i.headContainer.find(".accordion-section-title button, .accordion-section-title[tabindex]").first();e&&!o.hasClass("open")?(n=t.unchanged?t.completeCallback:function(){i._animateChangeExpanded(function(){r.trigger("focus"),o.css("top",""),a.scrollTop(0),t.completeCallback&&t.completeCallback()}),o.addClass("open"),s.addClass("section-open"),Y.state("expandedSection").set(i)}.bind(this),t.allowMultiple||Y.section.each(function(e){e!==i&&e.collapse({duration:t.duration})}),i.panel()?Y.panel(i.panel()).expand({duration:t.duration,completeCallback:n}):(t.allowMultiple||Y.panel.each(function(e){e.collapse()}),n())):!e&&o.hasClass("open")?(i.panel()&&(n=Y.panel(i.panel())).contentContainer.hasClass("skip-transition")&&n.collapse(),i._animateChangeExpanded(function(){c.trigger("focus"),o.css("top",""),t.completeCallback&&t.completeCallback()}),o.removeClass("open"),s.removeClass("section-open"),i===Y.state("expandedSection").get()&&Y.state("expandedSection").set(!1)):t.completeCallback&&t.completeCallback()}}),Y.ThemesSection=Y.Section.extend({currentTheme:"",overlay:"",template:"",screenshotQueue:null,$window:null,$body:null,loaded:0,loading:!1,fullyLoaded:!1,term:"",tags:"",nextTerm:"",nextTags:"",filtersHeight:0,headerContainer:null,updateCountDebounced:null,initialize:function(e,t){var n=this;n.headerContainer=J(),n.$window=J(window),n.$body=J(document.body),Y.Section.prototype.initialize.call(n,e,t),n.updateCountDebounced=_.debounce(n.updateCount,500)},embed:function(){var n=this,e=function(e){var t;Y.panel(e,function(e){e.deferred.embedded.done(function(){t=e.contentContainer,n.headContainer.parent().is(t)||t.find(".customize-themes-full-container-container").before(n.headContainer),n.contentContainer.parent().is(n.headContainer)||n.containerParent.append(n.contentContainer),n.deferred.embedded.resolve()})})};n.panel.bind(e),e(n.panel.get())},ready:function(){var t=this;t.overlay=t.container.find(".theme-overlay"),t.template=wp.template("customize-themes-details-view"),t.container.on("keydown",function(e){t.overlay.find(".theme-wrap").is(":visible")&&(39===e.keyCode&&t.nextTheme(),37===e.keyCode&&t.previousTheme(),27===e.keyCode)&&(t.$body.hasClass("modal-open")?t.closeDetails():t.headerContainer.find(".customize-themes-section-title").focus(),e.stopPropagation())}),t.renderScreenshots=_.throttle(t.renderScreenshots,100),_.bindAll(t,"renderScreenshots","loadMore","checkTerm","filtersChecked")},isContextuallyActive:function(){return this.active()},attachEvents:function(){var e,n=this;function t(){var e=n.headerContainer.find(".customize-themes-section-title");e.toggleClass("selected",n.expanded()),e.attr("aria-expanded",n.expanded()?"true":"false"),n.expanded()||e.removeClass("details-open")}n.container.find(".customize-section-back").on("click keydown",function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.collapse())}),n.headerContainer=J("#accordion-section-"+n.id),n.headerContainer.on("click",".customize-themes-section-title",function(){n.headerContainer.find(".filter-details").length&&(n.headerContainer.find(".customize-themes-section-title").toggleClass("details-open").attr("aria-expanded",function(e,t){return"true"===t?"false":"true"}),n.headerContainer.find(".filter-details").slideToggle(180)),n.expanded()||n.expand()}),n.container.on("click",".theme-actions .preview-theme",function(){Y.panel("themes").loadThemePreview(J(this).data("slug"))}),n.container.on("click",".left",function(){n.previousTheme()}),n.container.on("click",".right",function(){n.nextTheme()}),n.container.on("click",".theme-backdrop, .close",function(){n.closeDetails()}),"local"===n.params.filter_type?n.container.on("input",".wp-filter-search-themes",function(e){n.filterSearch(e.currentTarget.value)}):"remote"===n.params.filter_type&&(e=_.debounce(n.checkTerm,500),n.contentContainer.on("input",".wp-filter-search",function(){Y.panel("themes").expanded()&&(e(n),n.expanded()||n.expand())}),n.contentContainer.on("click",".filter-group input",function(){n.filtersChecked(),n.checkTerm(n)})),n.contentContainer.on("click",".feature-filter-toggle",function(e){var t=J(".customize-themes-full-container"),e=J(e.currentTarget);n.filtersHeight=e.parents(".themes-filter-bar").next(".filter-drawer").height(),0<t.scrollTop()&&(t.animate({scrollTop:0},400),e.hasClass("open"))||(e.toggleClass("open").attr("aria-expanded",function(e,t){return"true"===t?"false":"true"}).parents(".themes-filter-bar").next(".filter-drawer").slideToggle(180,"linear"),e.hasClass("open")?(t=1018<window.innerWidth?50:76,n.contentContainer.find(".themes").css("margin-top",n.filtersHeight+t)):n.contentContainer.find(".themes").css("margin-top",0))}),n.contentContainer.on("click",".no-themes-local .search-dotorg-themes",function(){Y.section("wporg_themes").focus()}),n.expanded.bind(t),t(),Y.bind("ready",function(){n.contentContainer=n.container.find(".customize-themes-section"),n.contentContainer.appendTo(J(".customize-themes-full-container")),n.container.add(n.headerContainer)})},onChangeExpanded:function(e,n){var i=this,t=i.contentContainer.closest(".customize-themes-full-container");function a(){0===i.loaded&&i.loadThemes(),Y.section.each(function(e){var t;e!==i&&"themes"===e.params.type&&(t=e.contentContainer.find(".wp-filter-search").val(),i.contentContainer.find(".wp-filter-search").val(t),""===t&&""!==i.term&&"local"!==i.params.filter_type?(i.term="",i.initializeNewQuery(i.term,i.tags)):"remote"===i.params.filter_type?i.checkTerm(i):"local"===i.params.filter_type&&i.filterSearch(t),e.collapse({duration:n.duration}))}),i.contentContainer.addClass("current-section"),t.scrollTop(),t.on("scroll",_.throttle(i.renderScreenshots,300)),t.on("scroll",_.throttle(i.loadMore,300)),n.completeCallback&&n.completeCallback(),i.updateCount()}n.unchanged?n.completeCallback&&n.completeCallback():e?i.panel()&&Y.panel.has(i.panel())?Y.panel(i.panel()).expand({duration:n.duration,completeCallback:a}):a():(i.contentContainer.removeClass("current-section"),i.headerContainer.find(".filter-details").slideUp(180),t.off("scroll"),n.completeCallback&&n.completeCallback())},getContent:function(){return this.container.find(".control-section-content")},loadThemes:function(){var n,e,i=this;i.loading||(n=Math.ceil(i.loaded/100)+1,e={nonce:Y.settings.nonce.switch_themes,wp_customize:"on",theme_action:i.params.action,customized_theme:Y.settings.theme.stylesheet,page:n},"remote"===i.params.filter_type&&(e.search=i.term,e.tags=i.tags),i.headContainer.closest(".wp-full-overlay").addClass("loading"),i.loading=!0,i.container.find(".no-themes").hide(),(e=wp.ajax.post("customize_load_themes",e)).done(function(e){var t=e.themes;""!==i.nextTerm||""!==i.nextTags?(i.nextTerm&&(i.term=i.nextTerm),i.nextTags&&(i.tags=i.nextTags),i.nextTerm="",i.nextTags="",i.loading=!1,i.loadThemes()):(0!==t.length?(i.loadControls(t,n),1===n&&(_.each(i.controls().slice(0,3),function(e){e=e.params.theme.screenshot[0];e&&((new Image).src=e)}),"local"!==i.params.filter_type)&&wp.a11y.speak(Y.settings.l10n.themeSearchResults.replace("%d",e.info.results)),_.delay(i.renderScreenshots,100),("local"===i.params.filter_type||t.length<100)&&(i.fullyLoaded=!0)):0===i.loaded?(i.container.find(".no-themes").show(),wp.a11y.speak(i.container.find(".no-themes").text())):i.fullyLoaded=!0,"local"===i.params.filter_type?i.updateCount():i.updateCount(e.info.results),i.container.find(".unexpected-error").hide(),i.headContainer.closest(".wp-full-overlay").removeClass("loading"),i.loading=!1)}),e.fail(function(e){void 0===e?(i.container.find(".unexpected-error").show(),wp.a11y.speak(i.container.find(".unexpected-error").text())):"undefined"!=typeof console&&console.error&&console.error(e),i.headContainer.closest(".wp-full-overlay").removeClass("loading"),i.loading=!1}))},loadControls:function(e,t){var n=[],i=this;_.each(e,function(e){e=new Y.controlConstructor.theme(i.params.action+"_theme_"+e.id,{type:"theme",section:i.params.id,theme:e,priority:i.loaded+1});Y.control.add(e),n.push(e),i.loaded=i.loaded+1}),1!==t&&Array.prototype.push.apply(i.screenshotQueue,n)},loadMore:function(){var e,t;this.fullyLoaded||this.loading||(t=(e=this.container.closest(".customize-themes-full-container")).scrollTop()+e.height(),e.prop("scrollHeight")-3e3<t&&this.loadThemes())},filterSearch:function(e){var t,n=0,i=this,a=Y.section.has("wporg_themes")&&"remote"!==i.params.filter_type?".no-themes-local":".no-themes",o=i.controls();i.loading||(t=e.toLowerCase().trim().replace(/-/g," ").split(" "),_.each(o,function(e){e.filter(t)&&(n+=1)}),0===n?(i.container.find(a).show(),wp.a11y.speak(i.container.find(a).text())):i.container.find(a).hide(),i.renderScreenshots(),Y.reflowPaneContents(),i.updateCountDebounced(n))},checkTerm:function(e){var t;"remote"===e.params.filter_type&&(t=e.contentContainer.find(".wp-filter-search").val(),e.term!==t.trim())&&e.initializeNewQuery(t,e.tags)},filtersChecked:function(){var e=this,t=e.container.find(".filter-group").find(":checkbox"),n=[];_.each(t.filter(":checked"),function(e){n.push(J(e).prop("value"))}),0===n.length?(n="",e.contentContainer.find(".feature-filter-toggle .filter-count-0").show(),e.contentContainer.find(".feature-filter-toggle .filter-count-filters").hide()):(e.contentContainer.find(".feature-filter-toggle .theme-filter-count").text(n.length),e.contentContainer.find(".feature-filter-toggle .filter-count-0").hide(),e.contentContainer.find(".feature-filter-toggle .filter-count-filters").show()),_.isEqual(e.tags,n)||(e.loading?e.nextTags=n:"remote"===e.params.filter_type?e.initializeNewQuery(e.term,n):"local"===e.params.filter_type&&e.filterSearch(n.join(" ")))},initializeNewQuery:function(e,t){var n=this;_.each(n.controls(),function(e){e.container.remove(),Y.control.remove(e.id)}),n.loaded=0,n.fullyLoaded=!1,n.screenshotQueue=null,n.loading?(n.nextTerm=e,n.nextTags=t):(n.term=e,n.tags=t,n.loadThemes()),n.expanded()||n.expand()},renderScreenshots:function(){var o=this;null!==o.screenshotQueue&&0!==o.screenshotQueue.length||(o.screenshotQueue=_.filter(o.controls(),function(e){return!e.screenshotRendered})),o.screenshotQueue.length&&(o.screenshotQueue=_.filter(o.screenshotQueue,function(e){var t,n,i=e.container.find(".theme-screenshot"),a=i.find("img");return!(!a.length||!a.is(":hidden")&&(t=(n=o.$window.scrollTop())+o.$window.height(),a=a.offset().top,(n=n-(i=3*(n=i.height()))<=a+n&&a<=t+i)&&e.container.trigger("render-screenshot"),n))}))},getVisibleCount:function(){return this.contentContainer.find("li.customize-control:visible").length},updateCount:function(e){var t,n;e||0===e||(e=this.getVisibleCount()),n=this.contentContainer.find(".themes-displayed"),t=this.contentContainer.find(".theme-count"),0===e?t.text("0"):(n.fadeOut(180,function(){t.text(e),n.fadeIn(180)}),wp.a11y.speak(Y.settings.l10n.announceThemeCount.replace("%d",e)))},nextTheme:function(){var e=this;e.getNextTheme()&&e.showDetails(e.getNextTheme(),function(){e.overlay.find(".right").focus()})},getNextTheme:function(){var e=Y.control(this.params.action+"_theme_"+this.currentTheme),t=this.controls(),e=_.indexOf(t,e);return-1!==e&&!!(t=t[e+1])&&t.params.theme},previousTheme:function(){var e=this;e.getPreviousTheme()&&e.showDetails(e.getPreviousTheme(),function(){e.overlay.find(".left").focus()})},getPreviousTheme:function(){var e=Y.control(this.params.action+"_theme_"+this.currentTheme),t=this.controls(),e=_.indexOf(t,e);return-1!==e&&!!(t=t[e-1])&&t.params.theme},updateLimits:function(){this.getNextTheme()||this.overlay.find(".right").addClass("disabled"),this.getPreviousTheme()||this.overlay.find(".left").addClass("disabled")},loadThemePreview:function(e){return Y.ThemesPanel.prototype.loadThemePreview.call(this,e)},showDetails:function(e,t){var n=this,i=Y.panel("themes");function a(){return!i.canSwitchTheme(e.id)}n.currentTheme=e.id,n.overlay.html(n.template(e)).fadeIn("fast").focus(),n.overlay.find("button.preview, button.preview-theme").toggleClass("disabled",a()),n.overlay.find("button.theme-install").toggleClass("disabled",a()||!1===Y.settings.theme._canInstall||!0===Y.settings.theme._filesystemCredentialsNeeded),n.$body.addClass("modal-open"),n.containFocus(n.overlay),n.updateLimits(),wp.a11y.speak(Y.settings.l10n.announceThemeDetails.replace("%s",e.name)),t&&t()},closeDetails:function(){this.$body.removeClass("modal-open"),this.overlay.fadeOut("fast"),Y.control(this.params.action+"_theme_"+this.currentTheme).container.find(".theme").focus()},containFocus:function(t){var n;t.on("keydown",function(e){if(9===e.keyCode)return(n=J(":tabbable",t)).last()[0]!==e.target||e.shiftKey?n.first()[0]===e.target&&e.shiftKey?(n.last().focus(),!1):void 0:(n.first().focus(),!1)})}}),Y.OuterSection=Y.Section.extend({initialize:function(){this.containerParent="#customize-outer-theme-controls",this.containerPaneParent=".customize-outer-pane-parent",Y.Section.prototype.initialize.apply(this,arguments)},onChangeExpanded:function(e,t){var n,i=this,a=i.headContainer.closest(".wp-full-overlay-sidebar-content"),o=i.contentContainer,s=o.find(".customize-section-back"),r=i.headContainer.find(".accordion-section-title button, .accordion-section-title[tabindex]").first();J(document.body).toggleClass("outer-section-open",e),i.container.toggleClass("open",e),i.container.removeClass("busy"),Y.section.each(function(e){"outer"===e.params.type&&e.id!==i.id&&e.container.removeClass("open")}),e&&!o.hasClass("open")?(n=t.unchanged?t.completeCallback:function(){i._animateChangeExpanded(function(){s.trigger("focus"),o.css("top",""),a.scrollTop(0),t.completeCallback&&t.completeCallback()}),o.addClass("open")}.bind(this),i.panel()?Y.panel(i.panel()).expand({duration:t.duration,completeCallback:n}):n()):!e&&o.hasClass("open")?(i.panel()&&(n=Y.panel(i.panel())).contentContainer.hasClass("skip-transition")&&n.collapse(),i._animateChangeExpanded(function(){r.trigger("focus"),o.css("top",""),t.completeCallback&&t.completeCallback()}),o.removeClass("open")):t.completeCallback&&t.completeCallback()}}),Y.Panel=a.extend({containerType:"panel",initialize:function(e,t){var n=this,i=t.params||t;i.type||_.find(Y.panelConstructor,function(e,t){return e===n.constructor&&(i.type=t,!0)}),a.prototype.initialize.call(n,e,i),n.embed(),n.deferred.embedded.done(function(){n.ready()})},embed:function(){var e=this,t=J("#customize-theme-controls"),n=J(".customize-pane-parent");e.headContainer.parent().is(n)||n.append(e.headContainer),e.contentContainer.parent().is(e.headContainer)||t.append(e.contentContainer),e.renderContent(),e.deferred.embedded.resolve()},attachEvents:function(){var t,n=this;n.headContainer.find(".accordion-section-title button, .accordion-section-title[tabindex]").on("click keydown",function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.expanded())||n.expand()}),n.container.find(".customize-panel-back").on("click keydown",function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.expanded()&&n.collapse())}),(t=n.container.find(".panel-meta:first")).find("> .accordion-section-title .customize-help-toggle").on("click",function(){var e;t.hasClass("cannot-expand")||(e=t.find(".customize-panel-description:first"),t.hasClass("open")?(t.toggleClass("open"),e.slideUp(n.defaultExpandedArguments.duration,function(){e.trigger("toggled")}),J(this).attr("aria-expanded",!1)):(e.slideDown(n.defaultExpandedArguments.duration,function(){e.trigger("toggled")}),t.toggleClass("open"),J(this).attr("aria-expanded",!0)))})},sections:function(){return this._children("panel","section")},isContextuallyActive:function(){var e=this.sections(),t=0;return _(e).each(function(e){e.active()&&e.isContextuallyActive()&&(t+=1)}),0!==t},onChangeExpanded:function(e,t){var n,i,a,o,s,r,c;t.unchanged?t.completeCallback&&t.completeCallback():(a=(i=(n=this).contentContainer).closest(".wp-full-overlay"),o=i.closest(".wp-full-overlay-sidebar-content"),s=n.headContainer.find(".accordion-section-title button, .accordion-section-title[tabindex]"),r=i.find(".customize-panel-back"),c=n.sections(),e&&!i.hasClass("current-panel")?(Y.section.each(function(e){n.id!==e.panel()&&e.collapse({duration:0})}),Y.panel.each(function(e){n!==e&&e.collapse({duration:0})}),n.params.autoExpandSoleSection&&1===c.length&&c[0].active.get()?(i.addClass("current-panel skip-transition"),a.addClass("in-sub-panel"),c[0].expand({completeCallback:t.completeCallback})):(n._animateChangeExpanded(function(){r.trigger("focus"),i.css("top",""),o.scrollTop(0),t.completeCallback&&t.completeCallback()}),i.addClass("current-panel"),a.addClass("in-sub-panel")),Y.state("expandedPanel").set(n)):!e&&i.hasClass("current-panel")&&(i.hasClass("skip-transition")?i.removeClass("skip-transition"):n._animateChangeExpanded(function(){s.focus(),i.css("top",""),t.completeCallback&&t.completeCallback()}),a.removeClass("in-sub-panel"),i.removeClass("current-panel"),n===Y.state("expandedPanel").get())&&Y.state("expandedPanel").set(!1))},renderContent:function(){var e=this,t=0!==J("#tmpl-"+e.templateSelector+"-content").length?wp.template(e.templateSelector+"-content"):wp.template("customize-panel-default-content");t&&e.headContainer&&e.contentContainer.html(t(_.extend({id:e.id},e.params)))}}),Y.ThemesPanel=Y.Panel.extend({initialize:function(e,t){this.installingThemes=[],Y.Panel.prototype.initialize.call(this,e,t)},canSwitchTheme:function(e){return!(!e||e!==Y.settings.theme.stylesheet)||"publish"===Y.state("selectedChangesetStatus").get()&&(""===Y.state("changesetStatus").get()||"auto-draft"===Y.state("changesetStatus").get())},attachEvents:function(){var t=this;function e(){t.canSwitchTheme()?t.notifications.remove("theme_switch_unavailable"):t.notifications.add(new Y.Notification("theme_switch_unavailable",{message:Y.l10n.themePreviewUnavailable,type:"warning"}))}Y.Panel.prototype.attachEvents.apply(t),Y.settings.theme._canInstall&&Y.settings.theme._filesystemCredentialsNeeded&&t.notifications.add(new Y.Notification("theme_install_unavailable",{message:Y.l10n.themeInstallUnavailable,type:"info",dismissible:!0})),e(),Y.state("selectedChangesetStatus").bind(e),Y.state("changesetStatus").bind(e),t.contentContainer.on("click",".customize-theme",function(){t.collapse()}),t.contentContainer.on("click",".customize-themes-section-title, .customize-themes-mobile-back",function(){J(".wp-full-overlay").toggleClass("showing-themes")}),t.contentContainer.on("click",".theme-install",function(e){t.installTheme(e)}),t.contentContainer.on("click",".update-theme, #update-theme",function(e){e.preventDefault(),e.stopPropagation(),t.updateTheme(e)}),t.contentContainer.on("click",".delete-theme",function(e){t.deleteTheme(e)}),_.bindAll(t,"installTheme","updateTheme")},onChangeExpanded:function(e,t){var n,i=!1;Y.Panel.prototype.onChangeExpanded.apply(this,[e,t]),t.unchanged?t.completeCallback&&t.completeCallback():(n=this.headContainer.closest(".wp-full-overlay"),e?(n.addClass("in-themes-panel").delay(200).find(".customize-themes-full-container").addClass("animate"),_.delay(function(){n.addClass("themes-panel-expanded")},200),600<window.innerWidth&&(t=this.sections(),_.each(t,function(e){e.expanded()&&(i=!0)}),!i)&&0<t.length&&t[0].expand()):n.removeClass("in-themes-panel themes-panel-expanded").find(".customize-themes-full-container").removeClass("animate"))},installTheme:function(e){var t,i=this,a=J(e.target).data("slug"),o=J.Deferred(),s=J(e.target).hasClass("preview");return Y.settings.theme._filesystemCredentialsNeeded?o.reject({errorCode:"theme_install_unavailable"}):i.canSwitchTheme(a)?_.contains(i.installingThemes,a)?o.reject({errorCode:"theme_already_installing"}):(wp.updates.maybeRequestFilesystemCredentials(e),e=function(t){var e,n=!1;if(s)Y.notifications.remove("theme_installing"),i.loadThemePreview(a);else{if(Y.control.each(function(e){"theme"===e.params.type&&e.params.theme.id===t.slug&&(n=e.params.theme,e.rerenderAsInstalled(!0))}),!n||Y.control.has("installed_theme_"+n.id))return void o.resolve(t);n.type="installed",e=new Y.controlConstructor.theme("installed_theme_"+n.id,{type:"theme",section:"installed_themes",theme:n,priority:0}),Y.control.add(e),Y.control(e.id).container.trigger("render-screenshot"),Y.section.each(function(e){"themes"===e.params.type&&n.id===e.currentTheme&&e.closeDetails()})}o.resolve(t)},i.installingThemes.push(a),t=wp.updates.installTheme({slug:a}),s&&Y.notifications.add(new Y.OverlayNotification("theme_installing",{message:Y.l10n.themeDownloading,type:"info",loading:!0})),t.done(e),t.fail(function(){Y.notifications.remove("theme_installing")})):o.reject({errorCode:"theme_switch_unavailable"}),o.promise()},loadThemePreview:function(e){var t,n,i=J.Deferred();return this.canSwitchTheme(e)?((n=document.createElement("a")).href=location.href,e=_.extend(Y.utils.parseQueryString(n.search.substr(1)),{theme:e,changeset_uuid:Y.settings.changeset.uuid,return:Y.settings.url.return}),Y.state("saved").get()||(e.customize_autosaved="on"),n.search=J.param(e),Y.notifications.add(new Y.OverlayNotification("theme_previewing",{message:Y.l10n.themePreviewWait,type:"info",loading:!0})),t=function(){var e;0<Y.state("processing").get()||(Y.state("processing").unbind(t),(e=Y.requestChangesetUpdate({},{autosave:!0})).done(function(){i.resolve(),J(window).off("beforeunload.customize-confirm"),location.replace(n.href)}),e.fail(function(){Y.notifications.remove("theme_previewing"),i.reject()}))},0===Y.state("processing").get()?t():Y.state("processing").bind(t)):i.reject({errorCode:"theme_switch_unavailable"}),i.promise()},updateTheme:function(e){wp.updates.maybeRequestFilesystemCredentials(e),J(document).one("wp-theme-update-success",function(e,t){Y.control.each(function(e){"theme"===e.params.type&&e.params.theme.id===t.slug&&(e.params.theme.hasUpdate=!1,e.params.theme.version=t.newVersion,setTimeout(function(){e.rerenderAsInstalled(!0)},2e3))})}),wp.updates.updateTheme({slug:J(e.target).closest(".notice").data("slug")})},deleteTheme:function(e){var t=J(e.target).data("slug"),n=Y.section("installed_themes");e.preventDefault(),Y.settings.theme._filesystemCredentialsNeeded||window.confirm(Y.settings.l10n.confirmDeleteTheme)&&(wp.updates.maybeRequestFilesystemCredentials(e),J(document).one("wp-theme-delete-success",function(){var e=Y.control("installed_theme_"+t);e.container.remove(),Y.control.remove(e.id),n.loaded=n.loaded-1,n.updateCount(),Y.control.each(function(e){"theme"===e.params.type&&e.params.theme.id===t&&e.rerenderAsInstalled(!1)})}),wp.updates.deleteTheme({slug:t}),n.closeDetails(),n.focus())}}),Y.Control=Y.Class.extend({defaultActiveArguments:{duration:"fast",completeCallback:J.noop},defaults:{label:"",description:"",active:!0,priority:10},initialize:function(e,t){var n,i=this,a=[];i.params=_.extend({},i.defaults,i.params||{},t.params||t||{}),Y.Control.instanceCounter||(Y.Control.instanceCounter=0),Y.Control.instanceCounter++,i.params.instanceNumber||(i.params.instanceNumber=Y.Control.instanceCounter),i.params.type||_.find(Y.controlConstructor,function(e,t){return e===i.constructor&&(i.params.type=t,!0)}),i.params.content||(i.params.content=J("<li></li>",{id:"customize-control-"+e.replace(/]/g,"").replace(/\[/g,"-"),class:"customize-control customize-control-"+i.params.type})),i.id=e,i.selector="#customize-control-"+e.replace(/\]/g,"").replace(/\[/g,"-"),i.params.content?i.container=J(i.params.content):i.container=J(i.selector),i.params.templateId?i.templateSelector=i.params.templateId:i.templateSelector="customize-control-"+i.params.type+"-content",i.deferred=_.extend(i.deferred||{},{embedded:new J.Deferred}),i.section=new Y.Value,i.priority=new Y.Value,i.active=new Y.Value,i.activeArgumentsQueue=[],i.notifications=new Y.Notifications({alt:i.altNotice}),i.elements=[],i.active.bind(function(e){var t=i.activeArgumentsQueue.shift(),t=J.extend({},i.defaultActiveArguments,t);i.onChangeActive(e,t)}),i.section.set(i.params.section),i.priority.set(isNaN(i.params.priority)?10:i.params.priority),i.active.set(i.params.active),Y.utils.bubbleChildValueChanges(i,["section","priority","active"]),i.settings={},n={},i.params.setting&&(n.default=i.params.setting),_.extend(n,i.params.settings),_.each(n,function(e,t){var n;_.isObject(e)&&_.isFunction(e.extended)&&e.extended(Y.Value)?i.settings[t]=e:_.isString(e)&&((n=Y(e))?i.settings[t]=n:a.push(e))}),t=function(){_.each(n,function(e,t){!i.settings[t]&&_.isString(e)&&(i.settings[t]=Y(e))}),i.settings[0]&&!i.settings.default&&(i.settings.default=i.settings[0]),i.setting=i.settings.default||null,i.linkElements(),i.embed()},0===a.length?t():Y.apply(Y,a.concat(t)),i.deferred.embedded.done(function(){i.linkElements(),i.setupNotifications(),i.ready()})},linkElements:function(){var i,a=this,o=a.container.find("[data-customize-setting-link], [data-customize-setting-key-link]"),s={};o.each(function(){var e,t,n=J(this);if(!n.data("customizeSettingLinked")){if(n.data("customizeSettingLinked",!0),n.is(":radio")){if(e=n.prop("name"),s[e])return;s[e]=!0,n=o.filter('[name="'+e+'"]')}n.data("customizeSettingLink")?t=Y(n.data("customizeSettingLink")):n.data("customizeSettingKeyLink")&&(t=a.settings[n.data("customizeSettingKeyLink")]),t&&(i=new Y.Element(n),a.elements.push(i),i.sync(t),i.set(t()))}})},embed:function(){var n=this,e=function(e){var t;e&&Y.section(e,function(e){e.deferred.embedded.done(function(){t=e.contentContainer.is("ul")?e.contentContainer:e.contentContainer.find("ul:first"),n.container.parent().is(t)||t.append(n.container),n.renderContent(),n.deferred.embedded.resolve()})})};n.section.bind(e),e(n.section.get())},ready:function(){var t,n=this;"dropdown-pages"===n.params.type&&n.params.allow_addition&&((t=n.container.find(".new-content-item-wrapper")).hide(),n.container.on("click",".add-new-toggle",function(e){J(e.currentTarget).slideUp(180),t.slideDown(180),t.find(".create-item-input").focus()}),n.container.on("click",".add-content",function(){n.addNewPage()}),n.container.on("keydown",".create-item-input",function(e){13===e.which&&n.addNewPage()}))},getNotificationsContainerElement:function(){var e,t=this,n=t.container.find(".customize-control-notifications-container:first");return n.length||(n=J('<div class="customize-control-notifications-container"></div>'),t.container.hasClass("customize-control-nav_menu_item")?t.container.find(".menu-item-settings:first").prepend(n):t.container.hasClass("customize-control-widget_form")?t.container.find(".widget-inside:first").prepend(n):(e=t.container.find(".customize-control-title")).length?e.after(n):t.container.prepend(n)),n},setupNotifications:function(){var n,e,i=this;_.each(i.settings,function(n){n.notifications&&(n.notifications.bind("add",function(e){var t=_.extend({},e,{setting:n.id});i.notifications.add(new Y.Notification(n.id+":"+e.code,t))}),n.notifications.bind("remove",function(e){i.notifications.remove(n.id+":"+e.code)}))}),n=function(){var e=i.section();(!e||Y.section.has(e)&&Y.section(e).expanded())&&i.notifications.render()},i.notifications.bind("rendered",function(){var e=i.notifications.get();i.container.toggleClass("has-notifications",0!==e.length),i.container.toggleClass("has-error",0!==_.where(e,{type:"error"}).length)}),i.section.bind(e=function(e,t){t&&Y.section.has(t)&&Y.section(t).expanded.unbind(n),e&&Y.section(e,function(e){e.expanded.bind(n),n()})}),e(i.section.get()),i.notifications.bind("change",_.debounce(n))},renderNotifications:function(){var e,t,n=this,i=!1;"undefined"!=typeof console&&console.warn&&console.warn("[DEPRECATED] wp.customize.Control.prototype.renderNotifications() is deprecated in favor of instantiating a wp.customize.Notifications and calling its render() method."),(e=n.getNotificationsContainerElement())&&e.length&&(t=[],n.notifications.each(function(e){t.push(e),"error"===e.type&&(i=!0)}),0===t.length?e.stop().slideUp("fast"):e.stop().slideDown("fast",null,function(){J(this).css("height","auto")}),n.notificationsTemplate||(n.notificationsTemplate=wp.template("customize-control-notifications")),n.container.toggleClass("has-notifications",0!==t.length),n.container.toggleClass("has-error",i),e.empty().append(n.notificationsTemplate({notifications:t,altNotice:Boolean(n.altNotice)}).trim()))},expand:function(e){Y.section(this.section()).expand(e)},focus:o,onChangeActive:function(e,t){t.unchanged?t.completeCallback&&t.completeCallback():J.contains(document,this.container[0])?e?this.container.slideDown(t.duration,t.completeCallback):this.container.slideUp(t.duration,t.completeCallback):(this.container.toggle(e),t.completeCallback&&t.completeCallback())},toggle:function(e){return this.onChangeActive(e,this.defaultActiveArguments)},activate:a.prototype.activate,deactivate:a.prototype.deactivate,_toggleActive:a.prototype._toggleActive,dropdownInit:function(){function e(e){"string"==typeof e&&i.statuses&&i.statuses[e]?n.html(i.statuses[e]).show():n.hide()}var t=this,n=this.container.find(".dropdown-status"),i=this.params,a=!1;this.container.on("click keydown",".dropdown",function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),a||t.container.toggleClass("open"),t.container.hasClass("open")&&t.container.parent().parent().find("li.library-selected").focus(),a=!0,setTimeout(function(){a=!1},400))}),this.setting.bind(e),e(this.setting())},renderContent:function(){var e=this,t=e.templateSelector;t==="customize-control-"+e.params.type+"-content"&&_.contains(["button","checkbox","date","datetime-local","email","month","number","password","radio","range","search","select","tel","time","text","textarea","week","url"],e.params.type)&&!document.getElementById("tmpl-"+t)&&0===e.container.children().length&&(t="customize-control-default-content"),document.getElementById("tmpl-"+t)&&(t=wp.template(t))&&e.container&&e.container.html(t(e.params)),e.notifications.container=e.getNotificationsContainerElement(),(!(t=e.section())||Y.section.has(t)&&Y.section(t).expanded())&&e.notifications.render()},addNewPage:function(){var e,a,o,t,s,r,c=this;"dropdown-pages"===c.params.type&&c.params.allow_addition&&Y.Menus&&(a=c.container.find(".add-new-toggle"),o=c.container.find(".new-content-item-wrapper"),t=c.container.find(".create-item-input"),s=t.val(),r=c.container.find("select"),s?(t.removeClass("invalid"),t.attr("disabled","disabled"),(e=Y.Menus.insertAutoDraftPost({post_title:s,post_type:"page"})).done(function(e){var t,n,i=new Y.Menus.AvailableItemModel({id:"post-"+e.post_id,title:s,type:"post_type",type_label:Y.Menus.data.l10n.page_label,object:"page",object_id:e.post_id,url:e.url});Y.Menus.availableMenuItemsPanel.collection.add(i),t=J("#available-menu-items-post_type-page").find(".available-menu-items-list"),n=wp.template("available-menu-item"),t.prepend(n(i.attributes)),r.focus(),c.setting.set(String(e.post_id)),o.slideUp(180),a.slideDown(180)}),e.always(function(){t.val("").removeAttr("disabled")})):t.addClass("invalid"))}}),Y.ColorControl=Y.Control.extend({ready:function(){var t,n=this,e="hue"===this.params.mode,i=!1;e?(t=this.container.find(".color-picker-hue")).val(n.setting()).wpColorPicker({change:function(e,t){i=!0,n.setting(t.color.h()),i=!1}}):(t=this.container.find(".color-picker-hex")).val(n.setting()).wpColorPicker({change:function(){i=!0,n.setting.set(t.wpColorPicker("color")),i=!1},clear:function(){i=!0,n.setting.set(""),i=!1}}),n.setting.bind(function(e){i||(t.val(e),t.wpColorPicker("color",e))}),n.container.on("keydown",function(e){27===e.which&&n.container.find(".wp-picker-container").hasClass("wp-picker-active")&&(t.wpColorPicker("close"),n.container.find(".wp-color-result").focus(),e.stopPropagation())})}}),Y.MediaControl=Y.Control.extend({ready:function(){var n=this;function e(e){var t=J.Deferred();n.extended(Y.UploadControl)?t.resolve():(e=parseInt(e,10),_.isNaN(e)||e<=0?(delete n.params.attachment,t.resolve()):n.params.attachment&&n.params.attachment.id===e&&t.resolve()),"pending"===t.state()&&wp.media.attachment(e).fetch().done(function(){n.params.attachment=this.attributes,t.resolve(),wp.customize.previewer.send(n.setting.id+"-attachment-data",this.attributes)}),t.done(function(){n.renderContent()})}_.bindAll(n,"restoreDefault","removeFile","openFrame","select","pausePlayer"),n.container.on("click keydown",".upload-button",n.openFrame),n.container.on("click keydown",".upload-button",n.pausePlayer),n.container.on("click keydown",".thumbnail-image img",n.openFrame),n.container.on("click keydown",".default-button",n.restoreDefault),n.container.on("click keydown",".remove-button",n.pausePlayer),n.container.on("click keydown",".remove-button",n.removeFile),n.container.on("click keydown",".remove-button",n.cleanupPlayer),Y.section(n.section()).container.on("expanded",function(){n.player&&n.player.setControlsSize()}).on("collapsed",function(){n.pausePlayer()}),e(n.setting()),n.setting.bind(e)},pausePlayer:function(){this.player&&this.player.pause()},cleanupPlayer:function(){this.player&&wp.media.mixin.removePlayer(this.player)},openFrame:function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.frame||this.initFrame(),this.frame.open())},initFrame:function(){this.frame=wp.media({button:{text:this.params.button_labels.frame_button},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:this.params.mime_type}),multiple:!1,date:!1})]}),this.frame.on("select",this.select)},select:function(){var e=this.frame.state().get("selection").first().toJSON(),t=window._wpmejsSettings||{};this.params.attachment=e,this.setting(e.id),(e=this.container.find("audio, video").get(0))?this.player=new MediaElementPlayer(e,t):this.cleanupPlayer()},restoreDefault:function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.params.attachment=this.params.defaultAttachment,this.setting(this.params.defaultAttachment.url))},removeFile:function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.params.attachment={},this.setting(""),this.renderContent())}}),Y.UploadControl=Y.MediaControl.extend({select:function(){var e=this.frame.state().get("selection").first().toJSON(),t=window._wpmejsSettings||{};this.params.attachment=e,this.setting(e.url),(e=this.container.find("audio, video").get(0))?this.player=new MediaElementPlayer(e,t):this.cleanupPlayer()},success:function(){},removerVisibility:function(){}}),Y.ImageControl=Y.UploadControl.extend({thumbnailSrc:function(){}}),Y.BackgroundControl=Y.UploadControl.extend({ready:function(){Y.UploadControl.prototype.ready.apply(this,arguments)},select:function(){Y.UploadControl.prototype.select.apply(this,arguments),wp.ajax.post("custom-background-add",{nonce:_wpCustomizeBackground.nonces.add,wp_customize:"on",customize_theme:Y.settings.theme.stylesheet,attachment_id:this.params.attachment.id})}}),Y.BackgroundPositionControl=Y.Control.extend({ready:function(){var e,n=this;n.container.on("change",'input[name="background-position"]',function(){var e=J(this).val().split(" ");n.settings.x(e[0]),n.settings.y(e[1])}),e=_.debounce(function(){var e=n.settings.x.get(),t=n.settings.y.get(),e=String(e)+" "+String(t);n.container.find('input[name="background-position"][value="'+e+'"]').trigger("click")}),n.settings.x.bind(e),n.settings.y.bind(e),e()}}),Y.CroppedImageControl=Y.MediaControl.extend({openFrame:function(e){Y.utils.isKeydownButNotEnterEvent(e)||(this.initFrame(),this.frame.setState("library").open())},initFrame:function(){var e=_wpMediaViewsL10n;this.frame=wp.media({button:{text:e.select,close:!1},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:this.params.width,suggestedHeight:this.params.height}),new wp.media.controller.CustomizeImageCropper({imgSelectOptions:this.calculateImageSelectOptions,control:this})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this)},onSelect:function(){var e=this.frame.state().get("selection").first().toJSON();this.params.width!==e.width||this.params.height!==e.height||this.params.flex_width||this.params.flex_height?this.frame.setState("cropper"):(this.setImageFromAttachment(e),this.frame.close())},onCropped:function(e){this.setImageFromAttachment(e)},calculateImageSelectOptions:function(e,t){var n=t.get("control"),i=!!parseInt(n.params.flex_width,10),a=!!parseInt(n.params.flex_height,10),o=e.get("width"),e=e.get("height"),s=parseInt(n.params.width,10),r=parseInt(n.params.height,10),c=s/r,l=o/e,d=s,u=r;return t.set("hasRequiredAspectRatio",n.hasRequiredAspectRatio(c,l)),t.set("suggestedCropSize",{width:o,height:e,x1:0,y1:0,x2:s,y2:r}),t.set("canSkipCrop",!n.mustBeCropped(i,a,s,r,o,e)),c<l?s=(r=e)*c:r=(s=o)/c,!(l={handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:o,imageHeight:e,minWidth:s<d?s:d,minHeight:r<u?r:u,x1:t=(o-s)/2,y1:n=(e-r)/2,x2:s+t,y2:r+n})==a&&!1==i&&(l.aspectRatio=s+":"+r),!0==a&&(delete l.minHeight,l.maxWidth=o),!0==i&&(delete l.minWidth,l.maxHeight=e),l},mustBeCropped:function(e,t,n,i,a,o){return(!0!==e||!0!==t)&&!(!0===e&&i===o||!0===t&&n===a||n===a&&i===o||a<=n)},hasRequiredAspectRatio:function(e,t){return Math.abs(e-t)<1e-6},onSkippedCrop:function(){var e=this.frame.state().get("selection").first().toJSON();this.setImageFromAttachment(e)},setImageFromAttachment:function(e){this.params.attachment=e,this.setting(e.id)}}),Y.SiteIconControl=Y.CroppedImageControl.extend({initFrame:function(){var e=_wpMediaViewsL10n;this.frame=wp.media({button:{text:e.select,close:!1},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:this.params.width,suggestedHeight:this.params.height}),new wp.media.controller.SiteIconCropper({imgSelectOptions:this.calculateImageSelectOptions,control:this})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this)},onSelect:function(){var e=this.frame.state().get("selection").first().toJSON(),t=this;this.params.width!==e.width||this.params.height!==e.height||this.params.flex_width||this.params.flex_height?this.frame.setState("cropper"):wp.ajax.post("crop-image",{nonce:e.nonces.edit,id:e.id,context:"site-icon",cropDetails:{x1:0,y1:0,width:this.params.width,height:this.params.height,dst_width:this.params.width,dst_height:this.params.height}}).done(function(e){t.setImageFromAttachment(e),t.frame.close()}).fail(function(){t.frame.trigger("content:error:crop")})},setImageFromAttachment:function(t){var n;_.each(["site_icon-32","thumbnail","full"],function(e){n||_.isUndefined(t.sizes[e])||(n=t.sizes[e])}),this.params.attachment=t,this.setting(t.id),n&&J('link[rel="icon"][sizes="32x32"]').attr("href",n.url)},removeFile:function(e){Y.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.params.attachment={},this.setting(""),this.renderContent(),J('link[rel="icon"][sizes="32x32"]').attr("href","/favicon.ico"))}}),Y.HeaderControl=Y.Control.extend({ready:function(){this.btnRemove=J("#customize-control-header_image .actions .remove"),this.btnNew=J("#customize-control-header_image .actions .new"),_.bindAll(this,"openMedia","removeImage"),this.btnNew.on("click",this.openMedia),this.btnRemove.on("click",this.removeImage),Y.HeaderTool.currentHeader=this.getInitialHeaderImage(),new Y.HeaderTool.CurrentView({model:Y.HeaderTool.currentHeader,el:"#customize-control-header_image .current .container"}),new Y.HeaderTool.ChoiceListView({collection:Y.HeaderTool.UploadsList=new Y.HeaderTool.ChoiceList,el:"#customize-control-header_image .choices .uploaded .list"}),new Y.HeaderTool.ChoiceListView({collection:Y.HeaderTool.DefaultsList=new Y.HeaderTool.DefaultsList,el:"#customize-control-header_image .choices .default .list"}),Y.HeaderTool.combinedList=Y.HeaderTool.CombinedList=new Y.HeaderTool.CombinedList([Y.HeaderTool.UploadsList,Y.HeaderTool.DefaultsList]),wp.media.controller.Cropper.prototype.defaults.doCropArgs.wp_customize="on",wp.media.controller.Cropper.prototype.defaults.doCropArgs.customize_theme=Y.settings.theme.stylesheet},getInitialHeaderImage:function(){var e;return Y.get().header_image&&Y.get().header_image_data&&!_.contains(["remove-header","random-default-image","random-uploaded-image"],Y.get().header_image)?(e=(e=_.find(_wpCustomizeHeader.uploads,function(e){return e.attachment_id===Y.get().header_image_data.attachment_id}))||{url:Y.get().header_image,thumbnail_url:Y.get().header_image,attachment_id:Y.get().header_image_data.attachment_id},new Y.HeaderTool.ImageModel({header:e,choice:e.url.split("/").pop()})):new Y.HeaderTool.ImageModel},calculateImageSelectOptions:function(e,t){var n=parseInt(_wpCustomizeHeader.data.width,10),i=parseInt(_wpCustomizeHeader.data.height,10),a=!!parseInt(_wpCustomizeHeader.data["flex-width"],10),o=!!parseInt(_wpCustomizeHeader.data["flex-height"],10),s=e.get("width"),e=e.get("height");return this.headerImage=new Y.HeaderTool.ImageModel,this.headerImage.set({themeWidth:n,themeHeight:i,themeFlexWidth:a,themeFlexHeight:o,imageWidth:s,imageHeight:e}),t.set("canSkipCrop",!this.headerImage.shouldBeCropped()),(t=n/i)<s/e?n=(i=e)*t:i=(n=s)/t,!(t={handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:s,imageHeight:e,x1:0,y1:0,x2:n,y2:i})==o&&!1==a&&(t.aspectRatio=n+":"+i),!1==o&&(t.maxHeight=i),!1==a&&(t.maxWidth=n),t},openMedia:function(e){var t=_wpMediaViewsL10n;e.preventDefault(),this.frame=wp.media({button:{text:t.selectAndCrop,close:!1},states:[new wp.media.controller.Library({title:t.chooseImage,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:_wpCustomizeHeader.data.width,suggestedHeight:_wpCustomizeHeader.data.height}),new wp.media.controller.Cropper({imgSelectOptions:this.calculateImageSelectOptions})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this),this.frame.open()},onSelect:function(){this.frame.setState("cropper")},onCropped:function(e){var t=e.url;this.setImageFromURL(t,e.attachment_id,e.width,e.height)},onSkippedCrop:function(e){var t=e.get("url"),n=e.get("width"),i=e.get("height");this.setImageFromURL(t,e.id,n,i)},setImageFromURL:function(e,t,n,i){var a={};a.url=e,a.thumbnail_url=e,a.timestamp=_.now(),t&&(a.attachment_id=t),n&&(a.width=n),i&&(a.height=i),t=new Y.HeaderTool.ImageModel({header:a,choice:e.split("/").pop()}),Y.HeaderTool.UploadsList.add(t),Y.HeaderTool.currentHeader.set(t.toJSON()),t.save(),t.importImage()},removeImage:function(){Y.HeaderTool.currentHeader.trigger("hide"),Y.HeaderTool.CombinedList.trigger("control:removeImage")}}),Y.ThemeControl=Y.Control.extend({touchDrag:!1,screenshotRendered:!1,ready:function(){var n=this,e=Y.panel("themes");function t(){return!e.canSwitchTheme(n.params.theme.id)}function i(){n.container.find("button.preview, button.preview-theme").toggleClass("disabled",t()),n.container.find("button.theme-install").toggleClass("disabled",t()||!1===Y.settings.theme._canInstall||!0===Y.settings.theme._filesystemCredentialsNeeded)}Y.state("selectedChangesetStatus").bind(i),Y.state("changesetStatus").bind(i),i(),n.container.on("touchmove",".theme",function(){n.touchDrag=!0}),n.container.on("click keydown touchend",".theme",function(e){var t;if(!Y.utils.isKeydownButNotEnterEvent(e))return!0===n.touchDrag?n.touchDrag=!1:void(J(e.target).is(".theme-actions .button, .update-theme")||(e.preventDefault(),(t=Y.section(n.section())).showDetails(n.params.theme,function(){Y.settings.theme._filesystemCredentialsNeeded&&t.overlay.find(".theme-actions .delete-theme").remove()})))}),n.container.on("render-screenshot",function(){var e=J(this).find("img"),t=e.data("src");t&&e.attr("src",t),n.screenshotRendered=!0})},filter:function(e){var t=this,n=0,i=(i=t.params.theme.name+" "+t.params.theme.description+" "+t.params.theme.tags+" "+t.params.theme.author+" ").toLowerCase().replace("-"," ");return _.isArray(e)||(e=[e]),t.params.theme.name.toLowerCase()===e.join(" ")?n=100:(n+=10*(i.split(e.join(" ")).length-1),_.each(e,function(e){n=(n+=2*(i.split(e+" ").length-1))+i.split(e).length-1}),99<n&&(n=99)),0!==n?(t.activate(),t.params.priority=101-n,!0):(t.deactivate(),!(t.params.priority=101))},rerenderAsInstalled:function(e){var t=this;e?t.params.theme.type="installed":(e=Y.section(t.params.section),t.params.theme.type=e.params.action),t.renderContent(),t.container.trigger("render-screenshot")}}),Y.CodeEditorControl=Y.Control.extend({initialize:function(e,t){var n=this;n.deferred=_.extend(n.deferred||{},{codemirror:J.Deferred()}),Y.Control.prototype.initialize.call(n,e,t),n.notifications.bind("add",function(e){var t;e.code===n.setting.id+":csslint_error"&&(e.templateId="customize-code-editor-lint-error-notification",e.render=(t=e.render,function(){var e=t.call(this);return e.find("input[type=checkbox]").on("click",function(){n.setting.notifications.remove("csslint_error")}),e}))})},ready:function(){var i=this;i.section()?Y.section(i.section(),function(n){n.deferred.embedded.done(function(){var t;n.expanded()?i.initEditor():n.expanded.bind(t=function(e){e&&(i.initEditor(),n.expanded.unbind(t))})})}):i.initEditor()},initEditor:function(){var e,t=this,n=!1;wp.codeEditor&&(_.isUndefined(t.params.editor_settings)||!1!==t.params.editor_settings)&&((n=wp.codeEditor.defaultSettings?_.clone(wp.codeEditor.defaultSettings):{}).codemirror=_.extend({},n.codemirror,{indentUnit:2,tabSize:2}),_.isObject(t.params.editor_settings))&&_.each(t.params.editor_settings,function(e,t){_.isObject(e)&&(n[t]=_.extend({},n[t],e))}),e=new Y.Element(t.container.find("textarea")),t.elements.push(e),e.sync(t.setting),e.set(t.setting()),n?t.initSyntaxHighlightingEditor(n):t.initPlainTextareaEditor()},focus:function(e){var t=this,e=_.extend({},e),n=e.completeCallback;e.completeCallback=function(){n&&n(),t.editor&&t.editor.codemirror.focus()},Y.Control.prototype.focus.call(t,e)},initSyntaxHighlightingEditor:function(e){var t=this,n=t.container.find("textarea"),i=!1,e=_.extend({},e,{onTabNext:_.bind(t.onTabNext,t),onTabPrevious:_.bind(t.onTabPrevious,t),onUpdateErrorNotice:_.bind(t.onUpdateErrorNotice,t)});t.editor=wp.codeEditor.initialize(n,e),J(t.editor.codemirror.display.lineDiv).attr({role:"textbox","aria-multiline":"true","aria-label":t.params.label,"aria-describedby":"editor-keyboard-trap-help-1 editor-keyboard-trap-help-2 editor-keyboard-trap-help-3 editor-keyboard-trap-help-4"}),t.container.find("label").on("click",function(){t.editor.codemirror.focus()}),t.editor.codemirror.on("change",function(e){i=!0,n.val(e.getValue()).trigger("change"),i=!1}),t.setting.bind(function(e){i||t.editor.codemirror.setValue(e)}),t.editor.codemirror.on("keydown",function(e,t){27===t.keyCode&&t.stopPropagation()}),t.deferred.codemirror.resolveWith(t,[t.editor.codemirror])},onTabNext:function(){var e=Y.section(this.section()).controls(),t=e.indexOf(this);e.length===t+1?J("#customize-footer-actions .collapse-sidebar").trigger("focus"):e[t+1].container.find(":focusable:first").focus()},onTabPrevious:function(){var e=Y.section(this.section()),t=e.controls(),n=t.indexOf(this);(0===n?e.contentContainer.find(".customize-section-title .customize-help-toggle, .customize-section-title .customize-section-description.open .section-description-close").last():t[n-1].contentContainer.find(":focusable:first")).focus()},onUpdateErrorNotice:function(e){this.setting.notifications.remove("csslint_error"),0!==e.length&&(e=1===e.length?Y.l10n.customCssError.singular.replace("%d","1"):Y.l10n.customCssError.plural.replace("%d",String(e.length)),this.setting.notifications.add(new Y.Notification("csslint_error",{message:e,type:"error"})))},initPlainTextareaEditor:function(){var a=this.container.find("textarea"),o=a[0];a.on("blur",function(){a.data("next-tab-blurs",!1)}),a.on("keydown",function(e){var t,n,i;27===e.keyCode?a.data("next-tab-blurs")||(a.data("next-tab-blurs",!0),e.stopPropagation()):9!==e.keyCode||e.ctrlKey||e.altKey||e.shiftKey||a.data("next-tab-blurs")||(t=o.selectionStart,n=o.selectionEnd,i=o.value,0<=t&&(o.value=i.substring(0,t).concat("\t",i.substring(n)),a.selectionStart=o.selectionEnd=t+1),e.stopPropagation(),e.preventDefault())}),this.deferred.codemirror.rejectWith(this)}}),Y.DateTimeControl=Y.Control.extend({ready:function(){var i=this;if(i.inputElements={},i.invalidDate=!1,_.bindAll(i,"populateSetting","updateDaysForMonth","populateDateInputs"),!i.setting)throw new Error("Missing setting");i.container.find(".date-input").each(function(){var e=J(this),t=e.data("component"),n=new Y.Element(e);i.inputElements[t]=n,i.elements.push(n),e.on("change",function(){i.invalidDate&&i.notifications.add(new Y.Notification("invalid_date",{message:Y.l10n.invalidDate}))}),e.on("input",_.debounce(function(){i.invalidDate||i.notifications.remove("invalid_date")})),e.on("blur",_.debounce(function(){i.invalidDate||i.populateDateInputs()}))}),i.inputElements.month.bind(i.updateDaysForMonth),i.inputElements.year.bind(i.updateDaysForMonth),i.populateDateInputs(),i.setting.bind(i.populateDateInputs),_.each(i.inputElements,function(e){e.bind(i.populateSetting)})},parseDateTime:function(e){var t;return(t=e?e.match(/^(\d\d\d\d)-(\d\d)-(\d\d)(?: (\d\d):(\d\d)(?::(\d\d))?)?$/):t)?(t.shift(),e={year:t.shift(),month:t.shift(),day:t.shift(),hour:t.shift()||"00",minute:t.shift()||"00",second:t.shift()||"00"},this.params.includeTime&&this.params.twelveHourFormat&&(e.hour=parseInt(e.hour,10),e.meridian=12<=e.hour?"pm":"am",e.hour=e.hour%12?String(e.hour%12):String(12),delete e.second),e):null},validateInputs:function(){var e,i,a=this;return a.invalidDate=!1,e=["year","day"],a.params.includeTime&&e.push("hour","minute"),_.find(e,function(e){var t,n,e=a.inputElements[e];return i=e.element.get(0),t=parseInt(e.element.attr("max"),10),n=parseInt(e.element.attr("min"),10),e=parseInt(e(),10),a.invalidDate=isNaN(e)||t<e||e<n,a.invalidDate||i.setCustomValidity(""),a.invalidDate}),a.inputElements.meridian&&!a.invalidDate&&(i=a.inputElements.meridian.element.get(0),"am"!==a.inputElements.meridian.get()&&"pm"!==a.inputElements.meridian.get()?a.invalidDate=!0:i.setCustomValidity("")),a.invalidDate?i.setCustomValidity(Y.l10n.invalidValue):i.setCustomValidity(""),(!a.section()||Y.section.has(a.section())&&Y.section(a.section()).expanded())&&_.result(i,"reportValidity"),a.invalidDate},updateDaysForMonth:function(){var e=this,t=parseInt(e.inputElements.month(),10),n=parseInt(e.inputElements.year(),10),i=parseInt(e.inputElements.day(),10);t&&n&&(n=new Date(n,t,0).getDate(),e.inputElements.day.element.attr("max",n),n<i)&&e.inputElements.day(String(n))},populateSetting:function(){var e,t=this;return!(t.validateInputs()||!t.params.allowPastDate&&!t.isFutureDate()||(e=t.convertInputDateToString(),t.setting.set(e),0))},convertInputDateToString:function(){var e,n=this,t="",i=function(e,t){return String(e).length<t&&(t=t-String(e).length,e=Math.pow(10,t).toString().substr(1)+String(e)),e},a=function(e){var t=parseInt(n.inputElements[e].get(),10);return _.contains(["month","day","hour","minute"],e)?t=i(t,2):"year"===e&&(t=i(t,4)),t},o=["year","-","month","-","day"];return n.params.includeTime&&(e=n.inputElements.meridian?n.convertHourToTwentyFourHourFormat(n.inputElements.hour(),n.inputElements.meridian()):n.inputElements.hour(),o=o.concat([" ",i(e,2),":","minute",":","00"])),_.each(o,function(e){t+=n.inputElements[e]?a(e):e}),t},isFutureDate:function(){return 0<Y.utils.getRemainingTime(this.convertInputDateToString())},convertHourToTwentyFourHourFormat:function(e,t){e=parseInt(e,10);return isNaN(e)?"":(t="pm"===t&&e<12?e+12:"am"===t&&12===e?e-12:e,String(t))},populateDateInputs:function(){var i=this.parseDateTime(this.setting.get());return!!i&&(_.each(this.inputElements,function(e,t){var n=i[t];"month"===t||"meridian"===t?(n=n.replace(/^0/,""),e.set(n)):(n=parseInt(n,10),e.element.is(document.activeElement)?n!==parseInt(e(),10)&&e.set(String(n)):e.set(i[t]))}),!0)},toggleFutureDateNotification:function(e){var t="not_future_date";return e?(e=new Y.Notification(t,{type:"error",message:Y.l10n.futureDateError}),this.notifications.add(e)):this.notifications.remove(t),this}}),Y.PreviewLinkControl=Y.Control.extend({defaults:_.extend({},Y.Control.prototype.defaults,{templateId:"customize-preview-link-control"}),ready:function(){var e,t,n,i,a,o=this;_.bindAll(o,"updatePreviewLink"),o.setting||(o.setting=new Y.Value),o.previewElements={},o.container.find(".preview-control-element").each(function(){t=J(this),e=t.data("component"),t=new Y.Element(t),o.previewElements[e]=t,o.elements.push(t)}),n=o.previewElements.url,i=o.previewElements.input,a=o.previewElements.button,i.link(o.setting),n.link(o.setting),n.bind(function(e){n.element.parent().attr({href:e,target:Y.settings.changeset.uuid})}),Y.bind("ready",o.updatePreviewLink),Y.state("saved").bind(o.updatePreviewLink),Y.state("changesetStatus").bind(o.updatePreviewLink),Y.state("activated").bind(o.updatePreviewLink),Y.previewer.previewUrl.bind(o.updatePreviewLink),a.element.on("click",function(e){e.preventDefault(),o.setting()&&(i.element.select(),document.execCommand("copy"),a(a.element.data("copied-text")))}),n.element.parent().on("click",function(e){J(this).hasClass("disabled")&&e.preventDefault()}),a.element.on("mouseenter",function(){o.setting()&&a(a.element.data("copy-text"))})},updatePreviewLink:function(){var e=!Y.state("saved").get()||""===Y.state("changesetStatus").get()||"auto-draft"===Y.state("changesetStatus").get();this.toggleSaveNotification(e),this.previewElements.url.element.parent().toggleClass("disabled",e),this.previewElements.button.element.prop("disabled",e),this.setting.set(Y.previewer.getFrontendPreviewUrl())},toggleSaveNotification:function(e){var t="changes_not_saved";e?(e=new Y.Notification(t,{type:"info",message:Y.l10n.saveBeforeShare}),this.notifications.add(e)):this.notifications.remove(t)}}),Y.defaultConstructor=Y.Setting,Y.control=new Y.Values({defaultConstructor:Y.Control}),Y.section=new Y.Values({defaultConstructor:Y.Section}),Y.panel=new Y.Values({defaultConstructor:Y.Panel}),Y.notifications=new Y.Notifications,Y.PreviewFrame=Y.Messenger.extend({sensitivity:null,initialize:function(e,t){var n=J.Deferred();n.promise(this),this.container=e.container,J.extend(e,{channel:Y.PreviewFrame.uuid()}),Y.Messenger.prototype.initialize.call(this,e,t),this.add("previewUrl",e.previewUrl),this.query=J.extend(e.query||{},{customize_messenger_channel:this.channel()}),this.run(n)},run:function(t){var e,n,i,a=this,o=!1,s=!1,r=null,c="{}"!==a.query.customized;a._ready&&a.unbind("ready",a._ready),a._ready=function(e){s=!0,r=e,a.container.addClass("iframe-ready"),e&&o&&t.resolveWith(a,[e])},a.bind("ready",a._ready),(e=document.createElement("a")).href=a.previewUrl(),n=_.extend(Y.utils.parseQueryString(e.search.substr(1)),{customize_changeset_uuid:a.query.customize_changeset_uuid,customize_theme:a.query.customize_theme,customize_messenger_channel:a.query.customize_messenger_channel}),!Y.settings.changeset.autosaved&&Y.state("saved").get()||(n.customize_autosaved="on"),e.search=J.param(n),a.iframe=J("<iframe />",{title:Y.l10n.previewIframeTitle,name:"customize-"+a.channel()}),a.iframe.attr("onmousewheel",""),a.iframe.attr("sandbox","allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-popups allow-popups-to-escape-sandbox allow-presentation allow-same-origin allow-scripts"),c?a.iframe.attr("data-src",e.href):a.iframe.attr("src",e.href),a.iframe.appendTo(a.container),a.targetWindow(a.iframe[0].contentWindow),c&&((i=J("<form>",{action:e.href,target:a.iframe.attr("name"),method:"post",hidden:"hidden"})).append(J("<input>",{type:"hidden",name:"_method",value:"GET"})),_.each(a.query,function(e,t){i.append(J("<input>",{type:"hidden",name:t,value:e}))}),a.container.append(i),i.trigger("submit"),i.remove()),a.bind("iframe-loading-error",function(e){a.iframe.remove(),0===e?a.login(t):-1===e?t.rejectWith(a,["cheatin"]):t.rejectWith(a,["request failure"])}),a.iframe.one("load",function(){o=!0,s?t.resolveWith(a,[r]):setTimeout(function(){t.rejectWith(a,["ready timeout"])},a.sensitivity)})},login:function(n){var i=this,a=function(){n.rejectWith(i,["logged out"])};if(this.triedLogin)return a();J.get(Y.settings.url.ajax,{action:"logged-in"}).fail(a).done(function(e){var t;"1"!==e&&a(),(t=J("<iframe />",{src:i.previewUrl(),title:Y.l10n.previewIframeTitle}).hide()).appendTo(i.container),t.on("load",function(){i.triedLogin=!0,t.remove(),i.run(n)})})},destroy:function(){Y.Messenger.prototype.destroy.call(this),this.iframe&&this.iframe.remove(),delete this.iframe,delete this.targetWindow}}),i=0,Y.PreviewFrame.uuid=function(){return"preview-"+String(i++)},Y.setDocumentTitle=function(e){e=Y.settings.documentTitleTmpl.replace("%s",e);document.title=e,Y.trigger("title",e)},Y.Previewer=Y.Messenger.extend({refreshBuffer:null,initialize:function(e,t){var n,o=this,i=document.createElement("a");J.extend(o,t||{}),o.deferred={active:J.Deferred()},o.refresh=_.debounce((n=o.refresh,function(){var e,t=function(){return 0===Y.state("processing").get()};t()?n.call(o):(e=function(){t()&&(n.call(o),Y.state("processing").unbind(e))},Y.state("processing").bind(e))}),o.refreshBuffer),o.container=Y.ensure(e.container),o.allowedUrls=e.allowedUrls,e.url=window.location.href,Y.Messenger.prototype.initialize.call(o,e),i.href=o.origin(),o.add("scheme",i.protocol.replace(/:$/,"")),o.add("previewUrl",e.previewUrl).setter(function(e){var n,i=null,t=[],a=document.createElement("a");return a.href=e,/\/wp-(admin|includes|content)(\/|$)/.test(a.pathname)?null:(1<a.search.length&&(delete(e=Y.utils.parseQueryString(a.search.substr(1))).customize_changeset_uuid,delete e.customize_theme,delete e.customize_messenger_channel,delete e.customize_autosaved,_.isEmpty(e)?a.search="":a.search=J.param(e)),t.push(a),o.scheme.get()+":"!==a.protocol&&((a=document.createElement("a")).href=t[0].href,a.protocol=o.scheme.get()+":",t.unshift(a)),n=document.createElement("a"),_.find(t,function(t){return!_.isUndefined(_.find(o.allowedUrls,function(e){if(n.href=e,a.protocol===n.protocol&&a.host===n.host&&0===a.pathname.indexOf(n.pathname.replace(/\/$/,"")))return i=t.href,!0}))}),i)}),o.bind("ready",o.ready),o.deferred.active.done(_.bind(o.keepPreviewAlive,o)),o.bind("synced",function(){o.send("active")}),o.previewUrl.bind(o.refresh),o.scroll=0,o.bind("scroll",function(e){o.scroll=e}),o.bind("url",function(e){var t,n=!1;o.scroll=0,o.previewUrl.bind(t=function(){n=!0}),o.previewUrl.set(e),o.previewUrl.unbind(t),n||o.refresh()}),o.bind("documentTitle",function(e){Y.setDocumentTitle(e)})},ready:function(e){var t=this,n={};n.settings=Y.get(),n["settings-modified-while-loading"]=t.settingsModifiedWhileLoading,"resolved"===t.deferred.active.state()&&!t.loading||(n.scroll=t.scroll),n["edit-shortcut-visibility"]=Y.state("editShortcutVisibility").get(),t.send("sync",n),e.currentUrl&&(t.previewUrl.unbind(t.refresh),t.previewUrl.set(e.currentUrl),t.previewUrl.bind(t.refresh)),_({panel:e.activePanels,section:e.activeSections,control:e.activeControls}).each(function(n,i){Y[i].each(function(e,t){_.isUndefined(Y.settings[i+"s"][t])&&_.isUndefined(n[t])||(n[t]?e.activate():e.deactivate())})}),e.settingValidities&&Y._handleSettingValidities({settingValidities:e.settingValidities,focusInvalidControl:!1})},keepPreviewAlive:function(){var e,t=function(){e=setTimeout(i,Y.settings.timeouts.keepAliveCheck)},n=function(){Y.state("previewerAlive").set(!0),clearTimeout(e),t()},i=function(){Y.state("previewerAlive").set(!1)};t(),this.bind("ready",n),this.bind("keep-alive",n)},query:function(){},abort:function(){this.loading&&(this.loading.destroy(),delete this.loading)},refresh:function(){var e,i=this;i.send("loading-initiated"),i.abort(),i.loading=new Y.PreviewFrame({url:i.url(),previewUrl:i.previewUrl(),query:i.query({excludeCustomizedSaved:!0})||{},container:i.container}),i.settingsModifiedWhileLoading={},Y.bind("change",e=function(e){i.settingsModifiedWhileLoading[e.id]=!0}),i.loading.always(function(){Y.unbind("change",e)}),i.loading.done(function(e){var t,n=this;i.preview=n,i.targetWindow(n.targetWindow()),i.channel(n.channel()),t=function(){n.unbind("synced",t),i._previousPreview&&i._previousPreview.destroy(),i._previousPreview=i.preview,i.deferred.active.resolve(),delete i.loading},n.bind("synced",t),i.trigger("ready",e)}),i.loading.fail(function(e){i.send("loading-failed"),"logged out"===e&&(i.preview&&(i.preview.destroy(),delete i.preview),i.login().done(i.refresh)),"cheatin"===e&&i.cheatin()})},login:function(){var t,n,i,a=this;return this._login||(t=J.Deferred(),this._login=t.promise(),n=new Y.Messenger({channel:"login",url:Y.settings.url.login}),i=J("<iframe />",{src:Y.settings.url.login,title:Y.l10n.loginIframeTitle}).appendTo(this.container),n.targetWindow(i[0].contentWindow),n.bind("login",function(){var e=a.refreshNonces();e.always(function(){i.remove(),n.destroy(),delete a._login}),e.done(function(){t.resolve()}),e.fail(function(){a.cheatin(),t.reject()})})),this._login},cheatin:function(){J(document.body).empty().addClass("cheatin").append("<h1>"+Y.l10n.notAllowedHeading+"</h1><p>"+Y.l10n.notAllowed+"</p>")},refreshNonces:function(){var e,t=J.Deferred();return t.promise(),(e=wp.ajax.post("customize_refresh_nonces",{wp_customize:"on",customize_theme:Y.settings.theme.stylesheet})).done(function(e){Y.trigger("nonce-refresh",e),t.resolve()}),e.fail(function(){t.reject()}),t}}),Y.settingConstructor={},Y.controlConstructor={color:Y.ColorControl,media:Y.MediaControl,upload:Y.UploadControl,image:Y.ImageControl,cropped_image:Y.CroppedImageControl,site_icon:Y.SiteIconControl,header:Y.HeaderControl,background:Y.BackgroundControl,background_position:Y.BackgroundPositionControl,theme:Y.ThemeControl,date_time:Y.DateTimeControl,code_editor:Y.CodeEditorControl},Y.panelConstructor={themes:Y.ThemesPanel},Y.sectionConstructor={themes:Y.ThemesSection,outer:Y.OuterSection},Y._handleSettingValidities=function(e){var o=[],n=!1;_.each(e.settingValidities,function(t,e){var a=Y(e);a&&(_.isObject(t)&&_.each(t,function(e,t){var n=!1,e=new Y.Notification(t,_.extend({fromServer:!0},e)),i=a.notifications(e.code);(n=i?e.type!==i.type||e.message!==i.message||!_.isEqual(e.data,i.data):n)&&a.notifications.remove(t),a.notifications.has(e.code)||a.notifications.add(e),o.push(a.id)}),a.notifications.each(function(e){!e.fromServer||"error"!==e.type||!0!==t&&t[e.code]||a.notifications.remove(e.code)}))}),e.focusInvalidControl&&(e=Y.findControlsForSettings(o),_(_.values(e)).find(function(e){return _(e).find(function(e){var t=e.section()&&Y.section.has(e.section())&&Y.section(e.section()).expanded();return(t=t&&e.expanded?e.expanded():t)&&(e.focus(),n=!0),n})}),n||_.isEmpty(e)||_.values(e)[0][0].focus())},Y.findControlsForSettings=function(e){var n,i={};return _.each(_.unique(e),function(e){var t=Y(e);t&&(n=t.findControls())&&0<n.length&&(i[e]=n)}),i},Y.reflowPaneContents=_.bind(function(){var i,e,t,a=[],o=!1;document.activeElement&&(e=J(document.activeElement)),Y.panel.each(function(e){var t,n;"themes"===e.id||(t=e.sections(),n=_.pluck(t,"headContainer"),a.push(e),i=e.contentContainer.is("ul")?e.contentContainer:e.contentContainer.find("ul:first"),Y.utils.areElementListsEqual(n,i.children("[id]")))||(_(t).each(function(e){i.append(e.headContainer)}),o=!0)}),Y.section.each(function(e){var t=e.controls(),n=_.pluck(t,"container");e.panel()||a.push(e),i=e.contentContainer.is("ul")?e.contentContainer:e.contentContainer.find("ul:first"),Y.utils.areElementListsEqual(n,i.children("[id]"))||(_(t).each(function(e){i.append(e.container)}),o=!0)}),a.sort(Y.utils.prioritySort),t=_.pluck(a,"headContainer"),i=J("#customize-theme-controls .customize-pane-parent"),Y.utils.areElementListsEqual(t,i.children())||(_(a).each(function(e){i.append(e.headContainer)}),o=!0),Y.panel.each(function(e){var t=e.active();e.active.callbacks.fireWith(e.active,[t,t])}),Y.section.each(function(e){var t=e.active();e.active.callbacks.fireWith(e.active,[t,t])}),o&&e&&e.trigger("focus"),Y.trigger("pane-contents-reflowed")},Y),Y.state=new Y.Values,_.each(["saved","saving","trashing","activated","processing","paneVisible","expandedPanel","expandedSection","changesetDate","selectedChangesetDate","changesetStatus","selectedChangesetStatus","remainingTimeToPublish","previewerAlive","editShortcutVisibility","changesetLocked","previewedDevice"],function(e){Y.state.create(e)}),J(function(){var h,o,t,n,i,d,u,p,a,s,r,c,l,f,m,H,L,g,v,w,b,M,O,C,R,y,e,x,k,z,S,T,E,j,q,B,D,N,P,I,U,A;function F(e){e&&e.lockUser&&(Y.settings.changeset.lockUser=e.lockUser),Y.state("changesetLocked").set(!0),Y.notifications.add(new R("changeset_locked",{lockUser:Y.settings.changeset.lockUser,allowOverride:Boolean(e&&e.allowOverride)}))}function W(){var e,t=document.createElement("a");return t.href=location.href,e=Y.utils.parseQueryString(t.search.substr(1)),Y.settings.changeset.latestAutoDraftUuid?e.changeset_uuid=Y.settings.changeset.latestAutoDraftUuid:e.customize_autosaved="on",e.return=Y.settings.url.return,t.search=J.param(e),t.href}function Q(){T||(wp.ajax.post("customize_dismiss_autosave_or_lock",{wp_customize:"on",customize_theme:Y.settings.theme.stylesheet,customize_changeset_uuid:Y.settings.changeset.uuid,nonce:Y.settings.nonce.dismiss_autosave_or_lock,dismiss_autosave:!0}),T=!0)}function K(){var e;return Y.state("activated").get()?(""!==(e=Y.state("changesetStatus").get())&&"auto-draft"!==e||(e="publish"),Y.state("selectedChangesetStatus").get()===e&&("future"!==Y.state("selectedChangesetStatus").get()||Y.state("selectedChangesetDate").get()===Y.state("changesetDate").get())&&Y.state("saved").get()&&"auto-draft"!==Y.state("changesetStatus").get()):0===Y._latestRevision}function V(){Y.unbind("change",V),Y.state("selectedChangesetStatus").unbind(V),Y.state("selectedChangesetDate").unbind(V),J(window).on("beforeunload.customize-confirm",function(){if(!K()&&!Y.state("changesetLocked").get())return setTimeout(function(){t.removeClass("customize-loading")},1),Y.l10n.saveAlert})}function $(){var e=J.Deferred(),t=!1,n=!1;return K()?n=!0:confirm(Y.l10n.saveAlert)?(n=!0,Y.each(function(e){e._dirty=!1}),J(document).off("visibilitychange.wp-customize-changeset-update"),J(window).off("beforeunload.wp-customize-changeset-update"),i.css("cursor","progress"),""!==Y.state("changesetStatus").get()&&(t=!0)):e.reject(),(n||t)&&wp.ajax.send("customize_dismiss_autosave_or_lock",{timeout:500,data:{wp_customize:"on",customize_theme:Y.settings.theme.stylesheet,customize_changeset_uuid:Y.settings.changeset.uuid,nonce:Y.settings.nonce.dismiss_autosave_or_lock,dismiss_autosave:t,dismiss_lock:n}}).always(function(){e.resolve()}),e.promise()}Y.settings=window._wpCustomizeSettings,Y.l10n=window._wpCustomizeControlsL10n,Y.settings&&J.support.postMessage&&(J.support.cors||!Y.settings.isCrossDomain)&&(null===Y.PreviewFrame.prototype.sensitivity&&(Y.PreviewFrame.prototype.sensitivity=Y.settings.timeouts.previewFrameSensitivity),null===Y.Previewer.prototype.refreshBuffer&&(Y.Previewer.prototype.refreshBuffer=Y.settings.timeouts.windowRefresh),o=J(document.body),t=o.children(".wp-full-overlay"),n=J("#customize-info .panel-title.site-title"),i=J(".customize-controls-close"),d=J("#save"),u=J("#customize-save-button-wrapper"),p=J("#publish-settings"),a=J("#customize-footer-actions"),Y.bind("ready",function(){Y.section.add(new Y.OuterSection("publish_settings",{title:Y.l10n.publishSettings,priority:0,active:Y.settings.theme.active}))}),Y.section("publish_settings",function(t){var e,n,i,a,o,s,r;function c(){r=r||Y.utils.highlightButton(u,{delay:1e3,focusTarget:d})}function l(){r&&(r(),r=null)}e=new Y.Control("trash_changeset",{type:"button",section:t.id,priority:30,input_attrs:{class:"button-link button-link-delete",value:Y.l10n.discardChanges}}),Y.control.add(e),e.deferred.embedded.done(function(){e.container.find(".button-link").on("click",function(){confirm(Y.l10n.trashConfirm)&&wp.customize.previewer.trash()})}),Y.control.add(new Y.PreviewLinkControl("changeset_preview_link",{section:t.id,priority:100})),t.active.validate=n=function(){return!!Y.state("activated").get()&&!(Y.state("trashing").get()||"trash"===Y.state("changesetStatus").get()||""===Y.state("changesetStatus").get()&&Y.state("saved").get())},s=function(){t.active.set(n())},Y.state("activated").bind(s),Y.state("trashing").bind(s),Y.state("saved").bind(s),Y.state("changesetStatus").bind(s),s(),(s=function(){p.toggle(t.active.get()),d.toggleClass("has-next-sibling",t.active.get())})(),t.active.bind(s),Y.state("selectedChangesetStatus").bind(l),t.contentContainer.find(".customize-action").text(Y.l10n.updating),t.contentContainer.find(".customize-section-back").removeAttr("tabindex"),p.prop("disabled",!1),p.on("click",function(e){e.preventDefault(),t.expanded.set(!t.expanded.get())}),t.expanded.bind(function(e){p.attr("aria-expanded",String(e)),p.toggleClass("active",e),e?l():(""!==(e=Y.state("changesetStatus").get())&&"auto-draft"!==e||(e="publish"),(Y.state("selectedChangesetStatus").get()!==e||"future"===Y.state("selectedChangesetStatus").get()&&Y.state("selectedChangesetDate").get()!==Y.state("changesetDate").get())&&c())}),s=new Y.Control("changeset_status",{priority:10,type:"radio",section:"publish_settings",setting:Y.state("selectedChangesetStatus"),templateId:"customize-selected-changeset-status-control",label:Y.l10n.action,choices:Y.settings.changeset.statusChoices}),Y.control.add(s),(i=new Y.DateTimeControl("changeset_scheduled_date",{priority:20,section:"publish_settings",setting:Y.state("selectedChangesetDate"),minYear:(new Date).getFullYear(),allowPastDate:!1,includeTime:!0,twelveHourFormat:/a/i.test(Y.settings.timeFormat),description:Y.l10n.scheduleDescription})).notifications.alt=!0,Y.control.add(i),a=function(){Y.state("selectedChangesetStatus").set("publish"),Y.previewer.save()},s=function(){var e="future"===Y.state("changesetStatus").get()&&"future"===Y.state("selectedChangesetStatus").get()&&Y.state("changesetDate").get()&&Y.state("selectedChangesetDate").get()===Y.state("changesetDate").get()&&0<=Y.utils.getRemainingTime(Y.state("changesetDate").get());e&&!o?o=setInterval(function(){var e=Y.utils.getRemainingTime(Y.state("changesetDate").get());Y.state("remainingTimeToPublish").set(e),e<=0&&(clearInterval(o),o=0,a())},1e3):!e&&o&&(clearInterval(o),o=0)},Y.state("changesetDate").bind(s),Y.state("selectedChangesetDate").bind(s),Y.state("changesetStatus").bind(s),Y.state("selectedChangesetStatus").bind(s),s(),i.active.validate=function(){return"future"===Y.state("selectedChangesetStatus").get()},(s=function(e){i.active.set("future"===e)})(Y.state("selectedChangesetStatus").get()),Y.state("selectedChangesetStatus").bind(s),Y.state("saving").bind(function(e){e&&"future"===Y.state("selectedChangesetStatus").get()&&i.toggleFutureDateNotification(!i.isFutureDate())})}),J("#customize-controls").on("keydown",function(e){var t=13===e.which,n=J(e.target);t&&(n.is("input:not([type=button])")||n.is("select"))&&e.preventDefault()}),J(".customize-info").find("> .accordion-section-title .customize-help-toggle").on("click",function(){var e=J(this).closest(".accordion-section"),t=e.find(".customize-panel-description:first");e.hasClass("cannot-expand")||(e.hasClass("open")?(e.toggleClass("open"),t.slideUp(Y.Panel.prototype.defaultExpandedArguments.duration,function(){t.trigger("toggled")}),J(this).attr("aria-expanded",!1)):(t.slideDown(Y.Panel.prototype.defaultExpandedArguments.duration,function(){t.trigger("toggled")}),e.toggleClass("open"),J(this).attr("aria-expanded",!0)))}),Y.previewer=new Y.Previewer({container:"#customize-preview",form:"#customize-controls",previewUrl:Y.settings.url.preview,allowedUrls:Y.settings.url.allowed},{nonce:Y.settings.nonce,query:function(e){var t={wp_customize:"on",customize_theme:Y.settings.theme.stylesheet,nonce:this.nonce.preview,customize_changeset_uuid:Y.settings.changeset.uuid};return!Y.settings.changeset.autosaved&&Y.state("saved").get()||(t.customize_autosaved="on"),t.customized=JSON.stringify(Y.dirtyValues({unsaved:e&&e.excludeCustomizedSaved})),t},save:function(i){var e,t,a=this,o=J.Deferred(),s=Y.state("selectedChangesetStatus").get(),r=Y.state("selectedChangesetDate").get(),n=Y.state("processing"),c={},l=[],d=[],u=[];function p(e){c[e.id]=!0}return i&&i.status&&(s=i.status),Y.state("saving").get()&&(o.reject("already_saving"),o.promise()),Y.state("saving").set(!0),t=function(){var n={},t=Y._latestRevision,e="client_side_error";if(Y.bind("change",p),Y.notifications.remove(e),Y.each(function(t){t.notifications.each(function(e){"error"!==e.type||e.fromServer||(l.push(t.id),n[t.id]||(n[t.id]={}),n[t.id][e.code]=e)})}),Y.control.each(function(t){t.setting&&(t.setting.id||!t.active.get())||t.notifications.each(function(e){"error"===e.type&&u.push([t])})}),d=_.union(u,_.values(Y.findControlsForSettings(l))),!_.isEmpty(d))return d[0][0].focus(),Y.unbind("change",p),l.length&&Y.notifications.add(new Y.Notification(e,{message:(1===l.length?Y.l10n.saveBlockedError.singular:Y.l10n.saveBlockedError.plural).replace(/%s/g,String(l.length)),type:"error",dismissible:!0,saveFailure:!0})),o.rejectWith(a,[{setting_invalidities:n}]),Y.state("saving").set(!1),o.promise();e=J.extend(a.query({excludeCustomizedSaved:!1}),{nonce:a.nonce.save,customize_changeset_status:s}),i&&i.date?e.customize_changeset_date=i.date:"future"===s&&r&&(e.customize_changeset_date=r),i&&i.title&&(e.customize_changeset_title=i.title),Y.trigger("save-request-params",e),e=wp.ajax.post("customize_save",e),Y.state("processing").set(Y.state("processing").get()+1),Y.trigger("save",e),e.always(function(){Y.state("processing").set(Y.state("processing").get()-1),Y.state("saving").set(!1),Y.unbind("change",p)}),Y.notifications.each(function(e){e.saveFailure&&Y.notifications.remove(e.code)}),e.fail(function(e){var t,n={type:"error",dismissible:!0,fromServer:!0,saveFailure:!0};"0"===e?e="not_logged_in":"-1"===e&&(e="invalid_nonce"),"invalid_nonce"===e?a.cheatin():"not_logged_in"===e?(a.preview.iframe.hide(),a.login().done(function(){a.save(),a.preview.iframe.show()})):e.code?"not_future_date"===e.code&&Y.section.has("publish_settings")&&Y.section("publish_settings").active.get()&&Y.control.has("changeset_scheduled_date")?Y.control("changeset_scheduled_date").toggleFutureDateNotification(!0).focus():"changeset_locked"!==e.code&&(t=new Y.Notification(e.code,_.extend(n,{message:e.message}))):t=new Y.Notification("unknown_error",_.extend(n,{message:Y.l10n.unknownRequestFail})),t&&Y.notifications.add(t),e.setting_validities&&Y._handleSettingValidities({settingValidities:e.setting_validities,focusInvalidControl:!0}),o.rejectWith(a,[e]),Y.trigger("error",e),"changeset_already_published"===e.code&&e.next_changeset_uuid&&(Y.settings.changeset.uuid=e.next_changeset_uuid,Y.state("changesetStatus").set(""),Y.settings.changeset.branching&&h.send("changeset-uuid",Y.settings.changeset.uuid),Y.previewer.send("changeset-uuid",Y.settings.changeset.uuid))}),e.done(function(e){a.send("saved",e),Y.state("changesetStatus").set(e.changeset_status),e.changeset_date&&Y.state("changesetDate").set(e.changeset_date),"publish"===e.changeset_status&&(Y.each(function(e){e._dirty&&(_.isUndefined(Y._latestSettingRevisions[e.id])||Y._latestSettingRevisions[e.id]<=t)&&(e._dirty=!1)}),Y.state("changesetStatus").set(""),Y.settings.changeset.uuid=e.next_changeset_uuid,Y.settings.changeset.branching)&&h.send("changeset-uuid",Y.settings.changeset.uuid),Y._lastSavedRevision=Math.max(t,Y._lastSavedRevision),e.setting_validities&&Y._handleSettingValidities({settingValidities:e.setting_validities,focusInvalidControl:!0}),o.resolveWith(a,[e]),Y.trigger("saved",e),_.isEmpty(c)||Y.state("saved").set(!1)})},0===n()?t():(e=function(){0===n()&&(Y.state.unbind("change",e),t())},Y.state.bind("change",e)),o.promise()},trash:function(){var e,n,i;Y.state("trashing").set(!0),Y.state("processing").set(Y.state("processing").get()+1),e=wp.ajax.post("customize_trash",{customize_changeset_uuid:Y.settings.changeset.uuid,nonce:Y.settings.nonce.trash}),Y.notifications.add(new Y.OverlayNotification("changeset_trashing",{type:"info",message:Y.l10n.revertingChanges,loading:!0})),n=function(){var e,t=document.createElement("a");Y.state("changesetStatus").set("trash"),Y.each(function(e){e._dirty=!1}),Y.state("saved").set(!0),t.href=location.href,delete(e=Y.utils.parseQueryString(t.search.substr(1))).changeset_uuid,e.return=Y.settings.url.return,t.search=J.param(e),location.replace(t.href)},i=function(e,t){e=e||"unknown_error";Y.state("processing").set(Y.state("processing").get()-1),Y.state("trashing").set(!1),Y.notifications.remove("changeset_trashing"),Y.notifications.add(new Y.Notification(e,{message:t||Y.l10n.unknownError,dismissible:!0,type:"error"}))},e.done(function(e){n(e.message)}),e.fail(function(e){var t=e.code||"trashing_failed";e.success||"non_existent_changeset"===t||"changeset_already_trashed"===t?n(e.message):i(t,e.message)})},getFrontendPreviewUrl:function(){var e,t=document.createElement("a");return t.href=this.previewUrl.get(),e=Y.utils.parseQueryString(t.search.substr(1)),Y.state("changesetStatus").get()&&"publish"!==Y.state("changesetStatus").get()&&(e.customize_changeset_uuid=Y.settings.changeset.uuid),Y.state("activated").get()||(e.customize_theme=Y.settings.theme.stylesheet),t.search=J.param(e),t.href}}),J.ajaxPrefilter(function(e){/wp_customize=on/.test(e.data)&&(e.data+="&"+J.param({customize_preview_nonce:Y.settings.nonce.preview}))}),Y.previewer.bind("nonce",function(e){J.extend(this.nonce,e)}),Y.bind("nonce-refresh",function(e){J.extend(Y.settings.nonce,e),J.extend(Y.previewer.nonce,e),Y.previewer.send("nonce-refresh",e)}),J.each(Y.settings.settings,function(e,t){var n=Y.settingConstructor[t.type]||Y.Setting;Y.add(new n(e,t.value,{transport:t.transport,previewer:Y.previewer,dirty:!!t.dirty}))}),J.each(Y.settings.panels,function(e,t){var n=Y.panelConstructor[t.type]||Y.Panel,t=_.extend({params:t},t);Y.panel.add(new n(e,t))}),J.each(Y.settings.sections,function(e,t){var n=Y.sectionConstructor[t.type]||Y.Section,t=_.extend({params:t},t);Y.section.add(new n(e,t))}),J.each(Y.settings.controls,function(e,t){var n=Y.controlConstructor[t.type]||Y.Control,t=_.extend({params:t},t);Y.control.add(new n(e,t))}),_.each(["panel","section","control"],function(e){var t=Y.settings.autofocus[e];t&&Y[e](t,function(e){e.deferred.embedded.done(function(){Y.previewer.deferred.active.done(function(){e.focus()})})})}),Y.bind("ready",Y.reflowPaneContents),J([Y.panel,Y.section,Y.control]).each(function(e,t){var n=_.debounce(Y.reflowPaneContents,Y.settings.timeouts.reflowPaneContents);t.bind("add",n),t.bind("change",n),t.bind("remove",n)}),Y.bind("ready",function(){var e,t,n;Y.notifications.container=J("#customize-notifications-area"),Y.notifications.bind("change",_.debounce(function(){Y.notifications.render()})),e=J(".wp-full-overlay-sidebar-content"),Y.notifications.bind("rendered",function(){e.css("top",""),0!==Y.notifications.count()&&(t=Y.notifications.container.outerHeight()+1,n=parseInt(e.css("top"),10),e.css("top",n+t+"px")),Y.notifications.trigger("sidebarTopUpdated")}),Y.notifications.render()}),s=Y.state,c=s.instance("saved"),l=s.instance("saving"),f=s.instance("trashing"),m=s.instance("activated"),e=s.instance("processing"),I=s.instance("paneVisible"),H=s.instance("expandedPanel"),L=s.instance("expandedSection"),g=s.instance("changesetStatus"),v=s.instance("selectedChangesetStatus"),w=s.instance("changesetDate"),b=s.instance("selectedChangesetDate"),M=s.instance("previewerAlive"),O=s.instance("editShortcutVisibility"),C=s.instance("changesetLocked"),s.bind("change",function(){var e;m()?""===g.get()&&c()?(Y.settings.changeset.currentUserCanPublish?d.val(Y.l10n.published):d.val(Y.l10n.saved),i.find(".screen-reader-text").text(Y.l10n.close)):("draft"===v()?c()&&v()===g()?d.val(Y.l10n.draftSaved):d.val(Y.l10n.saveDraft):"future"===v()?!c()||v()!==g()||w.get()!==b.get()?d.val(Y.l10n.schedule):d.val(Y.l10n.scheduled):Y.settings.changeset.currentUserCanPublish&&d.val(Y.l10n.publish),i.find(".screen-reader-text").text(Y.l10n.cancel)):(d.val(Y.l10n.activate),i.find(".screen-reader-text").text(Y.l10n.cancel)),e=!l()&&!f()&&!C()&&(!m()||!c()||g()!==v()&&""!==g()||"future"===v()&&w.get()!==b.get()),d.prop("disabled",!e)}),v.validate=function(e){return""===e||"auto-draft"===e?null:e},S=Y.settings.changeset.currentUserCanPublish?"publish":"draft",g(Y.settings.changeset.status),C(Boolean(Y.settings.changeset.lockUser)),w(Y.settings.changeset.publishDate),b(Y.settings.changeset.publishDate),v(""===Y.settings.changeset.status||"auto-draft"===Y.settings.changeset.status?S:Y.settings.changeset.status),v.link(g),c(!0),""===g()&&Y.each(function(e){e._dirty&&c(!1)}),l(!1),m(Y.settings.theme.active),e(0),I(!0),H(!1),L(!1),M(!0),O("visible"),Y.bind("change",function(){s("saved").get()&&s("saved").set(!1)}),Y.settings.changeset.branching&&c.bind(function(e){e||r(!0)}),l.bind(function(e){o.toggleClass("saving",e)}),f.bind(function(e){o.toggleClass("trashing",e)}),Y.bind("saved",function(e){s("saved").set(!0),"publish"===e.changeset_status&&s("activated").set(!0)}),m.bind(function(e){e&&Y.trigger("activated")}),r=function(e){var t,n;if(history.replaceState){if((t=document.createElement("a")).href=location.href,n=Y.utils.parseQueryString(t.search.substr(1)),e){if(n.changeset_uuid===Y.settings.changeset.uuid)return;n.changeset_uuid=Y.settings.changeset.uuid}else{if(!n.changeset_uuid)return;delete n.changeset_uuid}t.search=J.param(n),history.replaceState({},document.title,t.href)}},Y.settings.changeset.branching&&g.bind(function(e){r(""!==e&&"publish"!==e&&"trash"!==e)}),R=Y.OverlayNotification.extend({templateId:"customize-changeset-locked-notification",lockUser:null,initialize:function(e,t){e=e||"changeset_locked",t=_.extend({message:"",type:"warning",containerClasses:"",lockUser:{}},t);t.containerClasses+=" notification-changeset-locked",Y.OverlayNotification.prototype.initialize.call(this,e,t)},render:function(){var t,n,i=this,e=_.extend({allowOverride:!1,returnUrl:Y.settings.url.return,previewUrl:Y.previewer.previewUrl.get(),frontendPreviewUrl:Y.previewer.getFrontendPreviewUrl()},this),a=Y.OverlayNotification.prototype.render.call(e);return Y.requestChangesetUpdate({},{autosave:!0}).fail(function(e){e.autosaved||a.find(".notice-error").prop("hidden",!1).text(e.message||Y.l10n.unknownRequestFail)}),(t=a.find(".customize-notice-take-over-button")).on("click",function(e){e.preventDefault(),n||(t.addClass("disabled"),(n=wp.ajax.post("customize_override_changeset_lock",{wp_customize:"on",customize_theme:Y.settings.theme.stylesheet,customize_changeset_uuid:Y.settings.changeset.uuid,nonce:Y.settings.nonce.override_lock})).done(function(){Y.notifications.remove(i.code),Y.state("changesetLocked").set(!1)}),n.fail(function(e){e=e.message||Y.l10n.unknownRequestFail;a.find(".notice-error").prop("hidden",!1).text(e),n.always(function(){t.removeClass("disabled")})}),n.always(function(){n=null}))}),a}}),Y.settings.changeset.lockUser&&F({allowOverride:!0}),J(document).on("heartbeat-send.update_lock_notice",function(e,t){t.check_changeset_lock=!0,t.changeset_uuid=Y.settings.changeset.uuid}),J(document).on("heartbeat-tick.update_lock_notice",function(e,t){var n,i="changeset_locked";t.customize_changeset_lock_user&&((n=Y.notifications(i))&&n.lockUser.id!==Y.settings.changeset.lockUser.id&&Y.notifications.remove(i),F({lockUser:t.customize_changeset_lock_user}))}),Y.bind("error",function(e){"changeset_locked"===e.code&&e.lock_user&&F({lockUser:e.lock_user})}),T=!(S=[]),Y.settings.changeset.autosaved&&(Y.state("saved").set(!1),S.push("customize_autosaved")),Y.settings.changeset.branching||Y.settings.changeset.status&&"auto-draft"!==Y.settings.changeset.status||S.push("changeset_uuid"),0<S.length&&(S=S,e=document.createElement("a"),x=0,e.href=location.href,y=Y.utils.parseQueryString(e.search.substr(1)),_.each(S,function(e){void 0!==y[e]&&(x+=1,delete y[e])}),0!==x)&&(e.search=J.param(y),history.replaceState({},document.title,e.href)),(Y.settings.changeset.latestAutoDraftUuid||Y.settings.changeset.hasAutosaveRevision)&&(z="autosave_available",Y.notifications.add(new Y.Notification(z,{message:Y.l10n.autosaveNotice,type:"warning",dismissible:!0,render:function(){var e=Y.Notification.prototype.render.call(this),t=e.find("a");return t.prop("href",W()),t.on("click",function(e){e.preventDefault(),location.replace(W())}),e.find(".notice-dismiss").on("click",Q),e}})),Y.bind("change",k=function(){Q(),Y.notifications.remove(z),Y.unbind("change",k),Y.state("changesetStatus").unbind(k)}),Y.state("changesetStatus").bind(k)),parseInt(J("#customize-info").data("block-theme"),10)&&(S=Y.l10n.blockThemeNotification,Y.notifications.add(new Y.Notification("site_editor_block_theme_notice",{message:S,type:"info",dismissible:!1,render:function(){var e=Y.Notification.prototype.render.call(this),t=e.find("button.switch-to-editor");return t.on("click",function(e){e.preventDefault(),location.assign(t.data("action"))}),e}}))),Y.previewer.previewUrl()?Y.previewer.refresh():Y.previewer.previewUrl(Y.settings.url.home),d.on("click",function(e){Y.previewer.save(),e.preventDefault()}).on("keydown",function(e){9!==e.which&&(13===e.which&&Y.previewer.save(),e.preventDefault())}),i.on("keydown",function(e){9!==e.which&&(13===e.which&&this.click(),e.preventDefault())}),J(".collapse-sidebar").on("click",function(){Y.state("paneVisible").set(!Y.state("paneVisible").get())}),Y.state("paneVisible").bind(function(e){t.toggleClass("preview-only",!e),t.toggleClass("expanded",e),t.toggleClass("collapsed",!e),e?J(".collapse-sidebar").attr({"aria-expanded":"true","aria-label":Y.l10n.collapseSidebar}):J(".collapse-sidebar").attr({"aria-expanded":"false","aria-label":Y.l10n.expandSidebar})}),o.on("keydown",function(e){var t,n=[],i=[],a=[];27===e.which&&(J(e.target).is("body")||J.contains(J("#customize-controls")[0],e.target))&&null===e.target.closest(".block-editor-writing-flow")&&null===e.target.closest(".block-editor-block-list__block-popover")&&(Y.control.each(function(e){e.expanded&&e.expanded()&&_.isFunction(e.collapse)&&n.push(e)}),Y.section.each(function(e){e.expanded()&&i.push(e)}),Y.panel.each(function(e){e.expanded()&&a.push(e)}),0<n.length&&0===i.length&&(n.length=0),t=n[0]||i[0]||a[0])&&("themes"===t.params.type?o.hasClass("modal-open")?t.closeDetails():Y.panel.has("themes")&&Y.panel("themes").collapse():(t.collapse(),e.preventDefault()))}),J(".customize-controls-preview-toggle").on("click",function(){Y.state("paneVisible").set(!Y.state("paneVisible").get())}),P=J(".wp-full-overlay-sidebar-content"),I=function(e){var t=Y.state("expandedSection").get(),n=Y.state("expandedPanel").get();if(D&&D.element&&(j(D.element),D.element.find(".description").off("toggled",E)),!e)if(!t&&n&&n.contentContainer)e=n;else{if(n||!t||!t.contentContainer)return void(D=!1);e=t}(n=e.contentContainer.find(".customize-section-title, .panel-meta").first()).length?((D={instance:e,element:n,parent:n.closest(".customize-pane-child"),height:n.outerHeight()}).element.find(".description").on("toggled",E),t&&q(D.element,D.parent)):D=!1},Y.state("expandedSection").bind(I),Y.state("expandedPanel").bind(I),P.on("scroll",_.throttle(function(){var e,t;D&&(e=P.scrollTop(),t=N?e===N?0:N<e?1:-1:1,N=e,0!==t)&&B(D,e,t)},8)),Y.notifications.bind("sidebarTopUpdated",function(){D&&D.element.hasClass("is-sticky")&&D.element.css("top",P.css("top"))}),j=function(e){e.hasClass("is-sticky")&&e.removeClass("is-sticky").addClass("maybe-sticky is-in-view").css("top",P.scrollTop()+"px")},q=function(e,t){e.hasClass("is-in-view")&&(e.removeClass("maybe-sticky is-in-view").css({width:"",top:""}),t.css("padding-top",""))},E=function(){D.height=D.element.outerHeight()},B=function(e,t,n){var i=e.element,a=e.parent,e=e.height,o=parseInt(i.css("top"),10),s=i.hasClass("maybe-sticky"),r=i.hasClass("is-sticky"),c=i.hasClass("is-in-view");if(-1===n){if(!s&&e<=t)s=!0,i.addClass("maybe-sticky");else if(0===t)return i.removeClass("maybe-sticky is-in-view is-sticky").css({top:"",width:""}),void a.css("padding-top","");c&&!r?t<=o&&i.addClass("is-sticky").css({top:P.css("top"),width:a.outerWidth()+"px"}):s&&!c&&(i.addClass("is-in-view").css("top",t-e+"px"),a.css("padding-top",e+"px"))}else r&&(o=t,i.removeClass("is-sticky").css({top:o+"px",width:""})),c&&o+e<t&&(i.removeClass("is-in-view"),a.css("padding-top",""))},Y.previewedDevice=Y.state("previewedDevice"),Y.bind("ready",function(){_.find(Y.settings.previewableDevices,function(e,t){if(!0===e.default)return Y.previewedDevice.set(t),!0})}),a.find(".devices button").on("click",function(e){Y.previewedDevice.set(J(e.currentTarget).data("device"))}),Y.previewedDevice.bind(function(e){var t=J(".wp-full-overlay"),n="";a.find(".devices button").removeClass("active").attr("aria-pressed",!1),a.find(".devices .preview-"+e).addClass("active").attr("aria-pressed",!0),J.each(Y.settings.previewableDevices,function(e){n+=" preview-"+e}),t.removeClass(n).addClass("preview-"+e)}),n.length&&Y("blogname",function(t){function e(){var e=t()||"";n.text(e.toString().trim()||Y.l10n.untitledBlogName)}t.bind(e),e()}),h=new Y.Messenger({url:Y.settings.url.parent,channel:"loader"}),U=!1,h.bind("back",function(){U=!0}),Y.bind("change",V),Y.state("selectedChangesetStatus").bind(V),Y.state("selectedChangesetDate").bind(V),h.bind("confirm-close",function(){$().done(function(){h.send("confirmed-close",!0)}).fail(function(){h.send("confirmed-close",!1)})}),i.on("click.customize-controls-close",function(e){e.preventDefault(),U?h.send("close"):$().done(function(){J(window).off("beforeunload.customize-confirm"),window.location.href=i.prop("href")})}),J.each(["saved","change"],function(e,t){Y.bind(t,function(){h.send(t)})}),Y.bind("title",function(e){h.send("title",e)}),Y.settings.changeset.branching&&h.send("changeset-uuid",Y.settings.changeset.uuid),h.send("ready"),J.each({background_image:{controls:["background_preset","background_position","background_size","background_repeat","background_attachment"],callback:function(e){return!!e}},show_on_front:{controls:["page_on_front","page_for_posts"],callback:function(e){return"page"===e}},header_textcolor:{controls:["header_textcolor"],callback:function(e){return"blank"!==e}}},function(e,i){Y(e,function(n){J.each(i.controls,function(e,t){Y.control(t,function(t){function e(e){t.container.toggle(i.callback(e))}e(n.get()),n.bind(e)})})})}),Y.control("background_preset",function(e){var i={default:[!1,!1,!1,!1],fill:[!0,!1,!1,!1],fit:[!0,!1,!0,!1],repeat:[!0,!1,!1,!0],custom:[!0,!0,!0,!0]},a={default:[_wpCustomizeBackground.defaults["default-position-x"],_wpCustomizeBackground.defaults["default-position-y"],_wpCustomizeBackground.defaults["default-size"],_wpCustomizeBackground.defaults["default-repeat"],_wpCustomizeBackground.defaults["default-attachment"]],fill:["left","top","cover","no-repeat","fixed"],fit:["left","top","contain","no-repeat","fixed"],repeat:["left","top","auto","repeat","scroll"]},t=function(n){_.each(["background_position","background_size","background_repeat","background_attachment"],function(e,t){e=Y.control(e);e&&e.container.toggle(i[n][t])})},n=function(n){_.each(["background_position_x","background_position_y","background_size","background_repeat","background_attachment"],function(e,t){e=Y(e);e&&e.set(a[n][t])})},o=e.setting.get();t(o),e.setting.bind("change",function(e){t(e),"custom"!==e&&n(e)})}),Y.control("background_repeat",function(t){t.elements[0].unsync(Y("background_repeat")),t.element=new Y.Element(t.container.find("input")),t.element.set("no-repeat"!==t.setting()),t.element.bind(function(e){t.setting.set(e?"repeat":"no-repeat")}),t.setting.bind(function(e){t.element.set("no-repeat"!==e)})}),Y.control("background_attachment",function(t){t.elements[0].unsync(Y("background_attachment")),t.element=new Y.Element(t.container.find("input")),t.element.set("fixed"!==t.setting()),t.element.bind(function(e){t.setting.set(e?"scroll":"fixed")}),t.setting.bind(function(e){t.element.set("fixed"!==e)})}),Y.control("display_header_text",function(t){var n="";t.elements[0].unsync(Y("header_textcolor")),t.element=new Y.Element(t.container.find("input")),t.element.set("blank"!==t.setting()),t.element.bind(function(e){e||(n=Y("header_textcolor").get()),t.setting.set(e?n:"blank")}),t.setting.bind(function(e){t.element.set("blank"!==e)})}),Y("show_on_front","page_on_front","page_for_posts",function(i,a,o){function e(){var e="show_on_front_page_collision",t=parseInt(a(),10),n=parseInt(o(),10);"page"===i()&&(this===a&&0<t&&Y.previewer.previewUrl.set(Y.settings.url.home),this===o)&&0<n&&Y.previewer.previewUrl.set(Y.settings.url.home+"?page_id="+n),"page"===i()&&t&&n&&t===n?i.notifications.add(new Y.Notification(e,{type:"error",message:Y.l10n.pageOnFrontError})):i.notifications.remove(e)}i.bind(e),a.bind(e),o.bind(e),e.call(i,i()),Y.control("show_on_front",function(e){e.deferred.embedded.done(function(){e.container.append(e.getNotificationsContainerElement())})})}),A=J.Deferred(),Y.section("custom_css",function(t){t.deferred.embedded.done(function(){t.expanded()?A.resolve(t):t.expanded.bind(function(e){e&&A.resolve(t)})})}),A.done(function(e){var t=Y.control("custom_css");t.container.find(".customize-control-title:first").addClass("screen-reader-text"),e.container.find(".section-description-buttons .section-description-close").on("click",function(){e.container.find(".section-meta .customize-section-description:first").removeClass("open").slideUp(),e.container.find(".customize-help-toggle").attr("aria-expanded","false").focus()}),t&&!t.setting.get()&&(e.container.find(".section-meta .customize-section-description:first").addClass("open").show().trigger("toggled"),e.container.find(".customize-help-toggle").attr("aria-expanded","true"))}),Y.control("header_video",function(n){n.deferred.embedded.done(function(){function e(){var e=Y.section(n.section()),t="video_header_not_available";e&&(n.active.get()?e.notifications.remove(t):e.notifications.add(new Y.Notification(t,{type:"info",message:Y.l10n.videoHeaderNotice})))}e(),n.active.bind(e)})}),Y.previewer.bind("selective-refresh-setting-validities",function(e){Y._handleSettingValidities({settingValidities:e,focusInvalidControl:!1})}),Y.previewer.bind("focus-control-for-setting",function(n){var i=[];Y.control.each(function(e){var t=_.pluck(e.settings,"id");-1!==_.indexOf(t,n)&&i.push(e)}),i.length&&(i.sort(function(e,t){return e.priority()-t.priority()}),i[0].focus())}),Y.previewer.bind("refresh",function(){Y.previewer.refresh()}),Y.state("paneVisible").bind(function(e){var t=window.matchMedia?window.matchMedia("screen and ( max-width: 640px )").matches:J(window).width()<=640;Y.state("editShortcutVisibility").set(e||t?"visible":"hidden")}),window.matchMedia&&window.matchMedia("screen and ( max-width: 640px )").addListener(function(){var e=Y.state("paneVisible");e.callbacks.fireWith(e,[e.get(),e.get()])}),Y.previewer.bind("edit-shortcut-visibility",function(e){Y.state("editShortcutVisibility").set(e)}),Y.state("editShortcutVisibility").bind(function(e){Y.previewer.send("edit-shortcut-visibility",e)}),Y.bind("change",function e(){var t,n,i,a=!1;function o(e){e||Y.settings.changeset.autosaved||(Y.settings.changeset.autosaved=!0,Y.previewer.send("autosaving"))}Y.unbind("change",e),Y.state("saved").bind(o),o(Y.state("saved").get()),n=function(){a||(a=!0,Y.requestChangesetUpdate({},{autosave:!0}).always(function(){a=!1})),i()},(i=function(){clearTimeout(t),t=setTimeout(function(){n()},Y.settings.timeouts.changesetAutoSave)})(),J(document).on("visibilitychange.wp-customize-changeset-update",function(){document.hidden&&n()}),J(window).on("beforeunload.wp-customize-changeset-update",function(){n()})}),J(document).one("tinymce-editor-setup",function(){window.tinymce.ui.FloatPanel&&(!window.tinymce.ui.FloatPanel.zIndex||window.tinymce.ui.FloatPanel.zIndex<500001)&&(window.tinymce.ui.FloatPanel.zIndex=500001)}),o.addClass("ready"),Y.trigger("ready"))})}((wp,jQuery));