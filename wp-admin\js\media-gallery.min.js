/*! This file is auto-generated */
jQuery(function(o){o("body").on("click.wp-gallery",function(a){var e,t,n=o(a.target);n.hasClass("wp-set-header")?((window.dialogArguments||opener||parent||top).location.href=n.data("location"),a.preventDefault()):n.hasClass("wp-set-background")&&(n=n.data("attachment-id"),e=o('input[name="attachments['+n+'][image-size]"]:checked').val(),t=o("#_wpnonce").val()&&"",jQuery.post(ajaxurl,{action:"set-background-image",attachment_id:n,_ajax_nonce:t,size:e},function(){var a=window.dialogArguments||opener||parent||top;a.tb_remove(),a.location.reload()}),a.preventDefault())})});