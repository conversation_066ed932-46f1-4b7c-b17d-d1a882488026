!function(){"use strict";function e(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function t(t){return function(t){if(Array.isArray(t))return e(t)}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(t,n){if(t){if("string"==typeof t)return e(t,n);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?e(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var n=function(e){var n=e.targetModal,r=e.openTrigger,o=e.triggers,a=void 0===o?[]:o,i=document.getElementById(n);if(i){var s={openTrigger:r,closeTrigger:"data-gpmodal-close",openClass:"gp-modal--open"},l="";a.length>0&&function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t.filter(Boolean).forEach((function(e){e.addEventListener("click",(function(e){e.preventDefault(),c()})),e.addEventListener("keydown",(function(e){" "!==e.key&&"Enter"!==e.key&&"Spacebar"!==e.key||(e.preventDefault(),c())}))}))}.apply(void 0,t(a))}function c(){i.classList.add("gp-modal--transition"),l=document.activeElement,i.classList.add(s.openClass),u("disable"),i.addEventListener("touchstart",f),i.addEventListener("click",f),document.addEventListener("keydown",g),function(){var e=v();if(0!==e.length){var t=e.filter((function(e){return!e.hasAttribute(s.closeTrigger)}));t.length>0&&t[0].focus(),0===t.length&&e[0].focus()}}(),setTimeout((function(){return i.classList.remove("gp-modal--transition")}),100)}function d(){i.classList.add("gp-modal--transition"),i.removeEventListener("touchstart",f),i.removeEventListener("click",f),document.removeEventListener("keydown",g),u("enable"),l&&l.focus&&l.focus(),i.classList.remove(s.openClass),setTimeout((function(){return i.classList.remove("gp-modal--transition")}),500)}function u(e){var t=document.querySelector("body");switch(e){case"enable":Object.assign(t.style,{overflow:""});break;case"disable":Object.assign(t.style,{overflow:"hidden"})}}function f(e){(e.target.hasAttribute(s.closeTrigger)||e.target.parentNode.hasAttribute(s.closeTrigger))&&(e.preventDefault(),e.stopPropagation(),d())}function g(e){27===e.keyCode&&d(),9===e.keyCode&&function(e){var t=v();if(0!==t.length){var n=(t=t.filter((function(e){return null!==e.offsetParent}))).indexOf(document.activeElement);e.shiftKey&&0===n&&(t[t.length-1].focus(),e.preventDefault()),!e.shiftKey&&t.length>0&&n===t.length-1&&(t[0].focus(),e.preventDefault())}}(e)}function v(){var e=i.querySelectorAll(["a[href]","area[href]",'input:not([disabled]):not([type="hidden"]):not([aria-hidden])',"select:not([disabled]):not([aria-hidden])","textarea:not([disabled]):not([aria-hidden])","button:not([disabled]):not([aria-hidden])","iframe","object","embed","[contenteditable]",'[tabindex]:not([tabindex^="-"])']);return Array.apply(void 0,t(e))}},r=Object.assign({},{openTrigger:"data-gpmodal-trigger"}),o=t(document.querySelectorAll("[".concat(r.openTrigger,"]"))).reduce((function(e,t){var n=t.attributes[r.openTrigger].value;return e[n]=e[n]||[],e[n].push(t),e}),[]);for(var a in o){var i=o[a];r.targetModal=a,r.triggers=t(i),new n(r)}}();