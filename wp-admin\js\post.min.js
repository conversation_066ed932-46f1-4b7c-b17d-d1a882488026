/*! This file is auto-generated */
window.makeSlugeditClickable=window.editPermalink=function(){},window.wp=window.wp||{},function(s){var t=!1,a=wp.i18n.__;window.commentsBox={st:0,get:function(t,e){var i=this.st;return this.st+=e=e||20,this.total=t,s("#commentsdiv .spinner").addClass("is-active"),t={action:"get-comments",mode:"single",_ajax_nonce:s("#add_comment_nonce").val(),p:s("#post_ID").val(),start:i,number:e},s.post(ajaxurl,t,function(t){t=wpAjax.parseAjaxResponse(t),s("#commentsdiv .widefat").show(),s("#commentsdiv .spinner").removeClass("is-active"),"object"==typeof t&&t.responses[0]?(s("#the-comment-list").append(t.responses[0].data),theList=theExtraList=null,s("a[className*=':']").off(),commentsBox.st>commentsBox.total?s("#show-comments").hide():s("#show-comments").show().children("a").text(a("Show more comments"))):1==t?s("#show-comments").text(a("No more comments found.")):s("#the-comment-list").append('<tr><td colspan="2">'+wpAjax.broken+"</td></tr>")}),!1},load:function(t){this.st=jQuery("#the-comment-list tr.comment:visible").length,this.get(t)}},window.WPSetThumbnailHTML=function(t){s(".inside","#postimagediv").html(t)},window.WPSetThumbnailID=function(t){var e=s('input[value="_thumbnail_id"]',"#list-table");0<e.length&&s("#meta\\["+e.attr("id").match(/[0-9]+/)+"\\]\\[value\\]").text(t)},window.WPRemoveThumbnail=function(t){s.post(ajaxurl,{action:"set-post-thumbnail",post_id:s("#post_ID").val(),thumbnail_id:-1,_ajax_nonce:t,cookie:encodeURIComponent(document.cookie)},function(t){"0"==t?alert(a("Could not set that as the thumbnail image. Try a different attachment.")):WPSetThumbnailHTML(t)})},s(document).on("heartbeat-send.refresh-lock",function(t,e){var i=s("#active_post_lock").val(),a=s("#post_ID").val(),n={};a&&s("#post-lock-dialog").length&&(n.post_id=a,i&&(n.lock=i),e["wp-refresh-post-lock"]=n)}).on("heartbeat-tick.refresh-lock",function(t,e){var i,a;e["wp-refresh-post-lock"]&&((e=e["wp-refresh-post-lock"]).lock_error?(i=s("#post-lock-dialog")).length&&!i.is(":visible")&&(wp.autosave&&(s(document).one("heartbeat-tick",function(){wp.autosave.server.suspend(),i.removeClass("saving").addClass("saved"),s(window).off("beforeunload.edit-post")}),i.addClass("saving"),wp.autosave.server.triggerSave()),e.lock_error.avatar_src&&(a=s("<img />",{class:"avatar avatar-64 photo",width:64,height:64,alt:"",src:e.lock_error.avatar_src,srcset:e.lock_error.avatar_src_2x?e.lock_error.avatar_src_2x+" 2x":void 0}),i.find("div.post-locked-avatar").empty().append(a)),i.show().find(".currently-editing").text(e.lock_error.text),i.find(".wp-tab-first").trigger("focus")):e.new_lock&&s("#active_post_lock").val(e.new_lock))}).on("before-autosave.update-post-slug",function(){t=document.activeElement&&"title"===document.activeElement.id}).on("after-autosave.update-post-slug",function(){s("#edit-slug-box > *").length||t||s.post(ajaxurl,{action:"sample-permalink",post_id:s("#post_ID").val(),new_title:s("#title").val(),samplepermalinknonce:s("#samplepermalinknonce").val()},function(t){"-1"!=t&&s("#edit-slug-box").html(t)})})}(jQuery),function(a){var n,t;function i(){n=!1,window.clearTimeout(t),t=window.setTimeout(function(){n=!0},3e5)}a(function(){i()}).on("heartbeat-send.wp-refresh-nonces",function(t,e){var i=a("#wp-auth-check-wrap");(n||i.length&&!i.hasClass("hidden"))&&(i=a("#post_ID").val())&&a("#_wpnonce").val()&&(e["wp-refresh-post-nonces"]={post_id:i})}).on("heartbeat-tick.wp-refresh-nonces",function(t,e){e=e["wp-refresh-post-nonces"];e&&(i(),e.replace&&a.each(e.replace,function(t,e){a("#"+t).val(e)}),e.heartbeatNonce)&&(window.heartbeatSettings.nonce=e.heartbeatNonce)})}(jQuery),jQuery(function(h){var d,e,i,a,n,s,o,l,r,t,c,p,u=h("#content"),v=h(document),f=h("#post_ID").val()||0,m=h("#submitpost"),w=!0,g=h("#post-visibility-select"),b=h("#timestampdiv"),k=h("#post-status-select"),_=!!window.navigator.platform&&-1!==window.navigator.platform.indexOf("Mac"),y=new ClipboardJS(".copy-attachment-url.edit-media"),x=wp.i18n.__,C=wp.i18n._x;function D(t){c.hasClass("wp-editor-expand")||(r?o.theme.resizeTo(null,l+t.pageY):u.height(Math.max(50,l+t.pageY)),t.preventDefault())}function j(){var t;c.hasClass("wp-editor-expand")||(t=r?(o.focus(),((t=parseInt(h("#wp-content-editor-container .mce-toolbar-grp").height(),10))<10||200<t)&&(t=30),parseInt(h("#content_ifr").css("height"),10)+t-28):(u.trigger("focus"),parseInt(u.css("height"),10)),v.off(".wp-editor-resize"),t&&50<t&&t<5e3&&setUserSetting("ed_size",t))}postboxes.add_postbox_toggles(pagenow),window.name="",h("#post-lock-dialog .notification-dialog").on("keydown",function(t){var e;9==t.which&&((e=h(t.target)).hasClass("wp-tab-first")&&t.shiftKey?(h(this).find(".wp-tab-last").trigger("focus"),t.preventDefault()):e.hasClass("wp-tab-last")&&!t.shiftKey&&(h(this).find(".wp-tab-first").trigger("focus"),t.preventDefault()))}).filter(":visible").find(".wp-tab-first").trigger("focus"),wp.heartbeat&&h("#post-lock-dialog").length&&wp.heartbeat.interval(10),i=m.find(":submit, a.submitdelete, #post-preview").on("click.edit-post",function(t){var e=h(this);e.hasClass("disabled")?t.preventDefault():e.hasClass("submitdelete")||e.is("#post-preview")||h("form#post").off("submit.edit-post").on("submit.edit-post",function(t){if(!t.isDefaultPrevented()){if(wp.autosave&&wp.autosave.server.suspend(),"undefined"!=typeof commentReply){if(!commentReply.discardCommentChanges())return!1;commentReply.close()}w=!1,h(window).off("beforeunload.edit-post"),i.addClass("disabled"),("publish"===e.attr("id")?m.find("#major-publishing-actions .spinner"):m.find("#minor-publishing .spinner")).addClass("is-active")}})}),h("#post-preview").on("click.post-preview",function(t){var e=h(this),i=h("form#post"),a=h("input#wp-preview"),n=e.attr("target")||"wp-preview",s=navigator.userAgent.toLowerCase();t.preventDefault(),e.hasClass("disabled")||(wp.autosave&&wp.autosave.server.tempBlockSave(),a.val("dopreview"),i.attr("target",n).trigger("submit").attr("target",""),-1!==s.indexOf("safari")&&-1===s.indexOf("chrome")&&i.attr("action",function(t,e){return e+"?t="+(new Date).getTime()}),a.val(""))}),h("#auto_draft").val()&&h("#title").on("blur",function(){var t;this.value&&!h("#edit-slug-box > *").length&&(h("form#post").one("submit",function(){t=!0}),window.setTimeout(function(){!t&&wp.autosave&&wp.autosave.server.triggerSave()},200))}),v.on("autosave-disable-buttons.edit-post",function(){i.addClass("disabled")}).on("autosave-enable-buttons.edit-post",function(){wp.heartbeat&&wp.heartbeat.hasConnectionError()||i.removeClass("disabled")}).on("before-autosave.edit-post",function(){h(".autosave-message").text(x("Saving Draft\u2026"))}).on("after-autosave.edit-post",function(t,e){h(".autosave-message").text(e.message),h(document.body).hasClass("post-new-php")&&h(".submitbox .submitdelete").show()}),h(window).on("beforeunload.edit-post",function(t){var e=window.tinymce&&window.tinymce.get("content"),i=!1;if(wp.autosave?i=wp.autosave.server.postChanged():e&&(i=!e.isHidden()&&e.isDirty()),i)return t.preventDefault(),x("The changes you made will be lost if you navigate away from this page.")}).on("pagehide.edit-post",function(t){if(w&&(!t.target||"#document"==t.target.nodeName)){var t=h("#post_ID").val(),e=h("#active_post_lock").val();if(t&&e){t={action:"wp-remove-post-lock",_wpnonce:h("#_wpnonce").val(),post_ID:t,active_post_lock:e};if(window.FormData&&window.navigator.sendBeacon){var i=new window.FormData;if(h.each(t,function(t,e){i.append(t,e)}),window.navigator.sendBeacon(ajaxurl,i))return}h.post({async:!1,data:t,url:ajaxurl})}}}),h("#tagsdiv-post_tag").length?window.tagBox&&window.tagBox.init():h(".meta-box-sortables").children("div.postbox").each(function(){if(0===this.id.indexOf("tagsdiv-"))return window.tagBox&&window.tagBox.init(),!1}),h(".categorydiv").each(function(){var t,a,e,i=h(this).attr("id").split("-");i.shift(),a=i.join("-"),e="category"==a?"cats":a+"_tab",h("a","#"+a+"-tabs").on("click",function(t){t.preventDefault();t=h(this).attr("href");h(this).parent().addClass("tabs").siblings("li").removeClass("tabs"),h("#"+a+"-tabs").siblings(".tabs-panel").hide(),h(t).show(),"#"+a+"-all"==t?deleteUserSetting(e):setUserSetting(e,"pop")}),getUserSetting(e)&&h('a[href="#'+a+'-pop"]',"#"+a+"-tabs").trigger("click"),h("#new"+a).one("focus",function(){h(this).val("").removeClass("form-input-tip")}),h("#new"+a).on("keypress",function(t){13===t.keyCode&&(t.preventDefault(),h("#"+a+"-add-submit").trigger("click"))}),h("#"+a+"-add-submit").on("click",function(){h("#new"+a).trigger("focus")}),i=function(t){return!!h("#new"+a).val()&&(t.data+="&"+h(":checked","#"+a+"checklist").serialize(),h("#"+a+"-add-submit").prop("disabled",!0),t)},t=function(t,e){var i=h("#new"+a+"_parent");h("#"+a+"-add-submit").prop("disabled",!1),"undefined"!=e.parsed.responses[0]&&(e=e.parsed.responses[0].supplemental.newcat_parent)&&(i.before(e),i.remove())},h("#"+a+"checklist").wpList({alt:"",response:a+"-ajax-response",addBefore:i,addAfter:t}),h("#"+a+"-add-toggle").on("click",function(t){t.preventDefault(),h("#"+a+"-adder").toggleClass("wp-hidden-children"),h('a[href="#'+a+'-all"]',"#"+a+"-tabs").trigger("click"),h("#new"+a).trigger("focus")}),h("#"+a+"checklist, #"+a+"checklist-pop").on("click",'li.popular-category > label input[type="checkbox"]',function(){var t=h(this),e=t.is(":checked"),i=t.val();i&&t.parents("#taxonomy-"+a).length&&(h("input#in-"+a+"-"+i+', input[id^="in-'+a+"-"+i+'-"]').prop("checked",e),h("input#in-popular-"+a+"-"+i).prop("checked",e))})}),h("#postcustom").length&&h("#the-list").wpList({addBefore:function(t){return t.data+="&post_id="+h("#post_ID").val(),t},addAfter:function(){h("table#list-table").show()}}),h("#submitdiv").length&&(d=h("#timestamp").html(),e=h("#post-visibility-display").html(),a=function(){"public"!=g.find("input:radio:checked").val()?(h("#sticky").prop("checked",!1),h("#sticky-span").hide()):h("#sticky-span").show(),"password"!=g.find("input:radio:checked").val()?h("#password-span").hide():h("#password-span").show()},n=function(){if(b.length){var t,e=h("#post_status"),i=h('option[value="publish"]',e),a=h("#aa").val(),n=h("#mm").val(),s=h("#jj").val(),o=h("#hh").val(),l=h("#mn").val(),r=new Date(a,n-1,s,o,l),c=new Date(h("#hidden_aa").val(),h("#hidden_mm").val()-1,h("#hidden_jj").val(),h("#hidden_hh").val(),h("#hidden_mn").val()),p=new Date(h("#cur_aa").val(),h("#cur_mm").val()-1,h("#cur_jj").val(),h("#cur_hh").val(),h("#cur_mn").val());if(r.getFullYear()!=a||1+r.getMonth()!=n||r.getDate()!=s||r.getMinutes()!=l)return b.find(".timestamp-wrap").addClass("form-invalid"),!1;b.find(".timestamp-wrap").removeClass("form-invalid"),p<r?(t=x("Schedule for:"),h("#publish").val(C("Schedule","post action/button label"))):r<=p&&"publish"!=h("#original_post_status").val()?(t=x("Publish on:"),h("#publish").val(x("Publish"))):(t=x("Published on:"),h("#publish").val(x("Update"))),c.toUTCString()==r.toUTCString()?h("#timestamp").html(d):h("#timestamp").html("\n"+t+" <b>"+x("%1$s %2$s, %3$s at %4$s:%5$s").replace("%1$s",h('option[value="'+n+'"]',"#mm").attr("data-text")).replace("%2$s",parseInt(s,10)).replace("%3$s",a).replace("%4$s",("00"+o).slice(-2)).replace("%5$s",("00"+l).slice(-2))+"</b> "),"private"==g.find("input:radio:checked").val()?(h("#publish").val(x("Update")),0===i.length?e.append('<option value="publish">'+x("Privately Published")+"</option>"):i.html(x("Privately Published")),h('option[value="publish"]',e).prop("selected",!0),h("#misc-publishing-actions .edit-post-status").hide()):("future"==h("#original_post_status").val()||"draft"==h("#original_post_status").val()?i.length&&(i.remove(),e.val(h("#hidden_post_status").val())):i.html(x("Published")),e.is(":hidden")&&h("#misc-publishing-actions .edit-post-status").show()),h("#post-status-display").text(wp.sanitize.stripTagsAndEncodeText(h("option:selected",e).text())),"private"==h("option:selected",e).val()||"publish"==h("option:selected",e).val()?h("#save-post").hide():(h("#save-post").show(),"pending"==h("option:selected",e).val()?h("#save-post").show().val(x("Save as Pending")):h("#save-post").show().val(x("Save Draft")))}return!0},h("#visibility .edit-visibility").on("click",function(t){t.preventDefault(),g.is(":hidden")&&(a(),g.slideDown("fast",function(){g.find('input[type="radio"]').first().trigger("focus")}),h(this).hide())}),g.find(".cancel-post-visibility").on("click",function(t){g.slideUp("fast"),h("#visibility-radio-"+h("#hidden-post-visibility").val()).prop("checked",!0),h("#post_password").val(h("#hidden-post-password").val()),h("#sticky").prop("checked",h("#hidden-post-sticky").prop("checked")),h("#post-visibility-display").html(e),h("#visibility .edit-visibility").show().trigger("focus"),n(),t.preventDefault()}),g.find(".save-post-visibility").on("click",function(t){var e="",i=g.find("input:radio:checked").val();switch(g.slideUp("fast"),h("#visibility .edit-visibility").show().trigger("focus"),n(),"public"!==i&&h("#sticky").prop("checked",!1),i){case"public":e=h("#sticky").prop("checked")?x("Public, Sticky"):x("Public");break;case"private":e=x("Private");break;case"password":e=x("Password Protected")}h("#post-visibility-display").text(e),t.preventDefault()}),g.find("input:radio").on("change",function(){a()}),b.siblings("a.edit-timestamp").on("click",function(t){b.is(":hidden")&&(b.slideDown("fast",function(){h("input, select",b.find(".timestamp-wrap")).first().trigger("focus")}),h(this).hide()),t.preventDefault()}),b.find(".cancel-timestamp").on("click",function(t){b.slideUp("fast").siblings("a.edit-timestamp").show().trigger("focus"),h("#mm").val(h("#hidden_mm").val()),h("#jj").val(h("#hidden_jj").val()),h("#aa").val(h("#hidden_aa").val()),h("#hh").val(h("#hidden_hh").val()),h("#mn").val(h("#hidden_mn").val()),n(),t.preventDefault()}),b.find(".save-timestamp").on("click",function(t){n()&&(b.slideUp("fast"),b.siblings("a.edit-timestamp").show().trigger("focus")),t.preventDefault()}),h("#post").on("submit",function(t){n()||(t.preventDefault(),b.show(),wp.autosave&&wp.autosave.enableButtons(),h("#publishing-action .spinner").removeClass("is-active"))}),k.siblings("a.edit-post-status").on("click",function(t){k.is(":hidden")&&(k.slideDown("fast",function(){k.find("select").trigger("focus")}),h(this).hide()),t.preventDefault()}),k.find(".save-post-status").on("click",function(t){k.slideUp("fast").siblings("a.edit-post-status").show().trigger("focus"),n(),t.preventDefault()}),k.find(".cancel-post-status").on("click",function(t){k.slideUp("fast").siblings("a.edit-post-status").show().trigger("focus"),h("#post_status").val(h("#hidden_post_status").val()),n(),t.preventDefault()})),h("#titlediv").on("click",".edit-slug",function(){var t,e,a,i,n=0,s=h("#post_name"),o=s.val(),l=h("#sample-permalink"),r=l.html(),c=h("#sample-permalink a").html(),p=h("#edit-slug-buttons"),d=p.html(),u=h("#editable-post-name-full");for(u.find("img").replaceWith(function(){return this.alt}),u=u.html(),l.html(c),a=h("#editable-post-name"),i=a.html(),p.html('<button type="button" class="save button button-small">'+x("OK")+'</button> <button type="button" class="cancel button-link">'+x("Cancel")+"</button>"),p.children(".save").on("click",function(){var i=a.children("input").val();i==h("#editable-post-name-full").text()?p.children(".cancel").trigger("click"):h.post(ajaxurl,{action:"sample-permalink",post_id:f,new_slug:i,new_title:h("#title").val(),samplepermalinknonce:h("#samplepermalinknonce").val()},function(t){var e=h("#edit-slug-box");e.html(t),e.hasClass("hidden")&&e.fadeIn("fast",function(){e.removeClass("hidden")}),p.html(d),l.html(r),s.val(i),h(".edit-slug").trigger("focus"),wp.a11y.speak(x("Permalink saved"))})}),p.children(".cancel").on("click",function(){h("#view-post-btn").show(),a.html(i),p.html(d),l.html(r),s.val(o),h(".edit-slug").trigger("focus")}),t=0;t<u.length;++t)"%"==u.charAt(t)&&n++;c=n>u.length/4?"":u,e=x("URL Slug"),a.html('<label for="new-post-slug" class="screen-reader-text">'+e+'</label><input type="text" id="new-post-slug" value="'+c+'" autocomplete="off" spellcheck="false" />').children("input").on("keydown",function(t){var e=t.which;13===e&&(t.preventDefault(),p.children(".save").trigger("click")),27===e&&p.children(".cancel").trigger("click")}).on("keyup",function(){s.val(this.value)}).trigger("focus")}),window.wptitlehint=function(t){var e=h("#"+(t=t||"title")),i=h("#"+t+"-prompt-text");""===e.val()&&i.removeClass("screen-reader-text"),e.on("input",function(){""===this.value?i.removeClass("screen-reader-text"):i.addClass("screen-reader-text")})},wptitlehint(),t=h("#post-status-info"),c=h("#postdivrich"),!u.length||"ontouchstart"in window?h("#content-resize-handle").hide():t.on("mousedown.wp-editor-resize",function(t){(o="undefined"!=typeof tinymce?tinymce.get("content"):o)&&!o.isHidden()?(r=!0,l=h("#content_ifr").height()-t.pageY):(r=!1,l=u.height()-t.pageY,u.trigger("blur")),v.on("mousemove.wp-editor-resize",D).on("mouseup.wp-editor-resize mouseleave.wp-editor-resize",j),t.preventDefault()}).on("mouseup.wp-editor-resize",j),"undefined"!=typeof tinymce&&(h("#post-formats-select input.post-format").on("change.set-editor-class",function(){var t,e,i=this.id;i&&h(this).prop("checked")&&(t=tinymce.get("content"))&&((e=t.getBody()).className=e.className.replace(/\bpost-format-[^ ]+/,""),t.dom.addClass(e,"post-format-0"==i?"post-format-standard":i),h(document).trigger("editor-classchange"))}),h("#page_template").on("change.set-editor-class",function(){var t,e,i=h(this).val()||"";(i=i.substr(i.lastIndexOf("/")+1,i.length).replace(/\.php$/,"").replace(/\./g,"-"))&&(t=tinymce.get("content"))&&((e=t.getBody()).className=e.className.replace(/\bpage-template-[^ ]+/,""),t.dom.addClass(e,"page-template-"+i),h(document).trigger("editor-classchange"))})),u.on("keydown.wp-autosave",function(t){83!==t.which||t.shiftKey||t.altKey||_&&(!t.metaKey||t.ctrlKey)||!_&&!t.ctrlKey||(wp.autosave&&wp.autosave.server.triggerSave(),t.preventDefault())}),"auto-draft"===h("#original_post_status").val()&&window.history.replaceState&&h("#publish").on("click",function(){p=(p=window.location.href)+(-1!==p.indexOf("?")?"&":"?")+"wp-post-new-reload=true",window.history.replaceState(null,null,p)}),y.on("success",function(t){var e=h(t.trigger),i=h(".success",e.closest(".copy-to-clipboard-container"));t.clearSelection(),clearTimeout(s),i.removeClass("hidden"),s=setTimeout(function(){i.addClass("hidden")},3e3),wp.a11y.speak(x("The file URL has been copied to your clipboard"))})}),function(t,o){t(function(){var i,e=t("#content"),a=t("#wp-word-count").find(".word-count"),n=0;function s(){var t=!i||i.isHidden()?e.val():i.getContent({format:"raw"}),t=o.count(t);t!==n&&a.text(t),n=t}t(document).on("tinymce-editor-init",function(t,e){"content"===e.id&&(i=e).on("nodechange keyup",_.debounce(s,1e3))}),e.on("input keyup",_.debounce(s,1e3)),s()})}(jQuery,new wp.utils.WordCounter);