<?php
/**
 * Rooftop Importer Class
 * Handles the core import functionality for JSON rooftop data
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rooftop_Importer {

    /**
     * Import results
     */
    private $import_results = array(
        'success' => 0,
        'errors' => 0,
        'skipped' => 0,
        'messages' => array()
    );

    /**
     * Import settings
     */
    private $settings = array();

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('rooftop_importer_settings', array());
    }

    /**
     * Import rooftops from JSON file
     */
    public function import_from_file($file_path) {
        rooftop_importer_log('Starting import from file: ' . $file_path, 'info');

        if (!file_exists($file_path)) {
            $this->add_error('File does not exist: ' . $file_path);
            return false;
        }

        rooftop_importer_log('File exists, reading content', 'info');

        $json_content = file_get_contents($file_path);
        if (empty($json_content)) {
            $this->add_error('File is empty or could not be read');
            return false;
        }

        rooftop_importer_log('File content read, length: ' . strlen($json_content), 'info');

        $data = json_decode($json_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->add_error('Invalid JSON format: ' . json_last_error_msg());
            return false;
        }

        rooftop_importer_log('JSON decoded successfully, data keys: ' . implode(', ', array_keys($data)), 'info');

        return $this->import_rooftop_data($data);
    }

    /**
     * Import single rooftop from JSON data
     */
    public function import_rooftop_data($data) {
        try {
            rooftop_importer_log('Starting import of rooftop data for: ' . (isset($data['name']) ? $data['name'] : 'Unknown'), 'info');

            // Validate required fields
            if (!$this->validate_rooftop_data($data)) {
                rooftop_importer_log('Validation failed for rooftop data', 'error');
                return false;
            }

            rooftop_importer_log('Validation passed, checking for existing rooftop', 'info');

            // Check if rooftop already exists
            $existing_post = $this->find_existing_rooftop($data);

            if ($existing_post && !$this->settings['update_existing_posts']) {
                rooftop_importer_log('Skipping existing rooftop: ' . $data['name'], 'warning');
                $this->add_message('Skipped existing rooftop: ' . $data['name'], 'warning');
                $this->import_results['skipped']++;
                return true;
            }

            rooftop_importer_log('Preparing post data', 'info');

            // Prepare post data
            $post_data = $this->prepare_post_data($data, $existing_post);

            rooftop_importer_log('Post data prepared: ' . print_r($post_data, true), 'info');

            // Insert or update post
            if ($existing_post) {
                $post_data['ID'] = $existing_post->ID;
                $post_id = wp_update_post($post_data);
                $action = 'updated';
                rooftop_importer_log('Updating existing post ID: ' . $existing_post->ID, 'info');
            } else {
                $post_id = wp_insert_post($post_data);
                $action = 'created';
                rooftop_importer_log('Creating new post', 'info');
            }

            if (is_wp_error($post_id)) {
                $this->add_error('Failed to save rooftop: ' . $post_id->get_error_message());
                return false;
            }

            rooftop_importer_log("Post {$action} successfully with ID: {$post_id}", 'success');

            // Save custom fields
            rooftop_importer_log('Saving custom fields', 'info');
            $this->save_custom_fields($post_id, $data);

            // Assign taxonomies
            rooftop_importer_log('Assigning taxonomies', 'info');
            $this->assign_taxonomies($post_id, $data);

            // Handle featured image if enabled
            if (isset($this->settings['import_images']) && $this->settings['import_images']) {
                rooftop_importer_log('Handling featured image', 'info');
                $this->handle_featured_image($post_id, $data);
            }

            $this->add_message("Successfully {$action} rooftop: " . $data['name'], 'success');
            $this->import_results['success']++;

            rooftop_importer_log("Rooftop {$action}: " . $data['name'] . " (ID: {$post_id})", 'success');

            return $post_id;

        } catch (Exception $e) {
            rooftop_importer_log('Exception during import: ' . $e->getMessage(), 'error');
            $this->add_error('Exception during import: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate rooftop data
     */
    private function validate_rooftop_data($data) {
        $required_fields = array('id', 'name');

        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $this->add_error("Missing required field: {$field}");
                return false;
            }
        }

        return true;
    }

    /**
     * Find existing rooftop by ID or name
     */
    private function find_existing_rooftop($data) {
        // First try to find by custom field rooftop_id
        $posts = get_posts(array(
            'post_type' => 'rooftops',
            'meta_query' => array(
                array(
                    'key' => '_rooftop_id',
                    'value' => $data['id'],
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        ));

        if (!empty($posts)) {
            return $posts[0];
        }

        // If not found, try by title
        $post = get_page_by_title($data['name'], OBJECT, 'rooftops');
        return $post;
    }

    /**
     * Prepare post data for insertion/update
     */
    private function prepare_post_data($data, $existing_post = null) {
        $post_data = array(
            'post_type' => 'rooftops',
            'post_title' => sanitize_text_field($data['name']),
            'post_status' => 'publish',
            'post_author' => get_current_user_id()
        );

        // Set content from description
        if (!empty($data['experienceAndVibe']['description'])) {
            $post_data['post_content'] = wp_kses_post($data['experienceAndVibe']['description']);
        }

        // Set excerpt from meta description
        if (!empty($data['meta_description'])) {
            $post_data['post_excerpt'] = sanitize_text_field($data['meta_description']);
        }

        // Set slug from ID
        if (!$existing_post) {
            $post_data['post_name'] = sanitize_title($data['id']);
        }

        return $post_data;
    }

    /**
     * Save custom fields from JSON data
     */
    private function save_custom_fields($post_id, $data) {
        // Save the original JSON ID
        update_post_meta($post_id, '_rooftop_id', sanitize_text_field($data['id']));

        // Basic info
        if (!empty($data['basicInfo']['address'])) {
            $address = $data['basicInfo']['address'];
            update_post_meta($post_id, '_rooftop_address', sanitize_text_field($address['street'] . ', ' . $address['city']));
            update_post_meta($post_id, '_rooftop_neighborhood', sanitize_text_field($address['neighborhood']));
            update_post_meta($post_id, '_rooftop_postal_code', sanitize_text_field($address['postalCode']));

            if (!empty($address['coordinates'])) {
                update_post_meta($post_id, '_rooftop_latitude', floatval($address['coordinates']['latitude']));
                update_post_meta($post_id, '_rooftop_longitude', floatval($address['coordinates']['longitude']));
            }

            if (!empty($address['googleMapsLink'])) {
                update_post_meta($post_id, '_rooftop_google_maps', esc_url($address['googleMapsLink']));
            }
        }

        // Contact info
        if (!empty($data['basicInfo']['contact'])) {
            $contact = $data['basicInfo']['contact'];
            if (!empty($contact['phone'])) {
                update_post_meta($post_id, '_rooftop_phone', sanitize_text_field($contact['phone']));
            }
            if (!empty($contact['email'])) {
                update_post_meta($post_id, '_rooftop_email', sanitize_email($contact['email']));
            }
        }

        // Host venue info
        if (!empty($data['hostVenue'])) {
            $host = $data['hostVenue'];
            update_post_meta($post_id, '_rooftop_host_name', sanitize_text_field($host['name']));
            update_post_meta($post_id, '_rooftop_host_type', sanitize_text_field($host['type']));
            if (!empty($host['website'])) {
                update_post_meta($post_id, '_rooftop_website', esc_url($host['website']));
            }
        }

        // Hours
        if (!empty($data['hours'])) {
            update_post_meta($post_id, '_rooftop_hours', wp_json_encode($data['hours']));
        }

        if (!empty($data['hoursDisplay'])) {
            update_post_meta($post_id, '_rooftop_opening_hours', sanitize_text_field($data['hoursDisplay']));
        }

        // Social media
        if (!empty($data['social_media'])) {
            foreach ($data['social_media'] as $platform => $url) {
                if (!empty($url)) {
                    update_post_meta($post_id, '_rooftop_social_' . $platform, esc_url($url));
                }
            }
        }

        // Price range
        if (!empty($data['practicalitiesForTourists']['priceRange']['indicator'])) {
            update_post_meta($post_id, '_rooftop_price_range', sanitize_text_field($data['practicalitiesForTourists']['priceRange']['indicator']));
        }

        // Dress code
        if (!empty($data['practicalitiesForTourists']['dressCode']['policy'])) {
            update_post_meta($post_id, '_rooftop_dress_code', sanitize_text_field($data['practicalitiesForTourists']['dressCode']['policy']));
        }

        // Transportation
        if (!empty($data['transportation'])) {
            update_post_meta($post_id, '_rooftop_transportation', wp_json_encode($data['transportation']));
        }

        // Accessibility
        if (!empty($data['accessibility'])) {
            $accessibility = $data['accessibility'];
            update_post_meta($post_id, '_rooftop_elevator_access', !empty($accessibility['elevatorAccessToRooftop']) ? 'yes' : 'no');
            update_post_meta($post_id, '_rooftop_wheelchair_accessible', !empty($accessibility['wheelchairAccessible']) ? 'yes' : 'no');
            update_post_meta($post_id, '_rooftop_service_animals', !empty($accessibility['serviceAnimalsAllowed']) ? 'yes' : 'no');
        }

        // Food and drinks
        if (!empty($data['foodAndDrinks'])) {
            $food = $data['foodAndDrinks'];
            if (!empty($food['menuType'])) {
                update_post_meta($post_id, '_rooftop_menu_type', sanitize_text_field($food['menuType']));
            }
            if (!empty($food['cuisineStyle'])) {
                update_post_meta($post_id, '_rooftop_cuisine_style', sanitize_text_field($food['cuisineStyle']));
            }

            // Dietary options
            update_post_meta($post_id, '_rooftop_vegetarian', !empty($food['vegetarian']) ? 'yes' : 'no');
            update_post_meta($post_id, '_rooftop_vegan', !empty($food['vegan']) ? 'yes' : 'no');
            update_post_meta($post_id, '_rooftop_gluten_free', !empty($food['gluten']) ? 'yes' : 'no');

            // Menu highlights
            if (!empty($food['menuHighlights'])) {
                update_post_meta($post_id, '_rooftop_menu_highlights', wp_json_encode($food['menuHighlights']));
            }
        }

        // Experience and vibe
        if (!empty($data['experienceAndVibe'])) {
            $experience = $data['experienceAndVibe'];

            if (!empty($experience['views'])) {
                update_post_meta($post_id, '_rooftop_views', wp_json_encode($experience['views']));
            }

            if (!empty($experience['bestFor'])) {
                update_post_meta($post_id, '_rooftop_best_for', wp_json_encode($experience['bestFor']));
            }

            if (!empty($experience['atmosphere'])) {
                update_post_meta($post_id, '_rooftop_atmosphere', wp_json_encode($experience['atmosphere']));
            }

            if (!empty($experience['music'])) {
                update_post_meta($post_id, '_rooftop_music', wp_json_encode($experience['music']));
            }

            if (!empty($experience['crowdProfile'])) {
                update_post_meta($post_id, '_rooftop_crowd_profile', wp_json_encode($experience['crowdProfile']));
            }
        }

        // Amenities
        if (!empty($data['amenities_specific_to_rooftop'])) {
            update_post_meta($post_id, '_rooftop_amenities_details', wp_json_encode($data['amenities_specific_to_rooftop']));
        }

        // Services offered
        if (!empty($data['services_offered_at_rooftop'])) {
            update_post_meta($post_id, '_rooftop_services_offered', wp_json_encode($data['services_offered_at_rooftop']));
        }

        // Specialties
        if (!empty($data['specialties_of_the_rooftop'])) {
            update_post_meta($post_id, '_rooftop_specialties_details', wp_json_encode($data['specialties_of_the_rooftop']));
        }

        // Awards
        if (!empty($data['awards'])) {
            update_post_meta($post_id, '_rooftop_awards', wp_json_encode($data['awards']));
        }
    }

    /**
     * Assign taxonomies based on JSON data
     */
    private function assign_taxonomies($post_id, $data) {
        $taxonomy_mapping = rooftop_get_taxonomy_mapping();

        // Ensure taxonomies are registered before trying to use them
        $this->ensure_taxonomies_registered();

        foreach ($taxonomy_mapping as $taxonomy => $config) {
            $terms = $this->extract_taxonomy_terms($data, $config['json_path']);

            // Special handling for dietary options
            if ($taxonomy === 'dietary_options') {
                $terms = $this->extract_dietary_options($data);
            }

            // Special handling for accessibility features
            if ($taxonomy === 'accessibility_features') {
                $terms = $this->extract_accessibility_features($data);
            }

            if (!empty($terms)) {
                $term_ids = array();

                foreach ($terms as $term_name) {
                    $term_id = $this->get_or_create_term($term_name, $taxonomy);
                    if ($term_id) {
                        $term_ids[] = $term_id;
                    }
                }

                if (!empty($term_ids)) {
                    wp_set_post_terms($post_id, $term_ids, $taxonomy);
                }
            }
        }
    }

    /**
     * Ensure taxonomies are registered
     */
    private function ensure_taxonomies_registered() {
        // Force registration of taxonomies if they don't exist
        if (!class_exists('Rooftop_Taxonomy_Manager')) {
            require_once ROOFTOP_IMPORTER_PLUGIN_DIR . 'includes/class-taxonomy-manager.php';
        }

        $taxonomy_manager = new Rooftop_Taxonomy_Manager();
        $taxonomy_manager->register_taxonomies();

        rooftop_importer_log('Ensured taxonomies are registered during import', 'info');
    }

    /**
     * Extract dietary options from JSON data
     */
    private function extract_dietary_options($data) {
        $dietary_options = array();

        if (!empty($data['foodAndDrinks'])) {
            $food = $data['foodAndDrinks'];

            if (!empty($food['vegetarian']) && $food['vegetarian']) {
                $dietary_options[] = 'Vegetarian Options';
            }

            if (!empty($food['vegan']) && $food['vegan']) {
                $dietary_options[] = 'Vegan Options';
            }

            if (!empty($food['gluten']) && $food['gluten']) {
                $dietary_options[] = 'Gluten-Free Options';
            }
        }

        return $dietary_options;
    }

    /**
     * Extract accessibility features from JSON data
     */
    private function extract_accessibility_features($data) {
        $accessibility_features = array();

        if (!empty($data['accessibility'])) {
            $accessibility = $data['accessibility'];

            if (!empty($accessibility['elevatorAccessToRooftop']) && $accessibility['elevatorAccessToRooftop']) {
                $accessibility_features[] = 'Elevator Access';
            }

            if (!empty($accessibility['wheelchairAccessible']) && $accessibility['wheelchairAccessible']) {
                $accessibility_features[] = 'Wheelchair Accessible';
            }

            if (!empty($accessibility['serviceAnimalsAllowed']) && $accessibility['serviceAnimalsAllowed']) {
                $accessibility_features[] = 'Service Animals Allowed';
            }

            if (!empty($accessibility['accessibleHotelParkingAllowed']) && $accessibility['accessibleHotelParkingAllowed']) {
                $accessibility_features[] = 'Accessible Parking';
            }

            if (!empty($accessibility['accessibleHotelRoomsAvailable']) && $accessibility['accessibleHotelRoomsAvailable']) {
                $accessibility_features[] = 'Accessible Rooms Available';
            }
        }

        return $accessibility_features;
    }

    /**
     * Extract terms from JSON data using dot notation path
     */
    private function extract_taxonomy_terms($data, $path) {
        $keys = explode('.', $path);
        $value = $data;

        foreach ($keys as $key) {
            if (!isset($value[$key])) {
                return array();
            }
            $value = $value[$key];
        }

        if (is_array($value)) {
            return array_filter($value, 'strlen');
        } elseif (is_string($value) && !empty($value)) {
            return array($value);
        }

        return array();
    }

    /**
     * Get or create taxonomy term
     */
    private function get_or_create_term($term_name, $taxonomy) {
        $term_name = trim($term_name);
        if (empty($term_name)) {
            return false;
        }

        $term = get_term_by('name', $term_name, $taxonomy);

        if (!$term) {
            $result = wp_insert_term($term_name, $taxonomy);
            if (is_wp_error($result)) {
                $this->add_error("Failed to create term '{$term_name}' in taxonomy '{$taxonomy}': " . $result->get_error_message());
                return false;
            }
            return $result['term_id'];
        }

        return $term->term_id;
    }

    /**
     * Handle featured image import
     */
    private function handle_featured_image($post_id, $data) {
        // This would be implemented to handle image imports
        // For now, we'll skip this functionality
        return;
    }

    /**
     * Add error message
     */
    private function add_error($message) {
        $this->import_results['errors']++;
        $this->import_results['messages'][] = array(
            'type' => 'error',
            'message' => $message
        );
        rooftop_importer_log($message, 'error');
    }

    /**
     * Add info message
     */
    private function add_message($message, $type = 'info') {
        $this->import_results['messages'][] = array(
            'type' => $type,
            'message' => $message
        );
    }

    /**
     * Get import results
     */
    public function get_import_results() {
        return $this->import_results;
    }

    /**
     * Reset import results
     */
    public function reset_results() {
        $this->import_results = array(
            'success' => 0,
            'errors' => 0,
            'skipped' => 0,
            'messages' => array()
        );
    }
}
