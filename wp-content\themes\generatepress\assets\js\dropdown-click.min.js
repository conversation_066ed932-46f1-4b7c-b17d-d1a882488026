(()=>{if("querySelector"in document&&"addEventListener"in window){var a=document.body,t=function(e,t){e.preventDefault(),e.stopPropagation();var e=(t=t||this).closest("li"),n=t.closest("nav").querySelectorAll("ul.toggled-on");if(n&&!t.closest("ul").classList.contains("toggled-on")&&!t.closest("li").classList.contains("sfHover"))for(var r=0;r<n.length;r++)n[r].classList.remove("toggled-on"),n[r].closest("li").classList.remove("sfHover");e.classList.toggle("sfHover"),a.classList.contains("dropdown-click-arrow")&&("false"!==(o=e.querySelector(".dropdown-menu-toggle")).getAttribute("aria-expanded")&&o.getAttribute("aria-expanded")?o.setAttribute("aria-expanded","false"):o.setAttribute("aria-expanded","true")),a.classList.contains("dropdown-click-menu-item")&&t.tagName&&"A"===t.tagName.toUpperCase()&&("false"!==t.getAttribute("aria-expanded")&&t.getAttribute("aria-expanded")?(t.setAttribute("aria-expanded","false"),t.setAttribute("aria-label",generatepressDropdownClick.openSubMenuLabel)):(t.setAttribute("aria-expanded","true"),t.setAttribute("aria-label",generatepressDropdownClick.closeSubMenuLabel)));var o=".children";e.querySelector(".sub-menu")&&(o=".sub-menu"),a.classList.contains("dropdown-click-menu-item")?t.parentNode.querySelector(o).classList.toggle("toggled-on"):a.classList.contains("dropdown-click-arrow")&&e.querySelector(o).classList.toggle("toggled-on")},e=document.querySelectorAll(".main-nav .menu-item-has-children > a");if(a.classList.contains("dropdown-click-menu-item"))for(r=0;r<e.length;r++)e[r].addEventListener("click",t,!0),e[r].addEventListener("keydown",function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),t(e,this))},!1);if(a.classList.contains("dropdown-click-arrow")){for(r=0;r<e.length;r++)"#"===e[r].getAttribute("href")&&e[r].classList.add("menu-item-dropdown-click");for(var n=document.querySelectorAll(".main-nav .menu-item-has-children > a .dropdown-menu-toggle"),r=0;r<n.length;r++)n[r].addEventListener("click",t,!1),n[r].addEventListener("keydown",function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),t(e,this))},!1);var o=document.querySelectorAll(".main-nav .menu-item-has-children > a.menu-item-dropdown-click");for(r=0;r<o.length;r++)o[r].addEventListener("click",t,!1),o[r].addEventListener("keydown",function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),t(e,this))},!1)}var s=function(){if(document.querySelector("nav ul .toggled-on")){var e=document.querySelectorAll("nav ul .toggled-on");for(r=0;r<e.length;r++)e[r].classList.remove("toggled-on"),e[r].closest(".sfHover").classList.remove("sfHover");if(a.classList.contains("dropdown-click-arrow")){var t=document.querySelectorAll("nav .dropdown-menu-toggle");for(r=0;r<t.length;r++)t[r].setAttribute("aria-expanded","false")}if(a.classList.contains("dropdown-click-menu-item")){var n=document.querySelectorAll("nav .menu-item-has-children > a");for(r=0;r<n.length;r++)n[r].setAttribute("aria-expanded","false"),n[r].setAttribute("aria-label",generatepressDropdownClick.openSubMenuLabel)}}};document.addEventListener("click",function(e){e.target.closest(".sfHover")||s()},!1),document.addEventListener("keydown",function(e){"Escape"===e.key&&s()},!1)}})();