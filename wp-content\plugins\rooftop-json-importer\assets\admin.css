/**
 * Admin CSS for Rooftop JSON Importer
 */

.rooftop-importer-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.rooftop-importer-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rooftop-importer-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rooftop-import-form {
    margin-top: 20px;
}

.rooftop-import-form .form-table th {
    width: 150px;
    padding-left: 0;
}

.rooftop-import-form input[type="file"] {
    width: 100%;
    max-width: 400px;
}

/* JSON Example Styling */
.card pre {
    background: #f6f7f7;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
    max-height: 300px;
}

.card pre code {
    background: none;
    padding: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    color: #333;
}

/* Quick Stats Styling */
.rooftop-importer-sidebar .card ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.rooftop-importer-sidebar .card ul li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

.rooftop-importer-sidebar .card ul li:last-child {
    border-bottom: none;
}

/* Import Tips Styling */
.rooftop-importer-sidebar .card ul li {
    padding: 5px 0;
    border-bottom: none;
    display: list-item;
    list-style: disc inside;
}

/* Log Types */
.log-type {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.log-type-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.log-type-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.log-type-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.log-type-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Progress Bar */
.rooftop-import-progress {
    display: none;
    margin: 20px 0;
}

.rooftop-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.rooftop-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.rooftop-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: bold;
    color: #333;
    z-index: 2;
}

/* Import Results */
.rooftop-import-results {
    margin: 20px 0;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #0073aa;
    background: #f7f7f7;
}

.rooftop-import-results.success {
    border-left-color: #46b450;
    background: #f0f8f0;
}

.rooftop-import-results.error {
    border-left-color: #dc3232;
    background: #fdf0f0;
}

.rooftop-import-results h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.rooftop-import-results ul {
    margin: 0;
    padding-left: 20px;
}

.rooftop-import-results ul li {
    margin: 5px 0;
    font-size: 13px;
}

/* File Upload Area */
.rooftop-file-upload-area {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.rooftop-file-upload-area:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.rooftop-file-upload-area.dragover {
    border-color: #46b450;
    background: #f0f8f0;
}

.rooftop-file-upload-area .upload-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.rooftop-file-upload-area .upload-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.rooftop-file-upload-area .upload-hint {
    font-size: 12px;
    color: #999;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .rooftop-importer-container {
        grid-template-columns: 1fr;
    }
    
    .rooftop-importer-sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .rooftop-importer-container {
        gap: 15px;
    }
    
    .rooftop-importer-main,
    .rooftop-importer-sidebar {
        gap: 15px;
    }
    
    .card pre {
        font-size: 11px;
        padding: 10px;
    }
    
    .rooftop-file-upload-area {
        padding: 20px;
    }
    
    .rooftop-file-upload-area .upload-icon {
        font-size: 36px;
    }
}

/* Loading States */
.rooftop-importing {
    opacity: 0.6;
    pointer-events: none;
}

.rooftop-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: rooftop-spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes rooftop-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Taxonomy Preview */
.rooftop-taxonomy-preview {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
}

.rooftop-taxonomy-preview h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
    font-size: 14px;
}

.rooftop-taxonomy-preview .taxonomy-terms {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.rooftop-taxonomy-preview .term-tag {
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 3px 8px;
    font-size: 11px;
    color: #666;
}

.rooftop-taxonomy-preview .term-tag.new {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* Settings Page */
.rooftop-settings-section {
    margin: 30px 0;
}

.rooftop-settings-section h3 {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* Validation Messages */
.rooftop-validation-message {
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 13px;
}

.rooftop-validation-message.error {
    background: #fdf0f0;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.rooftop-validation-message.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.rooftop-validation-message.success {
    background: #f0f8f0;
    border: 1px solid #c3e6cb;
    color: #155724;
}

/* Button Enhancements */
.button.rooftop-button-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
    text-shadow: none;
    box-shadow: none;
}

.button.rooftop-button-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

.button.rooftop-button-secondary {
    background: #f7f7f7;
    border-color: #ccc;
    color: #555;
}

.button.rooftop-button-secondary:hover {
    background: #fafafa;
    border-color: #999;
}

/* Help Text */
.rooftop-help-text {
    font-style: italic;
    color: #666;
    font-size: 12px;
    margin-top: 5px;
}

/* Card Enhancements */
.card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e5e5e5;
}

.card h2, .card h3 {
    color: #23282d;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* Table Enhancements */
.wp-list-table.rooftop-logs-table {
    border: 1px solid #e1e1e1;
}

.wp-list-table.rooftop-logs-table th {
    background: #f9f9f9;
    font-weight: 600;
}

.wp-list-table.rooftop-logs-table td {
    vertical-align: top;
    padding: 12px 10px;
}

/* Utility Classes */
.rooftop-text-center { text-align: center; }
.rooftop-text-right { text-align: right; }
.rooftop-text-left { text-align: left; }

.rooftop-mb-0 { margin-bottom: 0; }
.rooftop-mb-10 { margin-bottom: 10px; }
.rooftop-mb-20 { margin-bottom: 20px; }

.rooftop-mt-0 { margin-top: 0; }
.rooftop-mt-10 { margin-top: 10px; }
.rooftop-mt-20 { margin-top: 20px; }

.rooftop-hidden { display: none; }
.rooftop-visible { display: block; }
