# Rooftop JSON Importer

A WordPress plugin for importing rooftop data from JSON files into the Rooftops Barcelona directory website.

## Features

- **JSON File Import**: Upload and import rooftop data from structured JSON files
- **Comprehensive Taxonomy Support**: Automatically creates and assigns terms for all rooftop taxonomies
- **Custom Field Mapping**: Maps JSON data to WordPress custom fields for detailed rooftop information
- **Duplicate Detection**: Prevents duplicate imports and optionally updates existing rooftops
- **Admin Interface**: User-friendly WordPress admin interface for managing imports
- **Import Logging**: Detailed logs of import activities for troubleshooting
- **Validation**: Validates JSON structure and data before import

## Supported Taxonomies

The plugin supports the following taxonomies based on your rooftop directory requirements:

### Location-Based
- **Neighborhoods**: Barcelona neighborhoods (Eixample, Barri Gòtic, etc.)

### Experience & Vibe
- **Atmosphere**: Romantic, Lively, Chic, Sophisticated, etc.
- **Best For**: Sunset Views, Date Night, Groups, Business Meetings, etc.
- **View Types**: Panoramic City Views, Sea View, Landmark Views, etc.
- **Music Styles**: Live DJ, Chillout, Live Music, Electronic, etc.

### Features & Amenities
- **Amenities**: Pool Access, Covered Terrace, Heating, WiFi, etc.
- **Accessibility Features**: Wheelchair Accessible, Elevator Access, etc.

### Food & Drink
- **Menu Types**: Cocktail Focused, Tapas & Small Plates, Full Restaurant, etc.
- **Cuisine Styles**: Mediterranean, Spanish, International, Asian Fusion, etc.
- **Dietary Options**: Vegetarian, Vegan, Gluten-Free options

### Practical Information
- **Price Ranges**: $, $$, $$$
- **Dress Codes**: Casual, Smart Casual, Elegant
- **Venue Types**: Hotel Rooftop, Standalone Bar

## JSON Structure

Your JSON files should follow this structure:

```json
{
  "id": "BCN_ROOFTOP_EXAMPLE_001",
  "name": "Example Rooftop Bar",
  "hostVenue": {
    "name": "Example Hotel",
    "type": "Hotel",
    "website": "https://example.com"
  },
  "basicInfo": {
    "address": {
      "street": "Example Street, 123",
      "city": "Barcelona",
      "postalCode": "08001",
      "neighborhood": "Eixample",
      "country": "Spain",
      "coordinates": {
        "latitude": 41.3851,
        "longitude": 2.1734
      }
    },
    "contact": {
      "phone": "+34 123 456 789",
      "email": "<EMAIL>"
    }
  },
  "experienceAndVibe": {
    "description": "Description of the rooftop...",
    "views": ["City Skyline", "Sea View"],
    "bestFor": ["Sunset Cocktails", "Date Night"],
    "atmosphere": ["Romantic", "Sophisticated"]
  },
  "popular": ["360 Views", "Cocktails"],
  "services": ["Cocktail Bar", "Restaurant"],
  "amenities": ["Covered Terrace", "Heating"],
  "specialties": ["Signature Cocktails", "Tapas"]
}
```

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to **Rooftops > Import JSON** in your WordPress admin

## Usage

### Basic Import

1. Go to **Rooftops > Import JSON**
2. Select your JSON file
3. Click "Import JSON File"
4. Review the import results

### Settings

Configure import behavior in **Rooftops > Import Settings**:

- **Auto-create Taxonomies**: Automatically create taxonomy terms that don't exist
- **Update Existing Posts**: Update existing rooftops if they already exist
- **Import Images**: Import and set featured images from URLs (future feature)
- **Log Imports**: Keep detailed logs of import activities

### Viewing Logs

Check import logs in **Rooftops > Import Logs** to:
- Review successful imports
- Troubleshoot errors
- Monitor import activity

## Custom Fields Mapping

The plugin maps JSON data to the following custom fields:

### Basic Information
- `_rooftop_id`: Original JSON ID
- `_rooftop_address`: Full address
- `_rooftop_neighborhood`: Neighborhood
- `_rooftop_postal_code`: Postal code
- `_rooftop_latitude`: GPS latitude
- `_rooftop_longitude`: GPS longitude
- `_rooftop_google_maps`: Google Maps link

### Contact Information
- `_rooftop_phone`: Phone number
- `_rooftop_email`: Email address
- `_rooftop_website`: Website URL

### Host Venue
- `_rooftop_host_name`: Host venue name
- `_rooftop_host_type`: Host venue type

### Operating Information
- `_rooftop_hours`: Operating hours (JSON)
- `_rooftop_opening_hours`: Display hours text
- `_rooftop_price_range`: Price range indicator
- `_rooftop_dress_code`: Dress code policy

### Social Media
- `_rooftop_social_instagram`: Instagram URL
- `_rooftop_social_facebook`: Facebook URL
- `_rooftop_social_pinterest`: Pinterest URL
- `_rooftop_social_tiktok`: TikTok URL
- `_rooftop_social_youtube`: YouTube URL
- `_rooftop_social_x`: X (Twitter) URL

### Experience Details
- `_rooftop_views`: Available views (JSON)
- `_rooftop_best_for`: Best suited for (JSON)
- `_rooftop_atmosphere`: Atmosphere types (JSON)
- `_rooftop_music`: Music information (JSON)
- `_rooftop_crowd_profile`: Crowd profile (JSON)

### Accessibility
- `_rooftop_elevator_access`: Elevator access (yes/no)
- `_rooftop_wheelchair_accessible`: Wheelchair accessible (yes/no)
- `_rooftop_service_animals`: Service animals allowed (yes/no)

### Food & Drinks
- `_rooftop_menu_type`: Menu type
- `_rooftop_cuisine_style`: Cuisine style
- `_rooftop_vegetarian`: Vegetarian options (yes/no)
- `_rooftop_vegan`: Vegan options (yes/no)
- `_rooftop_gluten_free`: Gluten-free options (yes/no)
- `_rooftop_menu_highlights`: Menu highlights (JSON)

### Additional Details
- `_rooftop_transportation`: Transportation info (JSON)
- `_rooftop_amenities_details`: Detailed amenities (JSON)
- `_rooftop_services_offered`: Services offered (JSON)
- `_rooftop_specialties_details`: Specialties details (JSON)
- `_rooftop_awards`: Awards (JSON)

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- Rooftops custom post type (provided by theme)

## Support

For support and questions, please contact the development team.

## Changelog

### Version 1.0.0
- Initial release
- JSON file import functionality
- Comprehensive taxonomy support
- Custom field mapping
- Admin interface
- Import logging
- Validation and error handling

## License

This plugin is licensed under the GPL v2 or later.
