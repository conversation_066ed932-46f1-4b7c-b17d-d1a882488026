/*
Theme Name: GeneratePress Child - Rooftops Barcelona
Description: Child theme for Rooftops Barcelona directory website
Author: Rooftops Barcelona
Template: generatepress
Version: 1.0.0
Text Domain: generatepress-child
*/

/* Import parent theme styles */
@import url("../generatepress/style.css");

/* Custom styles for Rooftops Barcelona */

/* Homepage Container */
.rooftops-homepage {
    width: 100%;
    overflow-x: hidden;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1539037116277-4db20889f2d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-bottom: 60px;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    max-width: 600px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.hero-btn {
    background: #e74c3c;
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: background 0.3s ease;
}

.hero-btn:hover {
    background: #c0392b;
    color: white;
    text-decoration: none;
}

/* Section Styles */
.section {
    padding: 60px 0;
    width: 100%;
    clear: both;
    display: block;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Card Grid Styles */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.card-content {
    padding: 25px;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2c3e50;
}

.card-description {
    color: #7f8c8d;
    line-height: 1.6;
    margin-bottom: 15px;
}

.card-link {
    color: #e74c3c;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.card-link:hover {
    color: #c0392b;
    text-decoration: none;
}

/* Rooftop Cards (Featured Section) */
.rooftop-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.rooftop-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.rooftop-image {
    height: 250px;
    background-size: cover;
    background-position: center;
}

.rooftop-info {
    padding: 20px;
}

.rooftop-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.rooftop-address {
    color: #7f8c8d;
    font-size: 0.95rem;
    margin-bottom: 10px;
}

.rooftop-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.stars {
    color: #f39c12;
}

/* CTA Section */
.cta-section {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 80px 0;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-btn {
    background: #e74c3c;
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: background 0.3s ease;
    margin: 0 10px;
}

.cta-btn:hover {
    background: #c0392b;
    color: white;
    text-decoration: none;
}

.cta-btn.secondary {
    background: transparent;
    border: 2px solid white;
}

.cta-btn.secondary:hover {
    background: white;
    color: #2c3e50;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
    box-sizing: border-box;
}

/* Override theme defaults that might cause layout issues */
.rooftops-homepage .section {
    float: none !important;
    width: 100% !important;
    display: block !important;
    position: relative !important;
}

.rooftops-homepage .container {
    float: none !important;
    width: 100% !important;
    max-width: 1200px !important;
}

/* Additional layout fixes */
.rooftops-homepage {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Ensure sections stack vertically */
.rooftops-homepage .section {
    clear: both !important;
    margin: 0 !important;
    padding: 60px 0 !important;
}

/* Fix any grid issues */
.cards-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 30px !important;
    margin-bottom: 40px !important;
}

/* Ensure hero section displays properly */
.hero-section {
    width: 100% !important;
    height: 60vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-btn {
        display: block;
        margin: 10px auto;
        width: fit-content;
    }
}

/* Navigation Enhancements */
.main-navigation {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.main-navigation a {
    color: #2c3e50;
    font-weight: 500;
}

.main-navigation a:hover {
    color: #e74c3c;
}

/* Footer Enhancements */
.site-footer {
    background: #2c3e50;
    color: white;
    padding: 40px 0 20px;
}

.site-footer a {
    color: #ecf0f1;
}

.site-footer a:hover {
    color: #e74c3c;
}
