<?php
/**
 * Single Rooftop Template
 */

get_header(); ?>

<?php while (have_posts()) : the_post(); ?>
    <article class="rooftop-single" style="padding: 0;">
        <!-- Hero Image Section -->
        <div class="rooftop-hero" style="height: 50vh; background-size: cover; background-position: center; position: relative; background-image: url('<?php echo has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'full') : 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'; ?>');">
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5));"></div>
            <div class="hero-content" style="position: absolute; bottom: 40px; left: 0; right: 0; text-align: center; color: white;">
                <div class="container">
                    <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.7);">
                        <?php the_title(); ?>
                    </h1>
                    <?php 
                    $address = get_post_meta(get_the_ID(), '_rooftop_address', true);
                    if ($address) : ?>
                        <p style="font-size: 1.2rem; margin: 0; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">
                            📍 <?php echo esc_html($address); ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="container" style="padding: 60px 20px;">
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 60px; align-items: start;">
                
                <!-- Left Column - Main Content -->
                <div class="rooftop-main">
                    <!-- Quick Info Bar -->
                    <div class="quick-info" style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 40px; display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; text-align: center;">
                        <?php 
                        $rating = get_post_meta(get_the_ID(), '_rooftop_rating', true);
                        $price_range = get_post_meta(get_the_ID(), '_rooftop_price_range', true);
                        $phone = get_post_meta(get_the_ID(), '_rooftop_phone', true);
                        $website = get_post_meta(get_the_ID(), '_rooftop_website', true);
                        
                        if ($rating) : ?>
                            <div>
                                <div style="font-size: 1.5rem; color: #f39c12; margin-bottom: 5px;">
                                    <?php
                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= floor($rating)) {
                                            echo '★';
                                        } elseif ($i - 0.5 <= $rating) {
                                            echo '☆';
                                        } else {
                                            echo '☆';
                                        }
                                    }
                                    ?>
                                </div>
                                <div style="font-weight: 600; color: #2c3e50;"><?php echo esc_html($rating); ?>/5 Rating</div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($price_range) : ?>
                            <div>
                                <div style="font-size: 1.5rem; color: #e74c3c; margin-bottom: 5px; font-weight: 700;">
                                    <?php echo esc_html($price_range); ?>
                                </div>
                                <div style="font-weight: 600; color: #2c3e50;">Price Range</div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($phone) : ?>
                            <div>
                                <div style="font-size: 1.2rem; color: #2c3e50; margin-bottom: 5px;">📞</div>
                                <div style="font-weight: 600; color: #2c3e50;">
                                    <a href="tel:<?php echo esc_attr($phone); ?>" style="color: inherit; text-decoration: none;">
                                        <?php echo esc_html($phone); ?>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($website) : ?>
                            <div>
                                <div style="font-size: 1.2rem; color: #2c3e50; margin-bottom: 5px;">🌐</div>
                                <div style="font-weight: 600;">
                                    <a href="<?php echo esc_url($website); ?>" target="_blank" style="color: #e74c3c; text-decoration: none;">
                                        Visit Website
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Description -->
                    <div class="rooftop-description" style="margin-bottom: 40px;">
                        <h2 style="font-size: 2rem; color: #2c3e50; margin-bottom: 20px;">About This Rooftop</h2>
                        <div style="font-size: 1.1rem; line-height: 1.8; color: #555;">
                            <?php 
                            if (has_excerpt()) {
                                the_excerpt();
                            }
                            the_content(); 
                            ?>
                        </div>
                    </div>

                    <!-- Features & Amenities -->
                    <div class="rooftop-features" style="margin-bottom: 40px;">
                        <h2 style="font-size: 2rem; color: #2c3e50; margin-bottom: 20px;">Features & Amenities</h2>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
                            <?php
                            $popular_features = get_the_terms(get_the_ID(), 'popular_features');
                            $services = get_the_terms(get_the_ID(), 'services');
                            $amenities = get_the_terms(get_the_ID(), 'amenities');
                            
                            if ($popular_features) : ?>
                                <div>
                                    <h3 style="font-size: 1.2rem; color: #e74c3c; margin-bottom: 15px; font-weight: 600;">Popular Features</h3>
                                    <ul style="list-style: none; padding: 0; margin: 0;">
                                        <?php foreach ($popular_features as $feature) : ?>
                                            <li style="padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                                                <a href="<?php echo get_term_link($feature); ?>" style="color: #2c3e50; text-decoration: none;">
                                                    ✓ <?php echo esc_html($feature->name); ?>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($services) : ?>
                                <div>
                                    <h3 style="font-size: 1.2rem; color: #e74c3c; margin-bottom: 15px; font-weight: 600;">Services</h3>
                                    <ul style="list-style: none; padding: 0; margin: 0;">
                                        <?php foreach ($services as $service) : ?>
                                            <li style="padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                                                <a href="<?php echo get_term_link($service); ?>" style="color: #2c3e50; text-decoration: none;">
                                                    ✓ <?php echo esc_html($service->name); ?>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($amenities) : ?>
                                <div>
                                    <h3 style="font-size: 1.2rem; color: #e74c3c; margin-bottom: 15px; font-weight: 600;">Amenities</h3>
                                    <ul style="list-style: none; padding: 0; margin: 0;">
                                        <?php foreach ($amenities as $amenity) : ?>
                                            <li style="padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                                                <a href="<?php echo get_term_link($amenity); ?>" style="color: #2c3e50; text-decoration: none;">
                                                    ✓ <?php echo esc_html($amenity->name); ?>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Sidebar -->
                <div class="rooftop-sidebar">
                    <!-- Opening Hours -->
                    <?php 
                    $opening_hours = get_post_meta(get_the_ID(), '_rooftop_opening_hours', true);
                    if ($opening_hours) : ?>
                        <div class="info-box" style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                            <h3 style="font-size: 1.3rem; color: #2c3e50; margin-bottom: 15px; font-weight: 600;">Opening Hours</h3>
                            <div style="white-space: pre-line; color: #555; line-height: 1.6;">
                                <?php echo esc_html($opening_hours); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Location -->
                    <?php if ($address) : ?>
                        <div class="info-box" style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                            <h3 style="font-size: 1.3rem; color: #2c3e50; margin-bottom: 15px; font-weight: 600;">Location</h3>
                            <p style="color: #555; margin-bottom: 15px; line-height: 1.6;">
                                <?php echo esc_html($address); ?>
                            </p>
                            
                            <!-- Neighborhood -->
                            <?php 
                            $neighborhoods = get_the_terms(get_the_ID(), 'neighborhoods');
                            if ($neighborhoods) : ?>
                                <div style="margin-top: 15px;">
                                    <strong style="color: #2c3e50;">Neighborhood:</strong>
                                    <?php foreach ($neighborhoods as $neighborhood) : ?>
                                        <a href="<?php echo get_term_link($neighborhood); ?>" style="color: #e74c3c; text-decoration: none; margin-left: 5px;">
                                            <?php echo esc_html($neighborhood->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Contact Info -->
                    <div class="info-box" style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                        <h3 style="font-size: 1.3rem; color: #2c3e50; margin-bottom: 15px; font-weight: 600;">Contact</h3>
                        
                        <?php if ($phone) : ?>
                            <p style="margin-bottom: 10px;">
                                <strong>Phone:</strong> 
                                <a href="tel:<?php echo esc_attr($phone); ?>" style="color: #e74c3c; text-decoration: none;">
                                    <?php echo esc_html($phone); ?>
                                </a>
                            </p>
                        <?php endif; ?>
                        
                        <?php if ($website) : ?>
                            <p style="margin-bottom: 10px;">
                                <strong>Website:</strong> 
                                <a href="<?php echo esc_url($website); ?>" target="_blank" style="color: #e74c3c; text-decoration: none;">
                                    Visit Website
                                </a>
                            </p>
                        <?php endif; ?>
                        
                        <div style="margin-top: 20px;">
                            <a href="<?php echo esc_url($website ?: '#'); ?>" target="_blank" style="background: #e74c3c; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; font-weight: 600; display: inline-block; width: 100%; text-align: center;">
                                Make Reservation
                            </a>
                        </div>
                    </div>

                    <!-- Share -->
                    <div class="info-box" style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        <h3 style="font-size: 1.3rem; color: #2c3e50; margin-bottom: 15px; font-weight: 600;">Share This Rooftop</h3>
                        <div style="display: flex; gap: 10px;">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" style="background: #3b5998; color: white; padding: 10px; border-radius: 5px; text-decoration: none; flex: 1; text-align: center;">
                                Facebook
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" style="background: #1da1f2; color: white; padding: 10px; border-radius: 5px; text-decoration: none; flex: 1; text-align: center;">
                                Twitter
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Rooftops -->
        <section style="background: #f8f9fa; padding: 60px 0;">
            <div class="container">
                <h2 style="font-size: 2.5rem; color: #2c3e50; text-align: center; margin-bottom: 40px;">Similar Rooftops</h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                    <?php
                    // Get related rooftops based on shared taxonomies
                    $related_args = array(
                        'post_type' => 'rooftops',
                        'posts_per_page' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'tax_query' => array(
                            'relation' => 'OR',
                        )
                    );
                    
                    // Add tax queries for shared terms
                    if ($neighborhoods) {
                        $related_args['tax_query'][] = array(
                            'taxonomy' => 'neighborhoods',
                            'field' => 'term_id',
                            'terms' => wp_list_pluck($neighborhoods, 'term_id'),
                        );
                    }
                    
                    $related_query = new WP_Query($related_args);
                    
                    if ($related_query->have_posts()) :
                        while ($related_query->have_posts()) : $related_query->the_post();
                            $related_address = get_post_meta(get_the_ID(), '_rooftop_address', true);
                            $related_rating = get_post_meta(get_the_ID(), '_rooftop_rating', true);
                            ?>
                            <div class="related-rooftop" style="background: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s ease;">
                                <a href="<?php the_permalink(); ?>" style="text-decoration: none; color: inherit;">
                                    <div style="height: 200px; background-size: cover; background-position: center; background-image: url('<?php echo has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'medium') : 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'; ?>');"></div>
                                    <div style="padding: 20px;">
                                        <h3 style="font-size: 1.2rem; color: #2c3e50; margin-bottom: 8px;"><?php the_title(); ?></h3>
                                        <?php if ($related_address) : ?>
                                            <p style="color: #7f8c8d; font-size: 0.9rem; margin-bottom: 10px;">📍 <?php echo esc_html($related_address); ?></p>
                                        <?php endif; ?>
                                        <?php if ($related_rating) : ?>
                                            <div style="color: #f39c12; font-size: 0.9rem;">
                                                <?php
                                                for ($i = 1; $i <= 5; $i++) {
                                                    echo $i <= floor($related_rating) ? '★' : '☆';
                                                }
                                                echo ' ' . $related_rating;
                                                ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </a>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </div>
        </section>
    </article>
<?php endwhile; ?>

<style>
@media (max-width: 768px) {
    .container > div {
        grid-template-columns: 1fr !important;
        gap: 40px !important;
    }
    
    .quick-info {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }
    
    .rooftop-features > div {
        grid-template-columns: 1fr !important;
    }
    
    .hero-content h1 {
        font-size: 2rem !important;
    }
}

.related-rooftop:hover {
    transform: translateY(-5px);
}
</style>

<?php get_footer(); ?>
