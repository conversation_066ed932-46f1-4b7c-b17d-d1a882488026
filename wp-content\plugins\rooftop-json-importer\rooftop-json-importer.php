<?php
/**
 * Plugin Name: Rooftop JSON Importer
 * Plugin URI: https://rooftopsbarcelona.com
 * Description: Import rooftop data from JSON files into WordPress custom post types with taxonomies
 * Version: 1.0.0
 * Author: Rooftops Barcelona
 * License: GPL v2 or later
 * Text Domain: rooftop-json-importer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('ROOFTOP_IMPORTER_VERSION', '1.0.0');
define('ROOFTOP_IMPORTER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ROOFTOP_IMPORTER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ROOFTOP_IMPORTER_PLUGIN_FILE', __FILE__);

/**
 * Main plugin class
 */
class Rooftop_JSON_Importer {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('plugins_loaded', array($this, 'early_init'), 5);
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Early initialization - load taxonomies
     */
    public function early_init() {
        // Load taxonomy manager early
        require_once ROOFTOP_IMPORTER_PLUGIN_DIR . 'includes/class-taxonomy-manager.php';
        new Rooftop_Taxonomy_Manager();
    }

    /**
     * Initialize plugin
     */
    public function init() {
        // Load required files
        $this->load_dependencies();

        // Initialize taxonomy manager FIRST (before admin)
        new Rooftop_Taxonomy_Manager();

        // Initialize admin interface if in admin
        if (is_admin()) {
            new Rooftop_Importer_Admin();
        }
    }

    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once ROOFTOP_IMPORTER_PLUGIN_DIR . 'includes/class-taxonomy-manager.php';
        require_once ROOFTOP_IMPORTER_PLUGIN_DIR . 'includes/class-importer.php';

        if (is_admin()) {
            require_once ROOFTOP_IMPORTER_PLUGIN_DIR . 'admin/class-admin.php';
        }
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'rooftop-json-importer',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create upload directory for JSON files
        $upload_dir = wp_upload_dir();
        $rooftop_dir = $upload_dir['basedir'] . '/rooftop-imports';

        if (!file_exists($rooftop_dir)) {
            wp_mkdir_p($rooftop_dir);
        }

        // Create .htaccess to protect directory
        $htaccess_file = $rooftop_dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            file_put_contents($htaccess_file, "deny from all\n");
        }

        // Set default options
        add_option('rooftop_importer_settings', array(
            'auto_create_taxonomies' => true,
            'update_existing_posts' => false,
            'import_images' => true,
            'log_imports' => true
        ));

        // Initialize taxonomy manager to register taxonomies
        if (class_exists('Rooftop_Taxonomy_Manager')) {
            new Rooftop_Taxonomy_Manager();
        }

        // Flush rewrite rules
        flush_rewrite_rules();

        // Log activation
        rooftop_importer_log('Rooftop JSON Importer plugin activated', 'info');
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up temporary files older than 7 days
        $upload_dir = wp_upload_dir();
        $rooftop_dir = $upload_dir['basedir'] . '/rooftop-imports';

        if (is_dir($rooftop_dir)) {
            $files = glob($rooftop_dir . '/*');
            $now = time();

            foreach ($files as $file) {
                if (is_file($file) && ($now - filemtime($file)) >= 7 * 24 * 3600) {
                    unlink($file);
                }
            }
        }
    }
}

/**
 * Initialize the plugin
 */
function rooftop_json_importer() {
    return Rooftop_JSON_Importer::get_instance();
}

// Start the plugin
rooftop_json_importer();

/**
 * Helper function to log import activities
 */
function rooftop_importer_log($message, $type = 'info') {
    $log_entry = array(
        'timestamp' => current_time('mysql'),
        'type' => $type,
        'message' => $message
    );

    $logs = get_option('rooftop_importer_logs', array());
    if (!is_array($logs)) {
        $logs = array();
    }

    $logs[] = $log_entry;

    // Keep only last 100 log entries
    if (count($logs) > 100) {
        $logs = array_slice($logs, -100);
    }

    update_option('rooftop_importer_logs', $logs);

    // Also log to PHP error log for debugging
    error_log("Rooftop Importer [{$type}]: {$message}");
}

/**
 * Helper function to get taxonomy mapping
 */
function rooftop_get_taxonomy_mapping() {
    return array(
        // Location-Based Taxonomies
        'neighborhoods' => array(
            'slug' => 'neighborhoods',
            'json_path' => 'basicInfo.address.neighborhood',
            'hierarchical' => true
        ),

        // Experience & Vibe Taxonomies
        'atmosphere' => array(
            'slug' => 'atmosphere',
            'json_path' => 'experienceAndVibe.atmosphere',
            'hierarchical' => false
        ),
        'best_for' => array(
            'slug' => 'best-for',
            'json_path' => 'experienceAndVibe.bestFor',
            'hierarchical' => false
        ),
        'view_type' => array(
            'slug' => 'view-type',
            'json_path' => 'experienceAndVibe.views',
            'hierarchical' => false
        ),
        'music_style' => array(
            'slug' => 'music-style',
            'json_path' => 'experienceAndVibe.music.type',
            'hierarchical' => false
        ),

        // Features & Amenities Taxonomies
        'amenities' => array(
            'slug' => 'amenities',
            'json_path' => 'amenities',
            'hierarchical' => false
        ),
        'accessibility_features' => array(
            'slug' => 'accessibility-features',
            'json_path' => 'accessibility',
            'hierarchical' => false
        ),

        // Food & Drink Taxonomies
        'menu_type' => array(
            'slug' => 'menu-type',
            'json_path' => 'foodAndDrinks.menuType',
            'hierarchical' => false
        ),
        'cuisine_style' => array(
            'slug' => 'cuisine-style',
            'json_path' => 'foodAndDrinks.cuisineStyle',
            'hierarchical' => false
        ),
        'dietary_options' => array(
            'slug' => 'dietary-options',
            'json_path' => 'foodAndDrinks.dietary',
            'hierarchical' => false
        ),

        // Practical Taxonomies
        'price_range' => array(
            'slug' => 'price-range',
            'json_path' => 'practicalitiesForTourists.priceRange.indicator',
            'hierarchical' => false
        ),
        'dress_code' => array(
            'slug' => 'dress-code',
            'json_path' => 'practicalitiesForTourists.dressCode.policy',
            'hierarchical' => false
        ),
        'venue_type' => array(
            'slug' => 'venue-type',
            'json_path' => 'hostVenue.type',
            'hierarchical' => false
        ),

        // Legacy taxonomies (for backward compatibility)
        'popular_features' => array(
            'slug' => 'popular',
            'json_path' => 'popular',
            'hierarchical' => false
        ),
        'services' => array(
            'slug' => 'services',
            'json_path' => 'services',
            'hierarchical' => false
        ),
        'specialties' => array(
            'slug' => 'specialties',
            'json_path' => 'specialties',
            'hierarchical' => false
        )
    );
}
