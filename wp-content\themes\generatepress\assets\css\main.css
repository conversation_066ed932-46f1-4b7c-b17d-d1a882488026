/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Reset
# Elements
# Forms
# Links
# Alignments
# Accessibility
# Navigation
    # Dropdown Menus
    # Sidebar Navigation
# Post Navigation
# Header
# Post Content
# Widgets
# Content Layout
# Sidebars
# Footer
# Featured Images
# Icons
# Compatibility
# Mobile
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Reset
--------------------------------------------------------------*/
html,
body,
p,
ol,
ul,
li,
dl,
dt,
dd,
blockquote,
figure,
fieldset,
legend,
textarea,
pre,
iframe,
hr,
h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0;
	padding: 0;
	border: 0;
}

html {
	font-family: sans-serif;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
	display: block;
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
	vertical-align: baseline;
}

/* Inherit box-sizing to more easily change it's value on a component level.
@link http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/ */
html {
	box-sizing: border-box;
}

*,
*::before,
*::after {
	box-sizing: inherit;
}

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
*/
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	font-size: 100%;
	margin: 0;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}

/**
  * Remove the inner padding in Chrome and Safari on macOS.
  */
[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

/**
 * Remove the inner border and padding in Firefox.
 */
::-moz-focus-inner {
	border-style: none;
	padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
:-moz-focusring {
	outline: 1px dotted ButtonText;
}

/*--------------------------------------------------------------
# Elements
--------------------------------------------------------------*/
body,
button,
input,
select,
textarea {
	font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
	font-weight: normal;
	text-transform: none;
	font-size: 17px;
	line-height: 1.5;
}

p {
	margin-bottom: 1.5em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: inherit;
	font-size: 100%;
	font-style: inherit;
	font-weight: inherit;
}

pre {
	background: rgba(0, 0, 0, 0.05);
	font-family: inherit;
	font-size: inherit;
	line-height: normal;
	margin-bottom: 1.5em;
	padding: 20px;
	overflow: auto;
	max-width: 100%;
}

blockquote {
	border-left: 5px solid rgba(0, 0, 0, 0.05);
	padding: 20px;
	font-size: 1.2em;
	font-style: italic;
	margin: 0 0 1.5em;
	position: relative;
}

blockquote p:last-child {
	margin: 0;
}

table,
th,
td {
	border: 1px solid rgba(0, 0, 0, 0.1);
}

table {
	border-collapse: separate;
	border-spacing: 0;
	border-width: 1px 0 0 1px;
	margin: 0 0 1.5em;
	width: 100%;
}

th,
td {
	padding: 8px;
}

th {
	border-width: 0 1px 1px 0;
}

td {
	border-width: 0 1px 1px 0;
}

hr {
	background-color: rgba(0, 0, 0, 0.1);
	border: 0;
	height: 1px;
	margin-bottom: 40px;
	margin-top: 40px;
}

fieldset {
	padding: 0;
	border: 0;
	min-width: inherit;
}

fieldset legend {
	padding: 0;
	margin-bottom: 1.5em;
}

h1 {
	font-size: 42px;
	margin-bottom: 20px;
	line-height: 1.2em;
	font-weight: normal;
	text-transform: none;
}

h2 {
	font-size: 35px;
	margin-bottom: 20px;
	line-height: 1.2em;
	font-weight: normal;
	text-transform: none;
}

h3 {
	font-size: 29px;
	margin-bottom: 20px;
	line-height: 1.2em;
	font-weight: normal;
	text-transform: none;
}

h4 {
	font-size: 24px;
}

h5 {
	font-size: 20px;
}

h4,
h5,
h6 {
	margin-bottom: 20px;
}

ul,
ol {
	margin: 0 0 1.5em 3em;
}

ul {
	list-style: disc;
}

ol {
	list-style: decimal;
}

li > ul,
li > ol {
	margin-bottom: 0;
	margin-left: 1.5em;
}

dt {
	font-weight: bold;
}

dd {
	margin: 0 1.5em 1.5em;
}

b,
strong {
	font-weight: bold;
}

dfn,
cite,
em,
i {
	font-style: italic;
}

address {
	margin: 0 0 1.5em;
}

code,
kbd,
tt,
var {
	font: 15px Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
}

abbr,
acronym {
	border-bottom: 1px dotted #666;
	cursor: help;
}

mark,
ins {
	text-decoration: none;
}

sup,
sub {
	font-size: 75%;
	height: 0;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sup {
	bottom: 1ex;
}

sub {
	top: .5ex;
}

small {
	font-size: 75%;
}

big {
	font-size: 125%;
}

figure {
	margin: 0;
}

table {
	margin: 0 0 1.5em;
	width: 100%;
}

th {
	font-weight: bold;
}

img {
	height: auto;
	/* Make sure images are scaled correctly. */
	max-width: 100%;
	/* Adhere to container width. */
}

/*--------------------------------------------------------------
# Forms
--------------------------------------------------------------*/
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
	background: #55555e;
	color: #fff;
	border: 1px solid transparent;
	cursor: pointer;
	-webkit-appearance: button;
	/* Corrects inability to style clickable 'input' types in iOS */
	padding: 10px 20px;
}

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="number"],
textarea,
select {
	border: 1px solid;
	border-radius: 0;
	padding: 10px 15px;
	max-width: 100%;
}

textarea {
	width: 100%;
}

/*--------------------------------------------------------------
# Links
--------------------------------------------------------------*/
a,
button,
input {
	transition: color 0.1s ease-in-out, background-color 0.1s ease-in-out;
}

a {
	text-decoration: none;
}

.button,
.wp-block-button .wp-block-button__link {
	padding: 10px 20px;
	display: inline-block;
}

.wp-block-button .wp-block-button__link {
	font-size: inherit;
	line-height: inherit;
}

.using-mouse :focus {
	outline: 0;
}

.using-mouse ::-moz-focus-inner {
	border: 0;
}

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.alignleft {
	float: left;
	margin-right: 1.5em;
}

.alignright {
	float: right;
	margin-left: 1.5em;
}

.aligncenter {
	clear: both;
	display: block;
	margin: 0 auto;
}

.size-auto,
.size-full,
.size-large,
.size-medium,
.size-thumbnail {
	max-width: 100%;
	height: auto;
}

.no-sidebar .entry-content .alignfull {
	margin-left: calc( -100vw / 2 + 100% / 2);
	margin-right: calc( -100vw / 2 + 100% / 2);
	max-width: 100vw;
	width: auto;
}

/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/
.screen-reader-text {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important;
}

.screen-reader-text:focus {
	background-color: #f1f1f1;
	border-radius: 3px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	clip-path: none;
	color: #21759b;
	display: block;
	font-size: 0.875rem;
	font-weight: 700;
	height: auto;
	left: 5px;
	line-height: normal;
	padding: 15px 23px 14px;
	text-decoration: none;
	top: 5px;
	width: auto;
	z-index: 100000;
}

/* Do not show the outline on the skip link target. */
#primary[tabindex="-1"]:focus {
	outline: 0;
}

/*--------------------------------------------------------------
# Navigation
--------------------------------------------------------------*/
.main-navigation {
	z-index: 100;
	padding: 0;
	clear: both;
	display: block;
}

.main-navigation a {
	display: block;
	text-decoration: none;
	font-weight: normal;
	text-transform: none;
	font-size: 15px;
}

.main-navigation ul {
	list-style: none;
	margin: 0;
	padding-left: 0;
}

.main-navigation .main-nav ul li a {
	padding-left: 20px;
	padding-right: 20px;
	line-height: 60px;
}

.inside-navigation {
	position: relative;
}

.main-navigation .inside-navigation {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	justify-content: space-between;
}

.main-navigation .main-nav > ul {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.main-navigation li {
	position: relative;
}

.main-navigation .menu-bar-items {
	display: flex;
	align-items: center;
	font-size: 15px;
}

.main-navigation .menu-bar-items a {
	color: inherit;
}

.main-navigation .menu-bar-item {
	position: relative;
}

.main-navigation .menu-bar-item.search-item {
	z-index: 20;
}

.main-navigation .menu-bar-item > a {
	padding-left: 20px;
	padding-right: 20px;
	line-height: 60px;
}

.sidebar .main-navigation .main-nav {
	flex-basis: 100%;
}

.sidebar .main-navigation .main-nav > ul {
	flex-direction: column;
}

.sidebar .main-navigation .menu-bar-items {
	margin: 0 auto;
}

.sidebar .main-navigation .menu-bar-items .search-item {
	order: 10;
}

.nav-align-center .inside-navigation {
	justify-content: center;
}

.nav-align-center .main-nav > ul {
	justify-content: center;
}

.nav-align-right .inside-navigation {
	justify-content: flex-end;
}

/*--------------------------------------------------------------
## Dropdown Menus
--------------------------------------------------------------*/
.main-navigation ul ul {
	display: block;
	box-shadow: 1px 1px 0 rgba(0, 0, 0, 0.1);
	float: left;
	position: absolute;
	left: -99999px;
	opacity: 0;
	z-index: 99999;
	width: 200px;
	text-align: left;
	top: auto;
	transition: opacity 80ms linear;
	transition-delay: 0s;
	pointer-events: none;
	height: 0;
	overflow: hidden;
}

.main-navigation ul ul a {
	display: block;
}

.main-navigation ul ul li {
	width: 100%;
}

.main-navigation .main-nav ul ul li a {
	line-height: normal;
	padding: 10px 20px;
	font-size: 14px;
}

.main-navigation .main-nav ul li.menu-item-has-children > a {
	padding-right: 0;
	position: relative;
}

.main-navigation.sub-menu-left ul ul {
	box-shadow: -1px 1px 0 rgba(0, 0, 0, 0.1);
}

.main-navigation.sub-menu-left .sub-menu {
	right: 0;
}

.main-navigation:not(.toggled) ul li:hover > ul,
.main-navigation:not(.toggled) ul li.sfHover > ul {
	left: auto;
	opacity: 1;
	transition-delay: 150ms;
	pointer-events: auto;
	height: auto;
	overflow: visible;
}

.main-navigation:not(.toggled) ul ul li:hover > ul,
.main-navigation:not(.toggled) ul ul li.sfHover > ul {
	left: 100%;
	top: 0;
}

.main-navigation.sub-menu-left:not(.toggled) ul ul li:hover > ul,
.main-navigation.sub-menu-left:not(.toggled) ul ul li.sfHover > ul {
	right: 100%;
	left: auto;
}

.nav-float-right .main-navigation ul ul ul {
	top: 0;
}

.menu-item-has-children .dropdown-menu-toggle {
	display: inline-block;
	height: 100%;
	clear: both;
	padding-right: 20px;
	padding-left: 10px;
}

.menu-item-has-children ul .dropdown-menu-toggle {
	padding-top: 10px;
	padding-bottom: 10px;
	margin-top: -10px;
}

nav ul ul .menu-item-has-children .dropdown-menu-toggle,
.sidebar .menu-item-has-children .dropdown-menu-toggle {
	float: right;
}

/*--------------------------------------------------------------
## Sidebar Navigation
--------------------------------------------------------------*/
.widget-area .main-navigation li {
	float: none;
	display: block;
	width: 100%;
	padding: 0;
	margin: 0;
}

.sidebar .main-navigation.sub-menu-right ul li:hover ul,
.sidebar .main-navigation.sub-menu-right ul li.sfHover ul {
	top: 0;
	left: 100%;
}

.sidebar .main-navigation.sub-menu-left ul li:hover ul,
.sidebar .main-navigation.sub-menu-left ul li.sfHover ul {
	top: 0;
	right: 100%;
}

/*--------------------------------------------------------------
# Post Navigation
--------------------------------------------------------------*/
.site-main .comment-navigation,
.site-main .posts-navigation,
.site-main .post-navigation {
	margin: 0 0 2em;
	overflow: hidden;
}

.site-main .post-navigation {
	margin-bottom: 0;
}

.paging-navigation .nav-previous,
.paging-navigation .nav-next {
	display: none;
}

.paging-navigation .nav-links > * {
	padding: 0 5px;
}

.paging-navigation .nav-links .current {
	font-weight: bold;
}

/* Less specific so we don't overwrite existing customizations. */
.nav-links > *:first-child {
	padding-left: 0;
}

/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
.site-header {
	position: relative;
}

.inside-header {
	padding: 20px 40px;
}

.main-title {
	margin: 0;
	font-size: 25px;
	line-height: 1.2em;
	word-wrap: break-word;
	font-weight: bold;
	text-transform: none;
}

.site-description {
	margin: 0;
	line-height: 1.5;
	font-weight: normal;
	text-transform: none;
	font-size: 15px;
}

.site-logo {
	display: inline-block;
	max-width: 100%;
}

.site-header .header-image {
	vertical-align: middle;
}

.inside-header {
	display: flex;
	align-items: center;
}

.header-widget {
	margin-left: auto;
}

.header-widget p:last-child {
	margin-bottom: 0;
}

.nav-float-right .header-widget {
	margin-left: 20px;
}

.nav-float-right #site-navigation {
	margin-left: auto;
}

.nav-float-left #site-navigation {
	margin-right: auto;
	order: -10;
}

.nav-float-left .header-widget {
	margin-left: 0;
	margin-right: 20px;
	order: -15;
}

.header-aligned-center:not([class*="nav-float-"]) .inside-header {
	justify-content: center;
	flex-direction: column;
	text-align: center;
}

.header-aligned-center:not([class*="nav-float-"]) .header-widget {
	margin-left: auto;
	margin-right: auto;
}

.header-aligned-center:not([class*="nav-float-"]) .inside-header > *:not(:first-child) {
	margin-top: 1em;
}

.header-aligned-right:not([class*="nav-float-"]) .inside-header {
	justify-content: flex-end;
}

.header-aligned-right:not([class*="nav-float-"]) .header-widget {
	margin-right: auto;
	margin-left: 0;
	order: -10;
}

.site-branding-container {
	display: inline-flex;
	align-items: center;
	text-align: left;
	flex-shrink: 0;
}

.site-branding-container .site-logo {
	margin-right: 1em;
}

/*--------------------------------------------------------------
# Post Content
--------------------------------------------------------------*/
.sticky {
	display: block;
}

.posted-on .updated,
.entry-header .gp-icon {
	display: none;
}

.byline,
.single .byline,
.group-blog .byline,
.entry-header .cat-links,
.entry-header .tags-links,
.entry-header .comments-link {
	display: inline;
}

footer.entry-meta .byline,
footer.entry-meta .posted-on {
	display: block;
}

.page-content:not(:first-child),
.entry-content:not(:first-child),
.entry-summary:not(:first-child) {
	margin-top: 2em;
}

.page-links {
	clear: both;
	margin: 0 0 1.5em;
}

.blog .format-status .entry-title,
.archive .format-status .entry-title,
.blog .format-aside .entry-header,
.archive .format-aside .entry-header,
.blog .format-status .entry-header,
.archive .format-status .entry-header,
.blog .format-status .entry-meta,
.archive .format-status .entry-meta {
	display: none;
}

.blog .format-aside .entry-content,
.archive .format-aside .entry-content,
.blog .format-status .entry-content,
.archive .format-status .entry-content {
	margin-top: 0;
}

.blog .format-status .entry-content p:last-child,
.archive .format-status .entry-content p:last-child {
	margin-bottom: 0;
}

.site-content,
.entry-header {
	word-wrap: break-word;
}

.entry-title {
	margin-bottom: 0;
}

.author .page-header .page-title {
	display: flex;
	align-items: center;
}

.author .page-header .avatar {
	margin-right: 20px;
}

.page-header > *:last-child,
.page-header .author-info > *:last-child {
	margin-bottom: 0;
}

.entry-meta {
	font-size: 85%;
	margin-top: .5em;
	line-height: 1.5;
}

footer.entry-meta {
	margin-top: 2em;
}

.cat-links,
.tags-links,
.comments-link {
	display: block;
}

.taxonomy-description p:last-child,
.read-more-container,
.page-content > p:last-child,
.entry-content > p:last-child,
.entry-summary > p:last-child {
	margin-bottom: 0;
}

/*--------------------------------------------------------------
# Media
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Captions
--------------------------------------------------------------*/
.wp-caption {
	margin-bottom: 1.5em;
	max-width: 100%;
	position: relative;
}

.wp-caption img[class*="wp-image-"] {
	display: block;
	margin: 0 auto 0;
	max-width: 100%;
}

.wp-caption .wp-caption-text {
	font-size: 75%;
	padding-top: 5px;
	opacity: 0.8;
}

.wp-caption img {
	position: relative;
	vertical-align: bottom;
}

.wp-block-image figcaption {
	font-size: 13px;
	text-align: center;
}

/*--------------------------------------------------------------
## Galleries
--------------------------------------------------------------*/
.wp-block-gallery,
ul.blocks-gallery-grid {
	margin-left: 0;
}

.wp-block-gallery .blocks-gallery-image figcaption,
.wp-block-gallery .blocks-gallery-item figcaption {
	background: rgba(255, 255, 255, 0.7);
	color: #000;
	padding: 10px;
	box-sizing: border-box;
}

.gallery {
	margin-bottom: 1.5em;
}

.gallery-item {
	display: inline-block;
	text-align: center;
	vertical-align: top;
	width: 100%;
}

.gallery-columns-2 .gallery-item {
	max-width: 50%;
}

.gallery-columns-3 .gallery-item {
	max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
	max-width: 25%;
}

.gallery-columns-5 .gallery-item {
	max-width: 20%;
}

.gallery-columns-6 .gallery-item {
	max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
	max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
	max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
	max-width: 11.11%;
}

.gallery-caption {
	display: block;
}

.site-main .gallery {
	margin-bottom: 1.5em;
}

.gallery-item img {
	vertical-align: bottom;
}

.gallery-icon {
	padding: 5px;
}

embed,
iframe,
object {
	max-width: 100%;
}

/*--------------------------------------------------------------
## Post Loop Block
--------------------------------------------------------------*/
.wp-block-post-template {
	margin-left: 0;
}

/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
.widget-area .widget {
	padding: 40px;
}

.widget select {
	max-width: 100%;
}

.sidebar .widget *:last-child,
.footer-widgets .widget *:last-child {
	margin-bottom: 0;
}

.widget-title {
	margin-bottom: 30px;
	font-size: 20px;
	line-height: 1.5;
	font-weight: normal;
	text-transform: none;
}

.widget ul,
.widget ol {
	margin: 0;
}

.widget .search-field {
	width: 100%;
}

.widget .search-form {
	display: flex;
}

.widget .search-form button.search-submit {
	font-size: 15px;
}

.footer-widgets .widget {
	margin-bottom: 30px;
}

.sidebar .widget:last-child,
.footer-widgets .widget:last-child {
	margin-bottom: 0;
}

.widget ul li {
	list-style-type: none;
	position: relative;
	margin-bottom: 0.5em;
}

.widget ul li ul {
	margin-left: 1em;
	margin-top: 0.5em;
}

.wp-calendar-table {
	table-layout: fixed;
}

/*--------------------------------------------------------------
# Content Layout
--------------------------------------------------------------*/
.site-content {
	display: flex;
}

.grid-container {
	margin-left: auto;
	margin-right: auto;
	max-width: 1200px;
}

.sidebar .widget,
.page-header,
.site-main > * {
	margin-bottom: 20px;
}

.both-right .inside-left-sidebar,
.both-left .inside-left-sidebar {
	margin-right: 10px;
}

.both-right .inside-right-sidebar,
.both-left .inside-right-sidebar {
	margin-left: 10px;
}

/*--------------------------------------------------------------
## One Container
--------------------------------------------------------------*/
.one-container.right-sidebar .site-main,
.one-container.both-right .site-main {
	margin-right: 40px;
}

.one-container.left-sidebar .site-main,
.one-container.both-left .site-main {
	margin-left: 40px;
}

.one-container.both-sidebars .site-main {
	margin: 0px 40px 0px 40px;
}

.one-container.archive .post:not(:last-child):not(.is-loop-template-item),
.one-container.blog .post:not(:last-child):not(.is-loop-template-item) {
	padding-bottom: 40px;
}

.one-container .site-content {
	padding: 40px;
}

/*--------------------------------------------------------------
## Separate Containers
--------------------------------------------------------------*/
.separate-containers .inside-article,
.separate-containers .comments-area,
.separate-containers .page-header,
.separate-containers .paging-navigation {
	padding: 40px;
}

.separate-containers .site-main {
	margin: 20px;
}

.separate-containers.no-sidebar .site-main {
	margin-left: 0;
	margin-right: 0;
}

.separate-containers.right-sidebar .site-main,
.separate-containers.both-right .site-main {
	margin-left: 0;
}

.separate-containers.left-sidebar .site-main,
.separate-containers.both-left .site-main {
	margin-right: 0;
}

.separate-containers .inside-right-sidebar,
.separate-containers .inside-left-sidebar {
	margin-top: 20px;
	margin-bottom: 20px;
}

.inside-page-header {
	padding: 40px;
}

.widget-area .main-navigation {
	margin-bottom: 20px;
}

.separate-containers .site-main > *:last-child,
.one-container .site-main > *:last-child {
	margin-bottom: 0;
}

/*--------------------------------------------------------------
## Full Width Content
--------------------------------------------------------------*/
.full-width-content .container.grid-container {
	max-width: 100%;
}

.full-width-content.no-sidebar.separate-containers .site-main {
	margin: 0;
}

.full-width-content.separate-containers .inside-article,
.full-width-content.one-container .site-content {
	padding: 0;
}

.full-width-content .entry-content .alignwide {
	margin-left: 0;
	width: auto;
	max-width: unset;
}

/*--------------------------------------------------------------
## Contained Content
--------------------------------------------------------------*/
.contained-content.separate-containers .inside-article,
.contained-content.one-container .site-content {
	padding: 0;
}

/*--------------------------------------------------------------
# Sidebars
--------------------------------------------------------------*/
.sidebar .grid-container {
	max-width: 100%;
	width: 100%;
}

.left-sidebar .sidebar,
.both-left .is-left-sidebar,
.both-sidebars .is-left-sidebar {
	order: -10;
}

.both-left .is-right-sidebar {
	order: -5;
}

.both-right .is-left-sidebar {
	order: 5;
}

.both-right .is-right-sidebar,
.both-sidebars .is-right-sidebar {
	order: 10;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
.inside-site-info {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px 40px;
}

.site-info {
	text-align: center;
	font-size: 15px;
}

/*--------------------------------------------------------------
# Featured Images
--------------------------------------------------------------*/
.post-image:not(:first-child) {
	margin-top: 2em;
}

.featured-image {
	line-height: 0;
	/* no more weird spacing */
}

.separate-containers .featured-image {
	margin-top: 20px;
}

.separate-containers .inside-article > .featured-image {
	margin-top: 0;
	margin-bottom: 2em;
}

.one-container .inside-article > .featured-image {
	margin-top: 0;
	margin-bottom: 2em;
}

/*--------------------------------------------------------------
# Icons
--------------------------------------------------------------*/
.gp-icon {
	display: inline-flex;
	align-self: center;
}

.gp-icon svg {
	height: 1em;
	width: 1em;
	top: .125em;
	position: relative;
	fill: currentColor;
}

.icon-menu-bars svg:nth-child(2),
.toggled .icon-menu-bars svg:nth-child(1),
.icon-search svg:nth-child(2),
.close-search .icon-search svg:nth-child(1) {
	display: none;
}

.toggled .icon-menu-bars svg:nth-child(2),
.close-search .icon-search svg:nth-child(2) {
	display: block;
}

.entry-meta .gp-icon {
	margin-right: 0.6em;
	opacity: 0.7;
}

nav.toggled .icon-arrow-left svg {
	transform: rotate(-90deg);
}

nav.toggled .icon-arrow-right svg {
	transform: rotate(90deg);
}

nav.toggled .sfHover > a > .dropdown-menu-toggle .gp-icon svg {
	transform: rotate(180deg);
}

nav.toggled .sfHover > a > .dropdown-menu-toggle .gp-icon.icon-arrow-left svg {
	transform: rotate(-270deg);
}

nav.toggled .sfHover > a > .dropdown-menu-toggle .gp-icon.icon-arrow-right svg {
	transform: rotate(270deg);
}

/*--------------------------------------------------------------
# Compatibility
--------------------------------------------------------------*/
/* Bootstrap fix */
.container.grid-container {
	width: auto;
}

/*--------------------------------------------------------------
# Mobile
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Mobile Menu
--------------------------------------------------------------*/
.menu-toggle,
.mobile-bar-items,
.sidebar-nav-mobile {
	display: none;
}

.menu-toggle {
	padding: 0 20px;
	line-height: 60px;
	margin: 0;
	font-weight: normal;
	text-transform: none;
	font-size: 15px;
	cursor: pointer;
}

.menu-toggle .mobile-menu {
	padding-left: 3px;
}

.menu-toggle .gp-icon + .mobile-menu {
	padding-left: 9px;
}

.menu-toggle .mobile-menu:empty {
	display: none;
}

button.menu-toggle {
	background-color: transparent;
	flex-grow: 1;
	border: 0;
	text-align: center;
}

button.menu-toggle:hover, button.menu-toggle:active, button.menu-toggle:focus {
	background-color: transparent;
}

.has-menu-bar-items button.menu-toggle {
	flex-grow: 0;
}

nav.toggled ul ul.sub-menu {
	width: 100%;
}

.toggled .menu-item-has-children .dropdown-menu-toggle {
	padding-left: 20px;
}

.main-navigation.toggled .main-nav {
	flex-basis: 100%;
	order: 3;
}

.main-navigation.toggled .main-nav > ul {
	display: block;
}

.main-navigation.toggled .main-nav li {
	width: 100%;
	text-align: left;
}

.main-navigation.toggled .main-nav ul ul {
	transition: 0s;
	visibility: hidden;
	box-shadow: none;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.main-navigation.toggled .main-nav ul ul li:last-child > ul {
	border-bottom: 0;
}

.main-navigation.toggled .main-nav ul ul.toggled-on {
	position: relative;
	top: 0;
	left: auto !important;
	right: auto !important;
	width: 100%;
	pointer-events: auto;
	height: auto;
	opacity: 1;
	display: block;
	visibility: visible;
	float: none;
}

.main-navigation.toggled .menu-item-has-children .dropdown-menu-toggle {
	float: right;
}

.mobile-menu-control-wrapper {
	display: none;
	margin-left: auto;
	align-items: center;
}

.has-inline-mobile-toggle #site-navigation.toggled {
	margin-top: 1.5em;
}

.has-inline-mobile-toggle #site-navigation.has-active-search {
	margin-top: 1.5em;
}

.has-inline-mobile-toggle #site-navigation.has-active-search .nav-search-active {
	position: relative;
}

.has-inline-mobile-toggle #site-navigation.has-active-search .navigation-search input {
	outline: auto;
}

.nav-float-left .mobile-menu-control-wrapper {
	order: -10;
	margin-left: 0;
	margin-right: auto;
	flex-direction: row-reverse;
}

/*--------------------------------------------------------------
## Breakpoint (768px)
--------------------------------------------------------------*/
@media (max-width: 768px) {
	.hide-on-mobile {
		display: none !important;
	}
	/*--------------------------------------------------------------
	## Links
	--------------------------------------------------------------*/
	a, body, button, input, select, textarea {
		transition: all 0s ease-in-out;
	}
	/*--------------------------------------------------------------
	## Header
	--------------------------------------------------------------*/
	.inside-header {
		flex-direction: column;
		text-align: center;
	}
	.site-header .header-widget {
		margin-top: 1.5em;
		margin-left: auto;
		margin-right: auto;
		text-align: center;
	}
	/*--------------------------------------------------------------
	## Content Area
	--------------------------------------------------------------*/
	.site-content {
		flex-direction: column;
	}
	.container .site-content .content-area {
		width: auto;
	}
	.is-left-sidebar.sidebar,
	.is-right-sidebar.sidebar {
		width: auto;
		order: initial;
	}
	.is-left-sidebar + .is-right-sidebar .inside-right-sidebar {
		margin-top: 0;
	}
	.both-right .inside-left-sidebar,
	.both-left .inside-left-sidebar,
	.both-right .inside-right-sidebar,
	.both-left .inside-right-sidebar {
		margin-right: 0;
		margin-left: 0;
	}
	#main {
		margin-left: 0;
		margin-right: 0;
	}
	body:not(.no-sidebar) #main {
		margin-bottom: 0;
	}
	.alignleft,
	.alignright {
		float: none;
		display: block;
		margin-left: auto;
		margin-right: auto;
	}
	.comment .children {
		padding-left: 10px;
		margin-left: 0;
	}
	.entry-meta {
		font-size: inherit;
	}
	.entry-meta a {
		line-height: 1.8em;
	}
}

@media (min-width: 769px) and (max-width: 1024px) {
	.hide-on-tablet {
		display: none !important;
	}
}

@media (min-width: 1025px) {
	.hide-on-desktop {
		display: none !important;
	}
}
