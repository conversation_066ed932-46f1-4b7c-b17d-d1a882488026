# Rooftop JSON Importer - Installation Guide

## Quick Start

1. **Activate the Plugin**
   - The plugin is already installed in your WordPress site
   - Go to **Plugins** in your WordPress admin
   - Find "Rooftop JSON Importer" and click **Activate**

2. **Access the Importer**
   - Navigate to **Rooftops > Import JSON** in your WordPress admin
   - You'll see the import interface

3. **Test the Import**
   - Use the sample JSON file provided: `sample-rooftop.json`
   - Upload this file to test the import functionality

## Step-by-Step Installation

### 1. Plugin Activation

After activating the plugin, it will automatically:
- Create a secure upload directory for JSON files
- Register all the new taxonomies for rooftops
- Set up default import settings
- Create necessary database options

### 2. Verify Taxonomies

Go to **Rooftops** in your WordPress admin and check that you now see these taxonomy menus:
- Neighborhoods
- Atmosphere
- Best For
- View Types
- Music Styles
- Amenities
- Accessibility Features
- Menu Types
- Cuisine Styles
- Dietary Options
- Price Ranges
- Dress Codes
- Venue Types
- Popular Features (existing)
- Services (existing)
- Specialties

### 3. Configure Settings

Go to **Rooftops > Import Settings** and configure:
- **Auto-create Taxonomies**: ✅ Recommended (enabled by default)
- **Update Existing Posts**: ❌ Disabled by default (enable if you want to update existing rooftops)
- **Import Images**: ✅ Enabled by default (future feature)
- **Log Imports**: ✅ Recommended (enabled by default)

### 4. Test Import

1. Go to **Rooftops > Import JSON**
2. Upload the `sample-rooftop.json` file (located in the plugin directory)
3. Click "Import JSON File"
4. Check the results

### 5. Verify Import Results

After importing the sample file, you should see:
- A new rooftop post: "83.3 Terrace Bar"
- Taxonomy terms created automatically
- Custom fields populated with detailed information

## Troubleshooting

### Common Issues

**1. Plugin Not Appearing**
- Make sure the plugin files are in the correct directory: `/wp-content/plugins/rooftop-json-importer/`
- Check that all files are present and readable

**2. Taxonomies Not Showing**
- Deactivate and reactivate the plugin
- Go to **Settings > Permalinks** and click "Save Changes" to flush rewrite rules

**3. Import Fails**
- Check **Rooftops > Import Logs** for detailed error messages
- Ensure your JSON file is valid (use a JSON validator)
- Check file permissions on the uploads directory

**4. File Upload Issues**
- Ensure your server allows file uploads
- Check PHP upload limits (max_file_size, upload_max_filesize)
- Verify the uploads directory is writable

### File Permissions

Ensure these directories are writable:
- `/wp-content/uploads/`
- `/wp-content/uploads/rooftop-imports/` (created automatically)

### PHP Requirements

- PHP 7.4 or higher
- JSON extension enabled
- File upload enabled
- Sufficient memory limit (256MB recommended)

## Testing the Import

### Sample Data

The plugin includes a sample JSON file (`sample-rooftop.json`) with data for "83.3 Terrace Bar". This file demonstrates the complete JSON structure and can be used to test all import functionality.

### Expected Results

After importing the sample file, you should see:

**New Rooftop Post:**
- Title: "83.3 Terrace Bar"
- Content: Full description from the JSON
- Excerpt: Meta description from the JSON

**Taxonomies Assigned:**
- Neighborhood: Eixample
- Atmosphere: Vibrant, Modern, Sophisticated, etc.
- Best For: Sunset Cocktails, Panoramic City Views, etc.
- View Types: Sagrada Familia, Montjuïc Mountain, etc.
- Amenities: Covered & Climate-Controlled Terrace, etc.
- And many more...

**Custom Fields:**
- Address, phone, email, website
- GPS coordinates
- Social media links
- Operating hours
- Accessibility information
- Food and drink details
- Transportation information

### Validation

To verify everything is working correctly:

1. **Check the Post**: Go to **Rooftops > All Rooftops** and open the imported rooftop
2. **Review Taxonomies**: Check that terms were created in each taxonomy
3. **Verify Custom Fields**: Scroll down to see all the custom fields populated
4. **Check Logs**: Go to **Rooftops > Import Logs** to see the import activity

## Next Steps

### Preparing Your JSON Files

1. **Follow the Structure**: Use the sample JSON as a template
2. **Validate JSON**: Ensure your JSON files are valid before importing
3. **Test Small Batches**: Start with a few rooftops before importing large datasets
4. **Backup First**: Always backup your site before importing large amounts of data

### Customization

The plugin is designed to work with your existing rooftop structure. If you need to modify the import behavior:

1. **Custom Field Mapping**: Edit the `save_custom_fields()` method in `class-importer.php`
2. **Taxonomy Mapping**: Modify the `rooftop_get_taxonomy_mapping()` function
3. **Validation Rules**: Update the `validate_rooftop_data()` method

### Support

If you encounter issues:
1. Check the import logs first
2. Verify your JSON structure matches the expected format
3. Test with the sample JSON file
4. Contact the development team with specific error messages

## Security Notes

- JSON files are uploaded to a protected directory
- The upload directory includes an .htaccess file to prevent direct access
- All data is sanitized before being saved to the database
- File uploads are restricted to JSON files only

## Performance Considerations

- Large JSON files may take time to process
- Consider importing in smaller batches for better performance
- The plugin includes progress indicators for user feedback
- Import logs help track performance and identify bottlenecks
