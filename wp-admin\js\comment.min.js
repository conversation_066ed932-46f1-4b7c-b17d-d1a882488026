/*! This file is auto-generated */
jQuery(function(m){postboxes.add_postbox_toggles("comment");var d=m("#timestampdiv"),o=m("#timestamp"),a=o.html(),v=d.find(".timestamp-wrap"),c=d.siblings("a.edit-timestamp");c.on("click",function(e){d.is(":hidden")&&(d.slideDown("fast",function(){m("input, select",v).first().trigger("focus")}),m(this).hide()),e.preventDefault()}),d.find(".cancel-timestamp").on("click",function(e){c.show().trigger("focus"),d.slideUp("fast"),m("#mm").val(m("#hidden_mm").val()),m("#jj").val(m("#hidden_jj").val()),m("#aa").val(m("#hidden_aa").val()),m("#hh").val(m("#hidden_hh").val()),m("#mn").val(m("#hidden_mn").val()),o.html(a),e.preventDefault()}),d.find(".save-timestamp").on("click",function(e){var a=m("#aa").val(),t=m("#mm").val(),i=m("#jj").val(),s=m("#hh").val(),l=m("#mn").val(),n=new Date(a,t-1,i,s,l);e.preventDefault(),n.getFullYear()!=a||1+n.getMonth()!=t||n.getDate()!=i||n.getMinutes()!=l?v.addClass("form-invalid"):(v.removeClass("form-invalid"),o.html(wp.i18n.__("Submitted on:")+" <b>"+wp.i18n.__("%1$s %2$s, %3$s at %4$s:%5$s").replace("%1$s",m('option[value="'+t+'"]',"#mm").attr("data-text")).replace("%2$s",parseInt(i,10)).replace("%3$s",a).replace("%4$s",("00"+s).slice(-2)).replace("%5$s",("00"+l).slice(-2))+"</b> "),c.show().trigger("focus"),d.slideUp("fast"))})});