<?php
/**
 * Admin Interface Class
 * Handles the WordPress admin interface for the rooftop importer
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rooftop_Importer_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_rooftop_import_json', array($this, 'handle_ajax_import'));
        add_action('admin_init', array($this, 'handle_form_submission'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=rooftops',
            __('Import JSON', 'rooftop-json-importer'),
            __('Import JSON', 'rooftop-json-importer'),
            'manage_options',
            'rooftop-json-import',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'edit.php?post_type=rooftops',
            __('Import Settings', 'rooftop-json-importer'),
            __('Import Settings', 'rooftop-json-importer'),
            'manage_options',
            'rooftop-import-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'edit.php?post_type=rooftops',
            __('Import Logs', 'rooftop-json-importer'),
            __('Import Logs', 'rooftop-json-importer'),
            'manage_options',
            'rooftop-import-logs',
            array($this, 'logs_page')
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if (strpos($hook, 'rooftop') === false) {
            return;
        }
        
        wp_enqueue_style(
            'rooftop-importer-admin',
            ROOFTOP_IMPORTER_PLUGIN_URL . 'assets/admin.css',
            array(),
            ROOFTOP_IMPORTER_VERSION
        );
        
        wp_enqueue_script(
            'rooftop-importer-admin',
            ROOFTOP_IMPORTER_PLUGIN_URL . 'assets/admin.js',
            array('jquery'),
            ROOFTOP_IMPORTER_VERSION,
            true
        );
        
        wp_localize_script('rooftop-importer-admin', 'rooftopImporter', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rooftop_importer_nonce'),
            'strings' => array(
                'importing' => __('Importing...', 'rooftop-json-importer'),
                'success' => __('Import completed successfully!', 'rooftop-json-importer'),
                'error' => __('Import failed. Please check the logs.', 'rooftop-json-importer')
            )
        ));
    }
    
    /**
     * Handle form submission
     */
    public function handle_form_submission() {
        if (!isset($_POST['rooftop_import_submit']) || !wp_verify_nonce($_POST['rooftop_import_nonce'], 'rooftop_import_action')) {
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        // Handle settings update
        if (isset($_POST['update_settings'])) {
            $this->update_settings();
            return;
        }
        
        // Handle file upload and import
        if (isset($_FILES['json_file']) && !empty($_FILES['json_file']['tmp_name'])) {
            $this->handle_file_import();
        }
    }
    
    /**
     * Handle file import
     */
    private function handle_file_import() {
        $file = $_FILES['json_file'];
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('File upload error.', 'rooftop-json-importer') . '</p></div>';
            });
            return;
        }
        
        // Check file type
        $file_info = pathinfo($file['name']);
        if (strtolower($file_info['extension']) !== 'json') {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Please upload a JSON file.', 'rooftop-json-importer') . '</p></div>';
            });
            return;
        }
        
        // Move file to secure location
        $upload_dir = wp_upload_dir();
        $rooftop_dir = $upload_dir['basedir'] . '/rooftop-imports';
        $filename = 'import_' . time() . '_' . sanitize_file_name($file['name']);
        $file_path = $rooftop_dir . '/' . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $file_path)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Failed to save uploaded file.', 'rooftop-json-importer') . '</p></div>';
            });
            return;
        }
        
        // Import the file
        $importer = new Rooftop_Importer();
        $result = $importer->import_from_file($file_path);
        
        // Clean up file
        unlink($file_path);
        
        // Show results
        $this->show_import_results($importer->get_import_results());
    }
    
    /**
     * Show import results
     */
    private function show_import_results($results) {
        $message_type = $results['errors'] > 0 ? 'error' : 'success';
        $message = sprintf(
            __('Import completed: %d successful, %d errors, %d skipped', 'rooftop-json-importer'),
            $results['success'],
            $results['errors'],
            $results['skipped']
        );
        
        add_action('admin_notices', function() use ($message_type, $message) {
            echo '<div class="notice notice-' . $message_type . '"><p>' . esc_html($message) . '</p></div>';
        });
    }
    
    /**
     * Update settings
     */
    private function update_settings() {
        $settings = array(
            'auto_create_taxonomies' => isset($_POST['auto_create_taxonomies']),
            'update_existing_posts' => isset($_POST['update_existing_posts']),
            'import_images' => isset($_POST['import_images']),
            'log_imports' => isset($_POST['log_imports'])
        );
        
        update_option('rooftop_importer_settings', $settings);
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>' . __('Settings updated successfully.', 'rooftop-json-importer') . '</p></div>';
        });
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Rooftop JSON Importer', 'rooftop-json-importer'); ?></h1>
            
            <div class="rooftop-importer-container">
                <div class="rooftop-importer-main">
                    <div class="card">
                        <h2><?php _e('Import Rooftop Data', 'rooftop-json-importer'); ?></h2>
                        <p><?php _e('Upload a JSON file containing rooftop data to import into your WordPress site.', 'rooftop-json-importer'); ?></p>
                        
                        <form method="post" enctype="multipart/form-data" class="rooftop-import-form">
                            <?php wp_nonce_field('rooftop_import_action', 'rooftop_import_nonce'); ?>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="json_file"><?php _e('JSON File', 'rooftop-json-importer'); ?></label>
                                    </th>
                                    <td>
                                        <input type="file" id="json_file" name="json_file" accept=".json" required>
                                        <p class="description"><?php _e('Select a JSON file containing rooftop data.', 'rooftop-json-importer'); ?></p>
                                    </td>
                                </tr>
                            </table>
                            
                            <p class="submit">
                                <input type="submit" name="rooftop_import_submit" class="button-primary" value="<?php _e('Import JSON File', 'rooftop-json-importer'); ?>">
                            </p>
                        </form>
                    </div>
                    
                    <div class="card">
                        <h2><?php _e('JSON Format Example', 'rooftop-json-importer'); ?></h2>
                        <p><?php _e('Your JSON file should follow this structure:', 'rooftop-json-importer'); ?></p>
                        <pre><code>{
  "id": "BCN_ROOFTOP_EXAMPLE_001",
  "name": "Example Rooftop Bar",
  "hostVenue": {
    "name": "Example Hotel",
    "type": "Hotel",
    "website": "https://example.com"
  },
  "basicInfo": {
    "address": {
      "street": "Example Street, 123",
      "city": "Barcelona",
      "postalCode": "08001",
      "neighborhood": "Eixample",
      "country": "Spain",
      "coordinates": {
        "latitude": 41.3851,
        "longitude": 2.1734
      }
    },
    "contact": {
      "phone": "+34 123 456 789",
      "email": "<EMAIL>"
    }
  },
  "experienceAndVibe": {
    "description": "Description of the rooftop...",
    "views": ["City Skyline", "Sea View"],
    "bestFor": ["Sunset Cocktails", "Date Night"],
    "atmosphere": ["Romantic", "Sophisticated"]
  },
  "popular": ["360 Views", "Cocktails"],
  "services": ["Cocktail Bar", "Restaurant"],
  "amenities": ["Covered Terrace", "Heating"],
  "specialties": ["Signature Cocktails", "Tapas"]
}</code></pre>
                    </div>
                </div>
                
                <div class="rooftop-importer-sidebar">
                    <div class="card">
                        <h3><?php _e('Quick Stats', 'rooftop-json-importer'); ?></h3>
                        <?php
                        $rooftop_count = wp_count_posts('rooftops');
                        $taxonomy_counts = array();
                        $taxonomies = array('neighborhoods', 'amenities', 'services', 'popular_features');
                        
                        foreach ($taxonomies as $taxonomy) {
                            if (taxonomy_exists($taxonomy)) {
                                $taxonomy_counts[$taxonomy] = wp_count_terms($taxonomy);
                            }
                        }
                        ?>
                        <ul>
                            <li><strong><?php _e('Total Rooftops:', 'rooftop-json-importer'); ?></strong> <?php echo $rooftop_count->publish; ?></li>
                            <?php foreach ($taxonomy_counts as $taxonomy => $count): ?>
                                <li><strong><?php echo ucfirst(str_replace('_', ' ', $taxonomy)); ?>:</strong> <?php echo $count; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3><?php _e('Import Tips', 'rooftop-json-importer'); ?></h3>
                        <ul>
                            <li><?php _e('Ensure your JSON file is valid before uploading', 'rooftop-json-importer'); ?></li>
                            <li><?php _e('Large files may take some time to process', 'rooftop-json-importer'); ?></li>
                            <li><?php _e('Check the logs after import for detailed results', 'rooftop-json-importer'); ?></li>
                            <li><?php _e('Backup your site before importing large datasets', 'rooftop-json-importer'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        $settings = get_option('rooftop_importer_settings', array());
        ?>
        <div class="wrap">
            <h1><?php _e('Import Settings', 'rooftop-json-importer'); ?></h1>
            
            <form method="post">
                <?php wp_nonce_field('rooftop_import_action', 'rooftop_import_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Auto-create Taxonomies', 'rooftop-json-importer'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_create_taxonomies" <?php checked(!empty($settings['auto_create_taxonomies'])); ?>>
                                <?php _e('Automatically create taxonomy terms that don\'t exist', 'rooftop-json-importer'); ?>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Update Existing Posts', 'rooftop-json-importer'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="update_existing_posts" <?php checked(!empty($settings['update_existing_posts'])); ?>>
                                <?php _e('Update existing rooftops if they already exist', 'rooftop-json-importer'); ?>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Import Images', 'rooftop-json-importer'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="import_images" <?php checked(!empty($settings['import_images'])); ?>>
                                <?php _e('Import and set featured images from URLs', 'rooftop-json-importer'); ?>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Log Imports', 'rooftop-json-importer'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="log_imports" <?php checked(!empty($settings['log_imports'])); ?>>
                                <?php _e('Keep detailed logs of import activities', 'rooftop-json-importer'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="rooftop_import_submit" class="button-primary" value="<?php _e('Save Settings', 'rooftop-json-importer'); ?>">
                    <input type="hidden" name="update_settings" value="1">
                </p>
            </form>
        </div>
        <?php
    }
    
    /**
     * Logs page
     */
    public function logs_page() {
        $logs = get_option('rooftop_importer_logs', array());
        $logs = array_reverse($logs); // Show newest first
        ?>
        <div class="wrap">
            <h1><?php _e('Import Logs', 'rooftop-json-importer'); ?></h1>
            
            <?php if (empty($logs)): ?>
                <p><?php _e('No import logs found.', 'rooftop-json-importer'); ?></p>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Timestamp', 'rooftop-json-importer'); ?></th>
                            <th><?php _e('Type', 'rooftop-json-importer'); ?></th>
                            <th><?php _e('Message', 'rooftop-json-importer'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo esc_html($log['timestamp']); ?></td>
                                <td>
                                    <span class="log-type log-type-<?php echo esc_attr($log['type']); ?>">
                                        <?php echo esc_html(ucfirst($log['type'])); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html($log['message']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <p>
                    <a href="<?php echo admin_url('admin.php?page=rooftop-import-logs&clear_logs=1'); ?>" 
                       class="button" 
                       onclick="return confirm('<?php _e('Are you sure you want to clear all logs?', 'rooftop-json-importer'); ?>')">
                        <?php _e('Clear Logs', 'rooftop-json-importer'); ?>
                    </a>
                </p>
            <?php endif; ?>
        </div>
        <?php
        
        // Handle log clearing
        if (isset($_GET['clear_logs']) && $_GET['clear_logs'] == '1') {
            delete_option('rooftop_importer_logs');
            wp_redirect(admin_url('admin.php?page=rooftop-import-logs'));
            exit;
        }
    }
    
    /**
     * Handle AJAX import
     */
    public function handle_ajax_import() {
        check_ajax_referer('rooftop_importer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions.'));
        }
        
        // This would handle AJAX-based imports for better UX
        wp_die();
    }
}
