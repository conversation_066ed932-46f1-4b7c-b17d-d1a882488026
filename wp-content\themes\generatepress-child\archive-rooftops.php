<?php
/**
 * Archive template for all rooftops
 */

get_header(); ?>

<div class="container" style="padding: 60px 20px;">
    <header class="page-header" style="text-align: center; margin-bottom: 50px;">
        <h1 class="page-title" style="font-size: 2.5rem; color: #2c3e50; margin-bottom: 1rem;">
            All Rooftops in Barcelona
        </h1>
        <p class="page-description" style="font-size: 1.2rem; color: #7f8c8d; max-width: 600px; margin: 0 auto;">
            Discover the complete collection of Barcelona's most amazing rooftop venues
        </p>
    </header>

    <!-- Filter Bar -->
    <div class="filter-bar" style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 40px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; align-items: center;">
            <div>
                <label style="display: block; font-weight: 600; color: #2c3e50; margin-bottom: 5px;">Filter by Feature:</label>
                <select onchange="window.location.href=this.value" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="<?php echo get_post_type_archive_link('rooftops'); ?>">All Features</option>
                    <?php
                    $features = get_terms(array('taxonomy' => 'popular_features', 'hide_empty' => false));
                    foreach ($features as $feature) {
                        echo '<option value="' . get_term_link($feature) . '">' . esc_html($feature->name) . '</option>';
                    }
                    ?>
                </select>
            </div>
            
            <div>
                <label style="display: block; font-weight: 600; color: #2c3e50; margin-bottom: 5px;">Filter by Service:</label>
                <select onchange="window.location.href=this.value" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="<?php echo get_post_type_archive_link('rooftops'); ?>">All Services</option>
                    <?php
                    $services = get_terms(array('taxonomy' => 'services', 'hide_empty' => false));
                    foreach ($services as $service) {
                        echo '<option value="' . get_term_link($service) . '">' . esc_html($service->name) . '</option>';
                    }
                    ?>
                </select>
            </div>
            
            <div>
                <label style="display: block; font-weight: 600; color: #2c3e50; margin-bottom: 5px;">Filter by Neighborhood:</label>
                <select onchange="window.location.href=this.value" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    <option value="<?php echo get_post_type_archive_link('rooftops'); ?>">All Neighborhoods</option>
                    <?php
                    $neighborhoods = get_terms(array('taxonomy' => 'neighborhoods', 'hide_empty' => false));
                    foreach ($neighborhoods as $neighborhood) {
                        echo '<option value="' . get_term_link($neighborhood) . '">' . esc_html($neighborhood->name) . '</option>';
                    }
                    ?>
                </select>
            </div>
            
            <div>
                <label style="display: block; font-weight: 600; color: #2c3e50; margin-bottom: 5px;">Sort by:</label>
                <select style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    <option>Most Popular</option>
                    <option>Highest Rated</option>
                    <option>Price: Low to High</option>
                    <option>Price: High to Low</option>
                    <option>Newest</option>
                </select>
            </div>
        </div>
    </div>

    <main class="main-content">
        <?php if (have_posts()) : ?>
            <!-- Results Count -->
            <div style="margin-bottom: 30px; color: #7f8c8d;">
                <?php
                global $wp_query;
                $total = $wp_query->found_posts;
                echo "Showing {$total} rooftop" . ($total !== 1 ? 's' : '');
                ?>
            </div>

            <div class="rooftops-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px; margin-bottom: 40px;">
                <?php while (have_posts()) : the_post(); ?>
                    <article class="rooftop-card" style="background: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s ease;">
                        <a href="<?php the_permalink(); ?>" style="text-decoration: none; color: inherit;">
                            <div class="rooftop-image" style="height: 250px; background-size: cover; background-position: center; background-image: url('<?php echo has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'medium') : 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'; ?>');">
                                <!-- Featured Badge -->
                                <?php if (get_post_meta(get_the_ID(), '_featured_rooftop', true) === 'yes') : ?>
                                    <div style="position: absolute; top: 15px; left: 15px; background: #e74c3c; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8rem; font-weight: 600;">
                                        Featured
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="rooftop-content" style="padding: 25px;">
                                <h2 class="rooftop-title" style="font-size: 1.3rem; font-weight: 600; margin-bottom: 10px; color: #2c3e50;">
                                    <?php the_title(); ?>
                                </h2>
                                
                                <?php 
                                $address = get_post_meta(get_the_ID(), '_rooftop_address', true);
                                if ($address) : ?>
                                    <p class="rooftop-address" style="color: #7f8c8d; margin-bottom: 10px; font-size: 0.95rem;">
                                        📍 <?php echo esc_html($address); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if (has_excerpt()) : ?>
                                    <p class="rooftop-excerpt" style="color: #7f8c8d; line-height: 1.6; margin-bottom: 15px;">
                                        <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="rooftop-meta" style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                                    <?php 
                                    $rating = get_post_meta(get_the_ID(), '_rooftop_rating', true);
                                    if ($rating) : ?>
                                        <div class="rooftop-rating" style="display: flex; align-items: center; gap: 5px;">
                                            <span class="stars" style="color: #f39c12;">
                                                <?php
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= floor($rating)) {
                                                        echo '★';
                                                    } elseif ($i - 0.5 <= $rating) {
                                                        echo '☆';
                                                    } else {
                                                        echo '☆';
                                                    }
                                                }
                                                ?>
                                            </span>
                                            <span style="font-size: 0.9rem; color: #7f8c8d;"><?php echo esc_html($rating); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php 
                                    $price_range = get_post_meta(get_the_ID(), '_rooftop_price_range', true);
                                    if ($price_range) : ?>
                                        <span class="price-range" style="background: #e74c3c; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.85rem; font-weight: 600;">
                                            <?php echo esc_html($price_range); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="rooftop-features" style="margin-top: 15px;">
                                    <?php
                                    // Display taxonomies
                                    $popular_features = get_the_terms(get_the_ID(), 'popular_features');
                                    $services = get_the_terms(get_the_ID(), 'services');
                                    $amenities = get_the_terms(get_the_ID(), 'amenities');
                                    $neighborhoods = get_the_terms(get_the_ID(), 'neighborhoods');
                                    
                                    $all_terms = array();
                                    if ($popular_features) $all_terms = array_merge($all_terms, array_slice($popular_features, 0, 1));
                                    if ($services) $all_terms = array_merge($all_terms, array_slice($services, 0, 1));
                                    if ($amenities) $all_terms = array_merge($all_terms, array_slice($amenities, 0, 1));
                                    if ($neighborhoods) $all_terms = array_merge($all_terms, array_slice($neighborhoods, 0, 1));
                                    
                                    if ($all_terms) :
                                        echo '<div class="feature-tags" style="display: flex; flex-wrap: wrap; gap: 5px;">';
                                        foreach (array_slice($all_terms, 0, 3) as $term) {
                                            echo '<span style="background: #ecf0f1; color: #2c3e50; padding: 2px 6px; border-radius: 3px; font-size: 0.8rem;">' . esc_html($term->name) . '</span>';
                                        }
                                        echo '</div>';
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </a>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination(array(
                'mid_size' => 2,
                'prev_text' => '← Previous',
                'next_text' => 'Next →',
            ));
            ?>

        <?php else : ?>
            <div class="no-results" style="text-align: center; padding: 60px 20px;">
                <h2 style="font-size: 2rem; color: #2c3e50; margin-bottom: 1rem;">No Rooftops Found</h2>
                <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 2rem;">
                    We couldn't find any rooftops matching your criteria. Try adjusting your filters or check back soon!
                </p>
                <a href="<?php echo home_url(); ?>" style="background: #e74c3c; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: 600;">
                    Back to Homepage
                </a>
            </div>
        <?php endif; ?>
    </main>

    <!-- Quick Stats -->
    <aside class="stats-section" style="margin-top: 60px; padding-top: 40px; border-top: 1px solid #ecf0f1;">
        <h3 style="font-size: 1.8rem; color: #2c3e50; margin-bottom: 30px; text-align: center;">
            Barcelona Rooftops by Numbers
        </h3>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; text-align: center;">
            <?php
            $total_rooftops = wp_count_posts('rooftops')->publish;
            $total_neighborhoods = wp_count_terms('neighborhoods');
            $total_features = wp_count_terms('popular_features');
            $total_services = wp_count_terms('services');
            ?>
            
            <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 2.5rem; font-weight: 700; color: #e74c3c; margin-bottom: 10px;">
                    <?php echo $total_rooftops; ?>
                </div>
                <div style="color: #2c3e50; font-weight: 600;">Total Rooftops</div>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 2.5rem; font-weight: 700; color: #e74c3c; margin-bottom: 10px;">
                    <?php echo $total_neighborhoods; ?>
                </div>
                <div style="color: #2c3e50; font-weight: 600;">Neighborhoods</div>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 2.5rem; font-weight: 700; color: #e74c3c; margin-bottom: 10px;">
                    <?php echo $total_features; ?>
                </div>
                <div style="color: #2c3e50; font-weight: 600;">Features</div>
            </div>
            
            <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                <div style="font-size: 2.5rem; font-weight: 700; color: #e74c3c; margin-bottom: 10px;">
                    <?php echo $total_services; ?>
                </div>
                <div style="color: #2c3e50; font-weight: 600;">Services</div>
            </div>
        </div>
    </aside>
</div>

<style>
.rooftop-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

@media (max-width: 768px) {
    .rooftops-grid {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }
    
    .filter-bar > div {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
    
    .page-title {
        font-size: 2rem !important;
    }
    
    .stats-section > div {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 20px !important;
    }
}
</style>

<?php get_footer(); ?>
