(()=>{var c;"querySelector"in document&&"addEventListener"in window&&(c=document.querySelector(".generate-back-to-top"))&&(window.addEventListener("scroll",function(){var e=window.pageYOffset,t=c.getAttribute("data-start-scroll");t<e&&c.classList.add("generate-back-to-top__show"),e<t&&c.classList.remove("generate-back-to-top__show")}),c.addEventListener("click",function(e){var t,o,n,a,r;e.preventDefault(),generatepressBackToTop.smooth?(document.body,e=c.getAttribute("data-scroll-speed")||400,t=window.pageYOffset,o=document.body.offsetTop,n=(o-t)/(e/16),a=function(){window.pageYOffset<=(o||0)&&(clearInterval(r),document.activeElement.blur())},r=setInterval(function(){window.scrollBy(0,n),a()},16)):window.scrollTo(0,0)},!1))})();