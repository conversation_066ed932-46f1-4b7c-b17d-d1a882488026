caption,
td,
th {
	text-align: right;
}

.header-aligned-right:not([class*="nav-float-"]) .inside-header {
	justify-content: flex-start;
}

.header-aligned-left:not([class*="nav-float-"]) .inside-header {
	justify-content: flex-end;
}

.header-aligned-right:not([class*="nav-float-"]) .header-widget {
	order: 10;
}

.header-aligned-left:not([class*="nav-float-"]) .header-widget {
	order: -10;
}

.site-logo + .site-branding {
	order: -1;
}

.nav-float-right #site-navigation {
	order: -5;
}

.nav-float-right #site-navigation.toggled, .nav-float-right #site-navigation.has-active-search {
	order: 10;
}

.nav-float-right .header-widget {
	order: -10;
}

.nav-float-left #site-navigation {
	order: 5;
}

.nav-float-left .header-widget,
.nav-float-left .mobile-menu-control-wrapper {
	order: 10;
}

.mobile-menu-control-wrapper {
	margin-right: auto;
	margin-left: 0;
}

.nav-align-right .inside-navigation {
	justify-content: flex-start;
}

.nav-align-left .inside-navigation {
	justify-content: flex-end;
}

.menu-item-has-children .dropdown-menu-toggle {
	float: left !important;
}

.main-navigation ul ul {
	text-align: right;
}

.sidebar .menu-item-has-children .dropdown-menu-toggle,
nav ul ul .menu-item-has-children .dropdown-menu-toggle {
	float: left;
}

.comment-meta .avatar {
	float: right;
	margin-left: 10px;
}

.page-header .avatar {
	float: right;
	margin-left: 1.5em;
}

.slideout-navigation .menu-item-has-children .dropdown-menu-toggle {
	float: left;
}

.dropdown-click #generate-slideout-menu .slideout-menu .menu-item-has-children > a:first-child,
.slideout-desktop.dropdown-hover #generate-slideout-menu .slideout-menu .menu-item-has-children > a:first-child {
	padding-left: 0;
}

.comment .children {
	padding-right: 30px;
	border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.main-navigation .main-nav ul li.menu-item-has-children > a,
.secondary-navigation .main-nav ul li.menu-item-has-children > a {
	padding-left: 0;
}

nav:not(.toggled) ul ul .menu-item-has-children .dropdown-menu-toggle {
	padding-left: 15px;
}

nav:not(.toggled) .menu-item-has-children .dropdown-menu-toggle {
	padding-right: 10px;
}

.main-navigation {
	padding-right: 0;
}

ol,
ul {
	margin: 0 3em 1.5em 0;
}

li > ol,
li > ul {
	margin-right: 1.5em;
}

.menu-toggle .mobile-menu {
	margin-right: 5px;
	margin-left: 0;
}

.widget_categories .children {
	margin-right: 1.5em;
}

.widget_nav_menu ul ul,
.widget_pages ul ul {
	margin-right: 1em;
}

.cat-links:before,
.comments-link:before,
.nav-next .next:before,
.nav-previous .prev:before,
.tags-links:before {
	margin-left: 0.6em;
	margin-right: 0;
}

.entry-meta .gp-icon {
	margin-right: 0;
	margin-left: 0.6em;
}

.menu-toggle,
.nav-search-enabled .main-navigation .menu-toggle {
	text-align: right;
}

.rtl .navigation-search {
	left: auto;
	right: -99999px;
}

.rtl .navigation-search.nav-search-active {
	right: 0;
}

.main-navigation.toggled .main-nav li {
	text-align: right;
}

.left-sidebar .sidebar,
.both-left #left-sidebar,
.both-sidebars #left-sidebar {
	order: 10;
}

.both-left #right-sidebar {
	order: 5;
}

.both-right #left-sidebar {
	order: -5;
}

.both-right #right-sidebar,
.both-sidebars #right-sidebar,
.right-sidebar #right-sidebar {
	order: -10;
}

@media (max-width: 768px) {
	.site-content .content-area {
		order: -20;
	}
}
