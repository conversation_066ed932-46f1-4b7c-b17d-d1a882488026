<?php
/**
 * Template for Services taxonomy archive
 */

get_header(); ?>

<div class="container" style="padding: 60px 20px;">
    <header class="page-header" style="text-align: center; margin-bottom: 50px;">
        <?php
        $term = get_queried_object();
        ?>
        <h1 class="page-title" style="font-size: 2.5rem; color: #2c3e50; margin-bottom: 1rem;">
            <?php echo esc_html($term->name); ?> - Rooftop Services in Barcelona
        </h1>
        <?php if ($term->description) : ?>
            <p class="taxonomy-description" style="font-size: 1.2rem; color: #7f8c8d; max-width: 600px; margin: 0 auto;">
                <?php echo esc_html($term->description); ?>
            </p>
        <?php endif; ?>
    </header>

    <main class="main-content">
        <?php if (have_posts()) : ?>
            <div class="rooftops-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px; margin-bottom: 40px;">
                <?php while (have_posts()) : the_post(); ?>
                    <article class="rooftop-card" style="background: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s ease;">
                        <a href="<?php the_permalink(); ?>" style="text-decoration: none; color: inherit;">
                            <div class="rooftop-image" style="height: 250px; background-size: cover; background-position: center; background-image: url('<?php echo has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'medium') : 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'; ?>');">
                            </div>
                            
                            <div class="rooftop-content" style="padding: 25px;">
                                <h2 class="rooftop-title" style="font-size: 1.3rem; font-weight: 600; margin-bottom: 10px; color: #2c3e50;">
                                    <?php the_title(); ?>
                                </h2>
                                
                                <?php 
                                $address = get_post_meta(get_the_ID(), '_rooftop_address', true);
                                if ($address) : ?>
                                    <p class="rooftop-address" style="color: #7f8c8d; margin-bottom: 10px; font-size: 0.95rem;">
                                        📍 <?php echo esc_html($address); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if (has_excerpt()) : ?>
                                    <p class="rooftop-excerpt" style="color: #7f8c8d; line-height: 1.6; margin-bottom: 15px;">
                                        <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="rooftop-meta" style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                                    <?php 
                                    $rating = get_post_meta(get_the_ID(), '_rooftop_rating', true);
                                    if ($rating) : ?>
                                        <div class="rooftop-rating" style="display: flex; align-items: center; gap: 5px;">
                                            <span class="stars" style="color: #f39c12;">
                                                <?php
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= floor($rating)) {
                                                        echo '★';
                                                    } elseif ($i - 0.5 <= $rating) {
                                                        echo '☆';
                                                    } else {
                                                        echo '☆';
                                                    }
                                                }
                                                ?>
                                            </span>
                                            <span style="font-size: 0.9rem; color: #7f8c8d;"><?php echo esc_html($rating); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php 
                                    $price_range = get_post_meta(get_the_ID(), '_rooftop_price_range', true);
                                    if ($price_range) : ?>
                                        <span class="price-range" style="background: #e74c3c; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.85rem; font-weight: 600;">
                                            <?php echo esc_html($price_range); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="rooftop-features" style="margin-top: 15px;">
                                    <?php
                                    // Display other taxonomies
                                    $popular_features = get_the_terms(get_the_ID(), 'popular_features');
                                    $amenities = get_the_terms(get_the_ID(), 'amenities');
                                    
                                    if ($popular_features || $amenities) :
                                        echo '<div class="feature-tags" style="display: flex; flex-wrap: wrap; gap: 5px;">';
                                        
                                        if ($popular_features) {
                                            foreach (array_slice($popular_features, 0, 2) as $feature) {
                                                echo '<span style="background: #ecf0f1; color: #2c3e50; padding: 2px 6px; border-radius: 3px; font-size: 0.8rem;">' . esc_html($feature->name) . '</span>';
                                            }
                                        }
                                        
                                        if ($amenities) {
                                            foreach (array_slice($amenities, 0, 2) as $amenity) {
                                                echo '<span style="background: #ecf0f1; color: #2c3e50; padding: 2px 6px; border-radius: 3px; font-size: 0.8rem;">' . esc_html($amenity->name) . '</span>';
                                            }
                                        }
                                        
                                        echo '</div>';
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </a>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination(array(
                'mid_size' => 2,
                'prev_text' => '← Previous',
                'next_text' => 'Next →',
            ));
            ?>

        <?php else : ?>
            <div class="no-results" style="text-align: center; padding: 60px 20px;">
                <h2 style="font-size: 2rem; color: #2c3e50; margin-bottom: 1rem;">No Rooftops Found</h2>
                <p style="font-size: 1.1rem; color: #7f8c8d; margin-bottom: 2rem;">
                    We couldn't find any rooftops offering this service yet. Check back soon as we're always adding new venues!
                </p>
                <a href="<?php echo home_url(); ?>" style="background: #e74c3c; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: 600;">
                    Explore All Rooftops
                </a>
            </div>
        <?php endif; ?>
    </main>

    <!-- Related Services -->
    <aside class="related-services" style="margin-top: 60px; padding-top: 40px; border-top: 1px solid #ecf0f1;">
        <h3 style="font-size: 1.8rem; color: #2c3e50; margin-bottom: 30px; text-align: center;">
            Explore Other Services
        </h3>
        
        <div class="services-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <?php
            $other_services = get_terms(array(
                'taxonomy' => 'services',
                'hide_empty' => false,
                'exclude' => array($term->term_id),
                'number' => 4
            ));
            
            if ($other_services) :
                foreach ($other_services as $service) :
                    $count = $service->count;
                    ?>
                    <a href="<?php echo get_term_link($service); ?>" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 3px 10px rgba(0,0,0,0.1); text-decoration: none; color: inherit; text-align: center; transition: transform 0.3s ease;">
                        <h4 style="font-size: 1.1rem; color: #2c3e50; margin-bottom: 8px;">
                            <?php echo esc_html($service->name); ?>
                        </h4>
                        <p style="color: #7f8c8d; font-size: 0.9rem; margin: 0;">
                            <?php echo $count; ?> rooftop<?php echo $count !== 1 ? 's' : ''; ?>
                        </p>
                    </a>
                    <?php
                endforeach;
            endif;
            ?>
        </div>
    </aside>
</div>

<style>
.rooftop-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.related-services a:hover {
    transform: translateY(-3px);
}

@media (max-width: 768px) {
    .rooftops-grid {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }
    
    .page-title {
        font-size: 2rem !important;
    }
}
</style>

<?php get_footer(); ?>
