{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/categories", "title": "Terms List", "category": "widgets", "description": "Display a list of all terms of a given taxonomy.", "keywords": ["categories"], "textdomain": "default", "attributes": {"taxonomy": {"type": "string", "default": "category"}, "displayAsDropdown": {"type": "boolean", "default": false}, "showHierarchy": {"type": "boolean", "default": false}, "showPostCounts": {"type": "boolean", "default": false}, "showOnlyTopLevel": {"type": "boolean", "default": false}, "showEmpty": {"type": "boolean", "default": false}, "label": {"type": "string", "role": "content"}, "showLabel": {"type": "boolean", "default": true}}, "usesContext": ["enhancedPagination"], "supports": {"align": true, "html": false, "spacing": {"margin": true, "padding": true, "__experimentalDefaultControls": {"margin": false, "padding": false}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "interactivity": {"clientNavigation": true}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}}, "editorStyle": "wp-block-categories-editor", "style": "wp-block-categories"}