/**
 * Admin JavaScript for Rooftop JSON Importer
 */

(function($) {
    'use strict';

    var RooftopImporter = {
        
        init: function() {
            this.bindEvents();
            this.initFileUpload();
            this.initFormValidation();
        },
        
        bindEvents: function() {
            // Form submission
            $('.rooftop-import-form').on('submit', this.handleFormSubmit.bind(this));
            
            // File input change
            $('#json_file').on('change', this.handleFileSelect.bind(this));
            
            // Settings form
            $('form[action*="rooftop-import-settings"]').on('submit', this.handleSettingsSubmit.bind(this));
            
            // Clear logs confirmation
            $('a[href*="clear_logs"]').on('click', this.confirmClearLogs.bind(this));
        },
        
        initFileUpload: function() {
            var $fileInput = $('#json_file');
            var $uploadArea = $('.rooftop-file-upload-area');
            
            // Create drag and drop area if it doesn't exist
            if ($uploadArea.length === 0) {
                this.createUploadArea();
                $uploadArea = $('.rooftop-file-upload-area');
            }
            
            // Drag and drop events
            $uploadArea.on('dragover dragenter', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });
            
            $uploadArea.on('dragleave dragend drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
            });
            
            $uploadArea.on('drop', function(e) {
                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $fileInput[0].files = files;
                    RooftopImporter.handleFileSelect();
                }
            });
            
            // Click to upload
            $uploadArea.on('click', function() {
                $fileInput.click();
            });
        },
        
        createUploadArea: function() {
            var $fileInput = $('#json_file');
            var uploadAreaHtml = `
                <div class="rooftop-file-upload-area">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Click to select a JSON file or drag and drop</div>
                    <div class="upload-hint">Supported format: .json</div>
                </div>
            `;
            
            $fileInput.after(uploadAreaHtml);
            $fileInput.hide();
        },
        
        handleFileSelect: function() {
            var $fileInput = $('#json_file');
            var file = $fileInput[0].files[0];
            
            if (!file) {
                return;
            }
            
            // Update upload area text
            $('.upload-text').text('Selected: ' + file.name);
            
            // Validate file
            if (!this.validateFile(file)) {
                return;
            }
            
            // Preview file content
            this.previewFile(file);
        },
        
        validateFile: function(file) {
            var errors = [];
            
            // Check file type
            if (file.type !== 'application/json' && !file.name.toLowerCase().endsWith('.json')) {
                errors.push('Please select a JSON file.');
            }
            
            // Check file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                errors.push('File size must be less than 10MB.');
            }
            
            if (errors.length > 0) {
                this.showValidationErrors(errors);
                return false;
            }
            
            this.clearValidationErrors();
            return true;
        },
        
        previewFile: function(file) {
            var reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    var jsonData = JSON.parse(e.target.result);
                    RooftopImporter.showFilePreview(jsonData);
                } catch (error) {
                    RooftopImporter.showValidationErrors(['Invalid JSON format: ' + error.message]);
                }
            };
            
            reader.readAsText(file);
        },
        
        showFilePreview: function(data) {
            var previewHtml = this.generatePreviewHtml(data);
            
            // Remove existing preview
            $('.rooftop-file-preview').remove();
            
            // Add new preview
            $('.rooftop-import-form').after(previewHtml);
        },
        
        generatePreviewHtml: function(data) {
            var html = '<div class="rooftop-file-preview card">';
            html += '<h3>File Preview</h3>';
            
            // Basic info
            html += '<div class="preview-section">';
            html += '<h4>Basic Information</h4>';
            html += '<p><strong>Name:</strong> ' + (data.name || 'N/A') + '</p>';
            html += '<p><strong>ID:</strong> ' + (data.id || 'N/A') + '</p>';
            
            if (data.basicInfo && data.basicInfo.address) {
                html += '<p><strong>Address:</strong> ' + (data.basicInfo.address.street || '') + ', ' + (data.basicInfo.address.city || '') + '</p>';
                html += '<p><strong>Neighborhood:</strong> ' + (data.basicInfo.address.neighborhood || 'N/A') + '</p>';
            }
            html += '</div>';
            
            // Taxonomies preview
            html += this.generateTaxonomyPreview(data);
            
            html += '</div>';
            return html;
        },
        
        generateTaxonomyPreview: function(data) {
            var html = '<div class="preview-section">';
            html += '<h4>Taxonomies</h4>';
            
            var taxonomies = {
                'Neighborhoods': this.extractValue(data, 'basicInfo.address.neighborhood'),
                'Amenities': data.amenities || [],
                'Services': data.services || [],
                'Popular Features': data.popular || [],
                'Specialties': data.specialties || []
            };
            
            for (var taxonomy in taxonomies) {
                var terms = taxonomies[taxonomy];
                if (terms && (Array.isArray(terms) ? terms.length > 0 : terms)) {
                    html += '<div class="rooftop-taxonomy-preview">';
                    html += '<h5>' + taxonomy + '</h5>';
                    html += '<div class="taxonomy-terms">';
                    
                    if (Array.isArray(terms)) {
                        terms.forEach(function(term) {
                            html += '<span class="term-tag">' + term + '</span>';
                        });
                    } else {
                        html += '<span class="term-tag">' + terms + '</span>';
                    }
                    
                    html += '</div></div>';
                }
            }
            
            html += '</div>';
            return html;
        },
        
        extractValue: function(obj, path) {
            return path.split('.').reduce(function(current, key) {
                return current && current[key] !== undefined ? current[key] : null;
            }, obj);
        },
        
        handleFormSubmit: function(e) {
            var $form = $(e.target);
            var $submitButton = $form.find('input[type="submit"]');
            
            // Show loading state
            $submitButton.prop('disabled', true);
            $submitButton.val('Importing...');
            $form.addClass('rooftop-importing');
            
            // Show progress bar
            this.showProgressBar();
            
            // Let the form submit normally for now
            // In the future, this could be enhanced with AJAX
        },
        
        handleSettingsSubmit: function(e) {
            var $form = $(e.target);
            var $submitButton = $form.find('input[type="submit"]');
            
            $submitButton.prop('disabled', true);
            $submitButton.val('Saving...');
            
            // Form will submit normally
        },
        
        showProgressBar: function() {
            var progressHtml = `
                <div class="rooftop-import-progress">
                    <div class="rooftop-progress-bar">
                        <div class="rooftop-progress-fill"></div>
                        <div class="rooftop-progress-text">Processing...</div>
                    </div>
                </div>
            `;
            
            $('.rooftop-import-form').after(progressHtml);
            $('.rooftop-import-progress').show();
            
            // Simulate progress (since we're not using AJAX yet)
            this.simulateProgress();
        },
        
        simulateProgress: function() {
            var progress = 0;
            var interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 90) {
                    progress = 90;
                    clearInterval(interval);
                }
                
                $('.rooftop-progress-fill').css('width', progress + '%');
                $('.rooftop-progress-text').text('Processing... ' + Math.round(progress) + '%');
            }, 500);
        },
        
        showValidationErrors: function(errors) {
            this.clearValidationErrors();
            
            var errorHtml = '<div class="rooftop-validation-message error">';
            errorHtml += '<strong>Validation Errors:</strong><ul>';
            
            errors.forEach(function(error) {
                errorHtml += '<li>' + error + '</li>';
            });
            
            errorHtml += '</ul></div>';
            
            $('.rooftop-import-form').before(errorHtml);
        },
        
        clearValidationErrors: function() {
            $('.rooftop-validation-message').remove();
        },
        
        confirmClearLogs: function(e) {
            return confirm('Are you sure you want to clear all import logs? This action cannot be undone.');
        },
        
        initFormValidation: function() {
            // Real-time validation for settings
            $('input[type="checkbox"]').on('change', function() {
                var $this = $(this);
                var $row = $this.closest('tr');
                
                // Add visual feedback
                $row.addClass('setting-changed');
                setTimeout(function() {
                    $row.removeClass('setting-changed');
                }, 1000);
            });
        },
        
        // Utility functions
        showNotice: function(message, type) {
            type = type || 'info';
            var noticeHtml = '<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>';
            $('.wrap h1').after(noticeHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.notice.is-dismissible').fadeOut();
            }, 5000);
        },
        
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        RooftopImporter.init();
    });
    
    // Make RooftopImporter globally available
    window.RooftopImporter = RooftopImporter;
    
})(jQuery);
