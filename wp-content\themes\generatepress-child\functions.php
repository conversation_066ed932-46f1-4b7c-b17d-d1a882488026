<?php
/**
 * GeneratePress Child Theme Functions
 * Rooftops Barcelona Directory
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue parent and child theme styles
 */
function generatepress_child_enqueue_styles() {
    // Enqueue parent theme style
    wp_enqueue_style('generatepress-parent-style', get_template_directory_uri() . '/style.css');

    // Enqueue child theme style
    wp_enqueue_style('generatepress-child-style',
        get_stylesheet_directory_uri() . '/style.css',
        array('generatepress-parent-style'),
        wp_get_theme()->get('Version')
    );
}
add_action('wp_enqueue_scripts', 'generatepress_child_enqueue_styles');

/**
 * Register Custom Post Type: Rooftops
 */
function register_rooftops_post_type() {
    $labels = array(
        'name'                  => 'Rooftops',
        'singular_name'         => 'Rooftop',
        'menu_name'             => 'Rooftops',
        'name_admin_bar'        => 'Rooftop',
        'archives'              => 'Rooftop Archives',
        'attributes'            => 'Rooftop Attributes',
        'parent_item_colon'     => 'Parent Rooftop:',
        'all_items'             => 'All Rooftops',
        'add_new_item'          => 'Add New Rooftop',
        'add_new'               => 'Add New',
        'new_item'              => 'New Rooftop',
        'edit_item'             => 'Edit Rooftop',
        'update_item'           => 'Update Rooftop',
        'view_item'             => 'View Rooftop',
        'view_items'            => 'View Rooftops',
        'search_items'          => 'Search Rooftop',
        'not_found'             => 'Not found',
        'not_found_in_trash'    => 'Not found in Trash',
        'featured_image'        => 'Featured Image',
        'set_featured_image'    => 'Set featured image',
        'remove_featured_image' => 'Remove featured image',
        'use_featured_image'    => 'Use as featured image',
        'insert_into_item'      => 'Insert into rooftop',
        'uploaded_to_this_item' => 'Uploaded to this rooftop',
        'items_list'            => 'Rooftops list',
        'items_list_navigation' => 'Rooftops list navigation',
        'filter_items_list'     => 'Filter rooftops list',
    );

    $args = array(
        'label'                 => 'Rooftop',
        'description'           => 'Rooftops in Barcelona',
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'taxonomies'            => array('popular_features', 'services', 'amenities', 'neighborhoods'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-building',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
    );

    register_post_type('rooftops', $args);
}
add_action('init', 'register_rooftops_post_type', 0);

/**
 * Register Custom Taxonomies
 */
function register_rooftops_taxonomies() {

    // Popular Features Taxonomy
    register_taxonomy('popular_features', array('rooftops'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'              => 'Popular Features',
            'singular_name'     => 'Popular Feature',
            'search_items'      => 'Search Popular Features',
            'all_items'         => 'All Popular Features',
            'edit_item'         => 'Edit Popular Feature',
            'update_item'       => 'Update Popular Feature',
            'add_new_item'      => 'Add New Popular Feature',
            'new_item_name'     => 'New Popular Feature Name',
            'menu_name'         => 'Popular Features',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'popular'),
        'show_in_rest'      => true,
    ));

    // Services Taxonomy
    register_taxonomy('services', array('rooftops'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'              => 'Services',
            'singular_name'     => 'Service',
            'search_items'      => 'Search Services',
            'all_items'         => 'All Services',
            'edit_item'         => 'Edit Service',
            'update_item'       => 'Update Service',
            'add_new_item'      => 'Add New Service',
            'new_item_name'     => 'New Service Name',
            'menu_name'         => 'Services',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'services'),
        'show_in_rest'      => true,
    ));

    // Amenities Taxonomy
    register_taxonomy('amenities', array('rooftops'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'              => 'Amenities',
            'singular_name'     => 'Amenity',
            'search_items'      => 'Search Amenities',
            'all_items'         => 'All Amenities',
            'edit_item'         => 'Edit Amenity',
            'update_item'       => 'Update Amenity',
            'add_new_item'      => 'Add New Amenity',
            'new_item_name'     => 'New Amenity Name',
            'menu_name'         => 'Amenities',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'amenities'),
        'show_in_rest'      => true,
    ));

    // Neighborhoods Taxonomy
    register_taxonomy('neighborhoods', array('rooftops'), array(
        'hierarchical'      => true,
        'labels'            => array(
            'name'              => 'Neighborhoods',
            'singular_name'     => 'Neighborhood',
            'search_items'      => 'Search Neighborhoods',
            'all_items'         => 'All Neighborhoods',
            'parent_item'       => 'Parent Neighborhood',
            'parent_item_colon' => 'Parent Neighborhood:',
            'edit_item'         => 'Edit Neighborhood',
            'update_item'       => 'Update Neighborhood',
            'add_new_item'      => 'Add New Neighborhood',
            'new_item_name'     => 'New Neighborhood Name',
            'menu_name'         => 'Neighborhoods',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'neighborhoods'),
        'show_in_rest'      => true,
    ));
}
add_action('init', 'register_rooftops_taxonomies', 0);

/**
 * Flush rewrite rules on theme activation
 */
function rooftops_flush_rewrite_rules() {
    register_rooftops_post_type();
    register_rooftops_taxonomies();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'rooftops_flush_rewrite_rules');

/**
 * Add custom meta boxes for rooftop details
 */
function add_rooftop_meta_boxes() {
    add_meta_box(
        'rooftop_details',
        'Rooftop Details',
        'rooftop_details_callback',
        'rooftops',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_rooftop_meta_boxes');

/**
 * Meta box callback function
 */
function rooftop_details_callback($post) {
    wp_nonce_field('rooftop_details_nonce', 'rooftop_details_nonce');

    $address = get_post_meta($post->ID, '_rooftop_address', true);
    $phone = get_post_meta($post->ID, '_rooftop_phone', true);
    $website = get_post_meta($post->ID, '_rooftop_website', true);
    $rating = get_post_meta($post->ID, '_rooftop_rating', true);
    $price_range = get_post_meta($post->ID, '_rooftop_price_range', true);
    $opening_hours = get_post_meta($post->ID, '_rooftop_opening_hours', true);

    echo '<table class="form-table">';
    echo '<tr><th><label for="rooftop_address">Address</label></th>';
    echo '<td><input type="text" id="rooftop_address" name="rooftop_address" value="' . esc_attr($address) . '" style="width:100%;" /></td></tr>';

    echo '<tr><th><label for="rooftop_phone">Phone</label></th>';
    echo '<td><input type="text" id="rooftop_phone" name="rooftop_phone" value="' . esc_attr($phone) . '" style="width:100%;" /></td></tr>';

    echo '<tr><th><label for="rooftop_website">Website</label></th>';
    echo '<td><input type="url" id="rooftop_website" name="rooftop_website" value="' . esc_attr($website) . '" style="width:100%;" /></td></tr>';

    echo '<tr><th><label for="rooftop_rating">Rating (1-5)</label></th>';
    echo '<td><input type="number" id="rooftop_rating" name="rooftop_rating" value="' . esc_attr($rating) . '" min="1" max="5" step="0.1" /></td></tr>';

    echo '<tr><th><label for="rooftop_price_range">Price Range</label></th>';
    echo '<td><select id="rooftop_price_range" name="rooftop_price_range">';
    echo '<option value="">Select Price Range</option>';
    echo '<option value="€" ' . selected($price_range, '€', false) . '>€ (Budget)</option>';
    echo '<option value="€€" ' . selected($price_range, '€€', false) . '>€€ (Moderate)</option>';
    echo '<option value="€€€" ' . selected($price_range, '€€€', false) . '>€€€ (Expensive)</option>';
    echo '<option value="€€€€" ' . selected($price_range, '€€€€', false) . '>€€€€ (Luxury)</option>';
    echo '</select></td></tr>';

    echo '<tr><th><label for="rooftop_opening_hours">Opening Hours</label></th>';
    echo '<td><textarea id="rooftop_opening_hours" name="rooftop_opening_hours" rows="3" style="width:100%;">' . esc_textarea($opening_hours) . '</textarea></td></tr>';

    echo '</table>';
}

/**
 * Save meta box data
 */
function save_rooftop_details($post_id) {
    if (!isset($_POST['rooftop_details_nonce']) || !wp_verify_nonce($_POST['rooftop_details_nonce'], 'rooftop_details_nonce')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $fields = array('rooftop_address', 'rooftop_phone', 'rooftop_website', 'rooftop_rating', 'rooftop_price_range', 'rooftop_opening_hours');

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'save_rooftop_details');

/**
 * Custom homepage template detection
 */
function rooftops_home_template($template) {
    if (is_home() || is_front_page()) {
        $new_template = locate_template(array('front-page.php', 'index.php'));
        if (!empty($new_template)) {
            return $new_template;
        }
    }
    return $template;
}
add_filter('template_include', 'rooftops_home_template');

/**
 * Remove sidebar from homepage and set full width
 */
function rooftops_remove_sidebar_homepage() {
    if (is_home() || is_front_page()) {
        remove_action('generate_sidebar', 'generate_construct_sidebar');
        add_filter('generate_sidebar_layout', '__return_false');
    }
}
add_action('wp', 'rooftops_remove_sidebar_homepage');

/**
 * Force full width layout for homepage
 */
function rooftops_homepage_layout($layout) {
    if (is_home() || is_front_page()) {
        return 'no-sidebar';
    }
    return $layout;
}
add_filter('generate_sidebar_layout', 'rooftops_homepage_layout');
