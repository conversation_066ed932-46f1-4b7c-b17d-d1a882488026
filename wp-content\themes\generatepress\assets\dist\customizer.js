!function(){var e,t={822:function(e,t,r){"use strict";var a={};function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}function o(e,t,r){return(t=i(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.r(a),r.d(a,{FILE:function(){return qo},HTML:function(){return Xo},TEXT:function(){return Jo},URL:function(){return Yo}});var s=r(609),l=window.wp.element;function c(e,t){void 0!==l.createRoot?(0,l.createRoot)(t).render(e):(0,l.render)(e,t)}var u=window.wp.components;function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var f={extend:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return wp.customize.Control.extend(Object.assign({},{ready:function(){},embed:function(){var e=this,t=e.section();t&&wp.customize.section(t,(function(t){t.expanded.bind((function(t){t&&e.actuallyEmbed()}))}))},actuallyEmbed:function(){var e=this;"resolved"!==e.deferred.embedded.state()&&(e.renderContent(),e.deferred.embedded.resolve())},initialize:function(e,t){var r=this;r.setNotificationContainer=r.setNotificationContainer.bind(r),wp.customize.Control.prototype.initialize.call(r,e,t),wp.customize.control.bind("removed",(function e(t){r===t&&(r.destroy(),r.container.remove(),wp.customize.control.unbind("removed",e))}))},setNotificationContainer:function(e){this.notifications.container=jQuery(e),this.notifications.render()},getWrapper:function(){var e=this,t=e.container[0];if(e.params.choices.wrapper){var r=document.getElementById(e.params.choices.wrapper+"--wrapper");r&&(t=r,e.container.hide())}return t},renderContent:function(){var t=this,r=t.setting.get();c((0,s.createElement)(u.SlotFillProvider,null,(0,s.createElement)(e,d(d({},t.params),{},{value:r,setNotificationContainer:t.setNotificationContainer,customizerSetting:t.setting,control:t,choices:t.params.choices,default:t.params.defaultValue})),(0,s.createElement)(u.Popover.Slot,null)),t.getWrapper())},destroy:function(){(0,l.unmountComponentAtNode)(this.container[0]),wp.customize.Control.prototype.destroy&&wp.customize.Control.prototype.destroy.call(this)}},t))}},p=f;function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function h(e,t){if(e){if("string"==typeof e)return v(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(e,t):void 0}}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,i,o,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=i.call(r)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,n=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw n}}return s}}(e,t)||h(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var m=window.wp.i18n;function b(e){var t=e.color,r=e.tooltip,a=e.tooltipPosition,n=e.onClick,i=e.ariaExpanded;return(0,s.createElement)("div",{className:"components-circular-option-picker__option-wrapper"},(0,s.createElement)(u.Tooltip,{text:r,position:a},(0,s.createElement)(u.Button,{className:"components-color-palette__item components-circular-option-picker__option","aria-expanded":i,onClick:n,"aria-label":r,style:{color:t}},(0,s.createElement)("span",{className:"components-color-palette__custom-color-gradient"}))))}function S(){return(S=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function w(e,t){if(null==e)return{};var r,a,n={},i=Object.keys(e);for(a=0;a<i.length;a++)t.indexOf(r=i[a])>=0||(n[r]=e[r]);return n}function O(e){var t=(0,s.useRef)(e),r=(0,s.useRef)((function(e){t.current&&t.current(e)}));return t.current=e,r.current}var C=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1),e>r?r:e<t?t:e},E=function(e){return"touches"in e},D=function(e){return e&&e.ownerDocument.defaultView||self},N=function(e,t,r){var a=e.getBoundingClientRect(),n=E(t)?function(e,t){for(var r=0;r<e.length;r++)if(e[r].identifier===t)return e[r];return e[0]}(t.touches,r):t;return{left:C((n.pageX-(a.left+D(e).pageXOffset))/a.width),top:C((n.pageY-(a.top+D(e).pageYOffset))/a.height)}},x=function(e){!E(e)&&e.preventDefault()},T=s.memo((function(e){var t=e.onMove,r=e.onKey,a=w(e,["onMove","onKey"]),n=(0,s.useRef)(null),i=O(t),o=O(r),l=(0,s.useRef)(null),c=(0,s.useRef)(!1),u=(0,s.useMemo)((function(){var e=function(e){x(e),(E(e)?e.touches.length>0:e.buttons>0)&&n.current?i(N(n.current,e,l.current)):r(!1)},t=function(){return r(!1)};function r(r){var a=c.current,i=D(n.current),o=r?i.addEventListener:i.removeEventListener;o(a?"touchmove":"mousemove",e),o(a?"touchend":"mouseup",t)}return[function(e){var t=e.nativeEvent,a=n.current;if(a&&(x(t),!function(e,t){return t&&!E(e)}(t,c.current)&&a)){if(E(t)){c.current=!0;var o=t.changedTouches||[];o.length&&(l.current=o[0].identifier)}a.focus(),i(N(a,t,l.current)),r(!0)}},function(e){var t=e.which||e.keyCode;t<37||t>40||(e.preventDefault(),o({left:39===t?.05:37===t?-.05:0,top:40===t?.05:38===t?-.05:0}))},r]}),[o,i]),g=u[0],d=u[1],f=u[2];return(0,s.useEffect)((function(){return f}),[f]),s.createElement("div",S({},a,{onTouchStart:g,onMouseDown:g,className:"react-colorful__interactive",ref:n,onKeyDown:d,tabIndex:0,role:"slider"}))})),I=function(e){return e.filter(Boolean).join(" ")},M=function(e){var t=e.color,r=e.left,a=e.top,n=void 0===a?.5:a,i=I(["react-colorful__pointer",e.className]);return s.createElement("div",{className:i,style:{top:100*n+"%",left:100*r+"%"}},s.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:t}}))},k=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r},P=(Math.PI,function(e){var t=e.s,r=e.v,a=e.a,n=(200-t)*r/100;return{h:k(e.h),s:k(n>0&&n<200?t*r/100/(n<=100?n:200-n)*100:0),l:k(n/2),a:k(a,2)}}),_=function(e){var t=P(e);return"hsl("+t.h+", "+t.s+"%, "+t.l+"%)"},R=function(e){var t=P(e);return"hsla("+t.h+", "+t.s+"%, "+t.l+"%, "+t.a+")"},L=function(e){var t=e.h,r=e.s,a=e.v,n=e.a;t=t/360*6,r/=100,a/=100;var i=Math.floor(t),o=a*(1-r),s=a*(1-(t-i)*r),l=a*(1-(1-t+i)*r),c=i%6;return{r:k(255*[a,s,o,o,l,a][c]),g:k(255*[l,a,a,s,o,o][c]),b:k(255*[o,o,l,a,a,s][c]),a:k(n,2)}},A=function(e){var t=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?j({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}},F=A,j=function(e){var t=e.r,r=e.g,a=e.b,n=e.a,i=Math.max(t,r,a),o=i-Math.min(t,r,a),s=o?i===t?(r-a)/o:i===r?2+(a-t)/o:4+(t-r)/o:0;return{h:k(60*(s<0?s+6:s)),s:k(i?o/i*100:0),v:k(i/255*100),a:n}},V=s.memo((function(e){var t=e.hue,r=e.onChange,a=I(["react-colorful__hue",e.className]);return s.createElement("div",{className:a},s.createElement(T,{onMove:function(e){r({h:360*e.left})},onKey:function(e){r({h:C(t+360*e.left,0,360)})},"aria-label":"Hue","aria-valuenow":k(t),"aria-valuemax":"360","aria-valuemin":"0"},s.createElement(M,{className:"react-colorful__hue-pointer",left:t/360,color:_({h:t,s:100,v:100,a:1})})))})),B=s.memo((function(e){var t=e.hsva,r=e.onChange,a={backgroundColor:_({h:t.h,s:100,v:100,a:1})};return s.createElement("div",{className:"react-colorful__saturation",style:a},s.createElement(T,{onMove:function(e){r({s:100*e.left,v:100-100*e.top})},onKey:function(e){r({s:C(t.s+100*e.left,0,100),v:C(t.v-100*e.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+k(t.s)+"%, Brightness "+k(t.v)+"%"},s.createElement(M,{className:"react-colorful__saturation-pointer",top:1-t.v/100,left:t.s/100,color:_(t)})))})),H=function(e,t){return e.replace(/\s/g,"")===t.replace(/\s/g,"")};function z(e,t,r){var a=O(r),n=(0,s.useState)((function(){return e.toHsva(t)})),i=n[0],o=n[1],l=(0,s.useRef)({color:t,hsva:i});(0,s.useEffect)((function(){if(!e.equal(t,l.current.color)){var r=e.toHsva(t);l.current={hsva:r,color:t},o(r)}}),[t,e]),(0,s.useEffect)((function(){var t;(function(e,t){if(e===t)return!0;for(var r in e)if(e[r]!==t[r])return!1;return!0})(i,l.current.hsva)||e.equal(t=e.fromHsva(i),l.current.color)||(l.current={hsva:i,color:t},a(t))}),[i,e,a]);var c=(0,s.useCallback)((function(e){o((function(t){return Object.assign({},t,e)}))}),[]);return[i,c]}var U="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,G=new Map,K=function(e){U((function(){var t=e.current?e.current.ownerDocument:document;if(void 0!==t&&!G.has(t)){var a=t.createElement("style");a.innerHTML='.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}',G.set(t,a);var n=r.nc;n&&a.setAttribute("nonce",n),t.head.appendChild(a)}}),[])},$=function(e){var t=e.className,r=e.colorModel,a=e.color,n=void 0===a?r.defaultColor:a,i=e.onChange,o=w(e,["className","colorModel","color","onChange"]),l=(0,s.useRef)(null);K(l);var c=z(r,n,i),u=c[0],g=c[1],d=I(["react-colorful",t]);return s.createElement("div",S({},o,{ref:l,className:d}),s.createElement(B,{hsva:u,onChange:g}),s.createElement(V,{hue:u.h,onChange:g,className:"react-colorful__last-control"}))},W=function(e){var t=e.className,r=e.hsva,a=e.onChange,n={backgroundImage:"linear-gradient(90deg, "+R(Object.assign({},r,{a:0}))+", "+R(Object.assign({},r,{a:1}))+")"},i=I(["react-colorful__alpha",t]),o=k(100*r.a);return s.createElement("div",{className:i},s.createElement("div",{className:"react-colorful__alpha-gradient",style:n}),s.createElement(T,{onMove:function(e){a({a:e.left})},onKey:function(e){a({a:C(r.a+e.left)})},"aria-label":"Alpha","aria-valuetext":o+"%","aria-valuenow":o,"aria-valuemin":"0","aria-valuemax":"100"},s.createElement(M,{className:"react-colorful__alpha-pointer",left:r.a,color:R(r)})))},q=function(e){var t=e.className,r=e.colorModel,a=e.color,n=void 0===a?r.defaultColor:a,i=e.onChange,o=w(e,["className","colorModel","color","onChange"]),l=(0,s.useRef)(null);K(l);var c=z(r,n,i),u=c[0],g=c[1],d=I(["react-colorful",t]);return s.createElement("div",S({},o,{ref:l,className:d}),s.createElement(B,{hsva:u,onChange:g}),s.createElement(V,{hue:u.h,onChange:g}),s.createElement(W,{hsva:u,onChange:g,className:"react-colorful__last-control"}))},Y={defaultColor:"rgba(0, 0, 0, 1)",toHsva:A,fromHsva:function(e){var t=L(e);return"rgba("+t.r+", "+t.g+", "+t.b+", "+t.a+")"},equal:H},J=function(e){return s.createElement(q,S({},e,{colorModel:Y}))},X={defaultColor:"rgb(0, 0, 0)",toHsva:F,fromHsva:function(e){var t=L(e);return"rgb("+t.r+", "+t.g+", "+t.b+")"},equal:H},Z=function(e){return s.createElement($,S({},e,{colorModel:X}))},Q={grad:.9,turn:360,rad:360/(2*Math.PI)},ee=function(e){return"string"==typeof e?e.length>0:"number"==typeof e},te=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r+0},re=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1),e>r?r:e>t?e:t},ae=function(e){return(e=isFinite(e)?e%360:0)>0?e:e+360},ne=function(e){return{r:re(e.r,0,255),g:re(e.g,0,255),b:re(e.b,0,255),a:re(e.a)}},ie=function(e){return{r:te(e.r),g:te(e.g),b:te(e.b),a:te(e.a,3)}},oe=/^#([0-9a-f]{3,8})$/i,se=function(e){var t=e.toString(16);return t.length<2?"0"+t:t},le=function(e){var t=e.r,r=e.g,a=e.b,n=e.a,i=Math.max(t,r,a),o=i-Math.min(t,r,a),s=o?i===t?(r-a)/o:i===r?2+(a-t)/o:4+(t-r)/o:0;return{h:60*(s<0?s+6:s),s:i?o/i*100:0,v:i/255*100,a:n}},ce=function(e){var t=e.h,r=e.s,a=e.v,n=e.a;t=t/360*6,r/=100,a/=100;var i=Math.floor(t),o=a*(1-r),s=a*(1-(t-i)*r),l=a*(1-(1-t+i)*r),c=i%6;return{r:255*[a,s,o,o,l,a][c],g:255*[l,a,a,s,o,o][c],b:255*[o,o,l,a,a,s][c],a:n}},ue=function(e){return{h:ae(e.h),s:re(e.s,0,100),l:re(e.l,0,100),a:re(e.a)}},ge=function(e){return{h:te(e.h),s:te(e.s),l:te(e.l),a:te(e.a,3)}},de=function(e){return ce((r=(t=e).s,{h:t.h,s:(r*=((a=t.l)<50?a:100-a)/100)>0?2*r/(a+r)*100:0,v:a+r,a:t.a}));var t,r,a},fe=function(e){return{h:(t=le(e)).h,s:(n=(200-(r=t.s))*(a=t.v)/100)>0&&n<200?r*a/100/(n<=100?n:200-n)*100:0,l:n/2,a:t.a};var t,r,a,n},pe=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,ve=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,he=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,ye=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,me={string:[[function(e){var t=oe.exec(e);return t?(e=t[1]).length<=4?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:4===e.length?te(parseInt(e[3]+e[3],16)/255,2):1}:6===e.length||8===e.length?{r:parseInt(e.substr(0,2),16),g:parseInt(e.substr(2,2),16),b:parseInt(e.substr(4,2),16),a:8===e.length?te(parseInt(e.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(e){var t=he.exec(e)||ye.exec(e);return t?t[2]!==t[4]||t[4]!==t[6]?null:ne({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(e){var t=pe.exec(e)||ve.exec(e);if(!t)return null;var r,a,n=ue({h:(r=t[1],a=t[2],void 0===a&&(a="deg"),Number(r)*(Q[a]||1)),s:Number(t[3]),l:Number(t[4]),a:void 0===t[5]?1:Number(t[5])/(t[6]?100:1)});return de(n)},"hsl"]],object:[[function(e){var t=e.r,r=e.g,a=e.b,n=e.a,i=void 0===n?1:n;return ee(t)&&ee(r)&&ee(a)?ne({r:Number(t),g:Number(r),b:Number(a),a:Number(i)}):null},"rgb"],[function(e){var t=e.h,r=e.s,a=e.l,n=e.a,i=void 0===n?1:n;if(!ee(t)||!ee(r)||!ee(a))return null;var o=ue({h:Number(t),s:Number(r),l:Number(a),a:Number(i)});return de(o)},"hsl"],[function(e){var t=e.h,r=e.s,a=e.v,n=e.a,i=void 0===n?1:n;if(!ee(t)||!ee(r)||!ee(a))return null;var o=function(e){return{h:ae(e.h),s:re(e.s,0,100),v:re(e.v,0,100),a:re(e.a)}}({h:Number(t),s:Number(r),v:Number(a),a:Number(i)});return ce(o)},"hsv"]]},be=function(e,t){for(var r=0;r<t.length;r++){var a=t[r][0](e);if(a)return[a,t[r][1]]}return[null,void 0]},Se=function(e,t){var r=fe(e);return{h:r.h,s:re(r.s+100*t,0,100),l:r.l,a:r.a}},we=function(e){return(299*e.r+587*e.g+114*e.b)/1e3/255},Oe=function(e,t){var r=fe(e);return{h:r.h,s:r.s,l:re(r.l+100*t,0,100),a:r.a}},Ce=function(){function e(e){this.parsed=function(e){return"string"==typeof e?be(e.trim(),me.string):"object"==typeof e&&null!==e?be(e,me.object):[null,void 0]}(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return e.prototype.isValid=function(){return null!==this.parsed},e.prototype.brightness=function(){return te(we(this.rgba),2)},e.prototype.isDark=function(){return we(this.rgba)<.5},e.prototype.isLight=function(){return we(this.rgba)>=.5},e.prototype.toHex=function(){return t=(e=ie(this.rgba)).r,r=e.g,a=e.b,i=(n=e.a)<1?se(te(255*n)):"","#"+se(t)+se(r)+se(a)+i;var e,t,r,a,n,i},e.prototype.toRgb=function(){return ie(this.rgba)},e.prototype.toRgbString=function(){return t=(e=ie(this.rgba)).r,r=e.g,a=e.b,(n=e.a)<1?"rgba("+t+", "+r+", "+a+", "+n+")":"rgb("+t+", "+r+", "+a+")";var e,t,r,a,n},e.prototype.toHsl=function(){return ge(fe(this.rgba))},e.prototype.toHslString=function(){return t=(e=ge(fe(this.rgba))).h,r=e.s,a=e.l,(n=e.a)<1?"hsla("+t+", "+r+"%, "+a+"%, "+n+")":"hsl("+t+", "+r+"%, "+a+"%)";var e,t,r,a,n},e.prototype.toHsv=function(){return e=le(this.rgba),{h:te(e.h),s:te(e.s),v:te(e.v),a:te(e.a,3)};var e},e.prototype.invert=function(){return Ee({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},e.prototype.saturate=function(e){return void 0===e&&(e=.1),Ee(Se(this.rgba,e))},e.prototype.desaturate=function(e){return void 0===e&&(e=.1),Ee(Se(this.rgba,-e))},e.prototype.grayscale=function(){return Ee(Se(this.rgba,-1))},e.prototype.lighten=function(e){return void 0===e&&(e=.1),Ee(Oe(this.rgba,e))},e.prototype.darken=function(e){return void 0===e&&(e=.1),Ee(Oe(this.rgba,-e))},e.prototype.rotate=function(e){return void 0===e&&(e=15),this.hue(this.hue()+e)},e.prototype.alpha=function(e){return"number"==typeof e?Ee({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):te(this.rgba.a,3);var t},e.prototype.hue=function(e){var t=fe(this.rgba);return"number"==typeof e?Ee({h:e,s:t.s,l:t.l,a:t.a}):te(t.h)},e.prototype.isEqual=function(e){return this.toHex()===Ee(e).toHex()},e}(),Ee=function(e){return e instanceof Ce?e:new Ce(e)};function De(e,t,r){var a=this,n=(0,s.useRef)(null),i=(0,s.useRef)(0),o=(0,s.useRef)(null),l=(0,s.useRef)([]),c=(0,s.useRef)(),u=(0,s.useRef)(),g=(0,s.useRef)(e),d=(0,s.useRef)(!0);(0,s.useEffect)((function(){g.current=e}),[e]);var f=!t&&0!==t&&"undefined"!=typeof window;if("function"!=typeof e)throw new TypeError("Expected a function");t=+t||0;var p=!!(r=r||{}).leading,v=!("trailing"in r)||!!r.trailing,h="maxWait"in r,y=h?Math.max(+r.maxWait||0,t):null;(0,s.useEffect)((function(){return d.current=!0,function(){d.current=!1}}),[]);var m=(0,s.useMemo)((function(){var e=function(e){var t=l.current,r=c.current;return l.current=c.current=null,i.current=e,u.current=g.current.apply(r,t)},r=function(e,t){f&&cancelAnimationFrame(o.current),o.current=f?requestAnimationFrame(e):setTimeout(e,t)},s=function(e){if(!d.current)return!1;var r=e-n.current;return!n.current||r>=t||r<0||h&&e-i.current>=y},m=function(t){return o.current=null,v&&l.current?e(t):(l.current=c.current=null,u.current)},b=function e(){var a=Date.now();if(s(a))return m(a);if(d.current){var o=t-(a-n.current),l=h?Math.min(o,y-(a-i.current)):o;r(e,l)}},S=function(){var g=Date.now(),f=s(g);if(l.current=[].slice.call(arguments),c.current=a,n.current=g,f){if(!o.current&&d.current)return i.current=n.current,r(b,t),p?e(n.current):u.current;if(h)return r(b,t),e(n.current)}return o.current||r(b,t),u.current};return S.cancel=function(){o.current&&(f?cancelAnimationFrame(o.current):clearTimeout(o.current)),i.current=0,l.current=n.current=c.current=o.current=null},S.isPending=function(){return!!o.current},S.flush=function(){return o.current?m(Date.now()):u.current},S}),[p,h,t,y,v,f]);return m}function Ne(e){var t=e.value,r=e.showAlpha,a=e.onChange,n=r?J:Z,i=(0,l.useMemo)((function(){return function(e){if(String(e).startsWith("var(")){var t=e.match(/\(([^)]+)\)/);if(t){var r=getComputedStyle(document.documentElement).getPropertyValue(t[1]);r&&(e=r)}}return Ee(e).toRgbString()}(t)}),[t]),o=De(a,100);return(0,s.createElement)(n,{color:i,onChange:function(e){Ee(e).isValid()&&(e=1===Ee(e).alpha()?Ee(e).toHex():e),o(e)}})}function xe(e){return"info"===e?(0,s.createElement)("svg",{width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024","aria-hidden":"true"},(0,s.createElement)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}),(0,s.createElement)("path",{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"})):"x"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"M18 6L6 18M6 6l12 12"})):"ellipsis"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},(0,s.createElement)("rect",{x:"0",fill:"none",width:"20",height:"20"}),(0,s.createElement)("g",null,(0,s.createElement)("path",{d:"M5 10c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm12-2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-7 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"}))):"mobile"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20","aria-hidden":"true",width:"1em",height:"1em"},(0,s.createElement)("path",{d:"M6 2h8c.55 0 1 .45 1 1v14c0 .55-.45 1-1 1H6c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1zm7 12V4H7v10h6zM8 5h4l-4 5V5z",fill:"currentColor"})):"tablet"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20","aria-hidden":"true",width:"1em",height:"1em"},(0,s.createElement)("path",{d:"M4 2h12c.55 0 1 .45 1 1v14c0 .55-.45 1-1 1H4c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1zm11 14V4H5v12h10zM6 5h6l-6 5V5z",fill:"currentColor"})):"desktop"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20","aria-hidden":"true",width:"1em",height:"1em"},(0,s.createElement)("path",{d:"M3 2h14c.55 0 1 .45 1 1v10c0 .55-.45 1-1 1h-5v2h2c.55 0 1 .45 1 1v1H5v-1c0-.55.45-1 1-1h2v-2H3c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1zm13 9V4H4v7h12zM5 5h9L5 9V5z",fill:"currentColor"})):"dash"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",width:"1em",height:"1em",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"M4.5 12.75a.75.75 0 01.75-.75h13.5a.75.75 0 010 1.5H5.25a.75.75 0 01-.75-.75z",fill:"currentColor"})):"plus"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"M12 5v14M5 12h14"})):"lock"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("rect",{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2"}),(0,s.createElement)("path",{d:"M7 11V7a5 5 0 0110 0v4"})):"unlock"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("rect",{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2"}),(0,s.createElement)("path",{d:"M7 11V7a5 5 0 019.9-1"})):"chevron-down"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"M6 9l6 6 6-6"})):"chevron-up"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"M18 15l-6-6-6 6"})):"chevron-right"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"M9 18l6-6-6-6"})):"trash"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",style:{fill:"none"},stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2M10 11v6M14 11v6"})):"reorder"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",stroke:"currentColor",strokeWidth:"1",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{d:"m5 9-3 3 3 3M9 5l3-3 3 3M15 19l-3 3-3-3M19 9l3 3-3 3M2 12h20M12 2v20"})):"check"===e?(0,s.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,s.createElement)("path",{fill:"none",d:"M20 6 9 17l-5-5"})):void 0}function Te(e){var t=e.value,r=e.onChange,a=e.onBlur,n=e.onEnable,i=e.isDisabled,o=e.helpText;return(0,s.createElement)("div",{className:"generate-color-input--css-var-name-wrapper"},(0,s.createElement)(u.TextControl,{label:(0,m.__)("CSS Variable Name","generatepress"),disabled:i,help:o,type:"text",value:t,onChange:r,onBlur:a}),i&&(0,s.createElement)(u.Tooltip,{text:(0,m.__)("Changing this name will remove its color from elements already using it.","generatepress")},(0,s.createElement)(u.Button,{onClick:function(){window.alert((0,m.__)("Changing this name will break styles that are using it to define its color.","generatepress")),n(),setTimeout((function(){document.querySelector(".generate-color-input--css-var-name-wrapper input").focus()}),10)}},xe("unlock"))))}var Ie=function(e){return/^([0-9A-F]{3}){1,2}$/i.test(e)};function Me(e){var t=e.value,r=e.onChange,a=e.showReset,n=void 0!==a&&a,i=e.onClickReset;return(0,s.createElement)("div",{className:"generate-color-input-wrapper"},(0,s.createElement)(u.TextControl,{id:"generate-color-input-field",className:"generate-color-input",type:"text",value:t||"",onChange:function(e){!e.startsWith("#")&&Ie(e)&&(e="#"+e),r(e)}}),n&&(0,s.createElement)(u.Button,{isSmall:!0,isSecondary:!0,className:"components-color-clear-color",onClick:i},(0,m.__)("Default","generatepress")))}function ke(e){var t=e.value,r=e.onChange,a=generateCustomizerControls.palette,n=window.sessionStorage.getItem("generateGlobalColors");return n&&(a=JSON.parse(n)),(0,s.createElement)(u.BaseControl,{className:"generate-component-color-picker-palette"},(0,s.createElement)(u.ColorPalette,{colors:a,value:t,onChange:function(e){void 0===e&&(e=""),r(e),setTimeout((function(){document.querySelector(".generate-color-input-wrapper input").focus()}),10)},disableCustomColors:!0,clearable:!1}))}function Pe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function _e(e){var t=e.value,r=e.variableName,a=e.label,n=e.tooltipText,i=e.tooltipPosition,c=e.showAlpha,g=e.showReset,d=e.showVariableName,f=e.showPalette,p=e.variableNameIsDisabled,v=e.variableNameHelpText,h=e.onChange,m=void 0===h?function(){return!1}:h,S=e.onClosePanel,w=void 0===S?function(){return!1}:S,O=e.onChangeVariableName,C=void 0===O?function(){return!1}:O,E=e.onBlurVariableName,D=void 0===E?function(){return!1}:E,N=e.onEnableVariableName,x=void 0===N?function(){return!1}:N,T=e.onClickReset,I=void 0===T?function(){return!1}:T,M=y((0,l.useState)(!1),2),k=M[0],P=M[1],_=(0,l.useCallback)((function(){P(!0)})),R=De((0,l.useCallback)((function(){P(!1),w()})),100),L={};return generateCustomizerControls.colorPickerShouldShift&&(L.shift=!0),(0,s.createElement)("div",{className:"generate-color-picker-area"},(0,s.createElement)(b,{color:t||"transparent",tooltip:n,tooltipPosition:i,ariaExpanded:k,onClick:k?R:_}),k&&(0,s.createElement)(u.Popover,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Pe(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({position:"bottom center",className:"generate-component-color-picker",onClose:R,focusOnMount:"container"},L),(0,s.createElement)(u.BaseControl,{label:a||"",id:"generate-color-input-field",className:"generate-color-input-main-label"},(0,s.createElement)(Ne,{value:t,onChange:m,showAlpha:c}),(0,s.createElement)("div",{className:"generate-color-option-area"},d&&(0,s.createElement)(Te,{value:r,onChange:C,onBlur:D,onEnable:x,isDisabled:p,helpText:v}),(0,s.createElement)(Me,{value:t,onChange:m,showReset:g,onClickReset:I}),f&&(0,s.createElement)(ke,{value:t,onChange:m})))))}var Re=p.extend((function(e){var t,r=y((0,l.useState)(""),2),a=r[0],n=r[1];(0,l.useEffect)((function(){n(e.value)}),[]);var i=function(t){wp.customize.control(e.customizerSetting.id).setting.set(t),n(t)},o=!e.choices.hideLabel||void 0===e.choices.hideLabel;return(0,s.createElement)(s.Fragment,null,(0,s.createElement)("span",{className:"description customize-control-description",dangerouslySetInnerHTML:{__html:e.description}}),(0,s.createElement)("div",{className:"customize-control-notifications-container",ref:e.setNotificationContainer}),(0,s.createElement)(u.BaseControl,{className:"generate-component-color-picker-wrapper","data-toggleId":e.choices.toggleId?e.choices.toggleId:null},!!e.label&&o&&(0,s.createElement)("div",{className:"generate-color-component-label"},(0,s.createElement)("span",null,e.label)),(0,s.createElement)(_e,{value:a,hideLabel:!0,tooltipText:(null==e||null===(t=e.choices)||void 0===t?void 0:t.tooltip)||(0,m.__)("Choose Color","generatepress"),tooltipPosition:"top center",showAlpha:!0,showReset:!0,showVariableName:!1,showPalette:!0,variableNameIsDisabled:!0,label:e.label,onChange:function(e){i(e)},onClickReset:function(){i(e.defaultValue)}})))}),{getWrapper:function(){var e=this,t=e.container[0];if(e.params.choices.wrapper){var r=document.getElementById(e.params.choices.wrapper+"--wrapper");r&&(t=r,e.container[0].style.display="none")}return e.params.choices.toggleId&&t.setAttribute("data-toggleId",e.params.choices.toggleId),t}});function Le(e){return function(e){if(Array.isArray(e))return v(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}wp.customize.controlConstructor["generate-color-control"]=Re;var Ae=JSON.parse('{"ABeeZee":{"variants":["regular","italic"],"category":"sans-serif"},"Abel":{"variants":["regular"],"category":"sans-serif"},"Abhaya Libre":{"variants":["regular","500","600","700","800"],"category":"serif"},"Abril Fatface":{"variants":["regular"],"category":"display"},"Aclonica":{"variants":["regular"],"category":"sans-serif"},"Acme":{"variants":["regular"],"category":"sans-serif"},"Actor":{"variants":["regular"],"category":"sans-serif"},"Adamina":{"variants":["regular"],"category":"serif"},"Advent Pro":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"Aguafina Script":{"variants":["regular"],"category":"handwriting"},"Akaya Kanadaka":{"variants":["regular"],"category":"display"},"Akaya Telivigala":{"variants":["regular"],"category":"display"},"Akronim":{"variants":["regular"],"category":"display"},"Aladin":{"variants":["regular"],"category":"handwriting"},"Alata":{"variants":["regular"],"category":"sans-serif"},"Alatsi":{"variants":["regular"],"category":"sans-serif"},"Aldrich":{"variants":["regular"],"category":"sans-serif"},"Alef":{"variants":["regular","700"],"category":"sans-serif"},"Alegreya":{"variants":["regular","500","600","700","800","900","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Alegreya SC":{"variants":["regular","italic","500","500italic","700","700italic","800","800italic","900","900italic"],"category":"serif"},"Alegreya Sans":{"variants":["100","100italic","300","300italic","regular","italic","500","500italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Alegreya Sans SC":{"variants":["100","100italic","300","300italic","regular","italic","500","500italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Aleo":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"serif"},"Alex Brush":{"variants":["regular"],"category":"handwriting"},"Alfa Slab One":{"variants":["regular"],"category":"display"},"Alice":{"variants":["regular"],"category":"serif"},"Alike":{"variants":["regular"],"category":"serif"},"Alike Angular":{"variants":["regular"],"category":"serif"},"Allan":{"variants":["regular","700"],"category":"display"},"Allerta":{"variants":["regular"],"category":"sans-serif"},"Allerta Stencil":{"variants":["regular"],"category":"sans-serif"},"Allison":{"variants":["regular"],"category":"handwriting"},"Allura":{"variants":["regular"],"category":"handwriting"},"Almarai":{"variants":["300","regular","700","800"],"category":"sans-serif"},"Almendra":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Almendra Display":{"variants":["regular"],"category":"display"},"Almendra SC":{"variants":["regular"],"category":"serif"},"Alumni Sans":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Amarante":{"variants":["regular"],"category":"display"},"Amaranth":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Amatic SC":{"variants":["regular","700"],"category":"handwriting"},"Amethysta":{"variants":["regular"],"category":"serif"},"Amiko":{"variants":["regular","600","700"],"category":"sans-serif"},"Amiri":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Amita":{"variants":["regular","700"],"category":"handwriting"},"Anaheim":{"variants":["regular"],"category":"sans-serif"},"Andada Pro":{"variants":["regular","500","600","700","800","italic","500italic","600italic","700italic","800italic"],"category":"serif"},"Andika":{"variants":["regular"],"category":"sans-serif"},"Andika New Basic":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Angkor":{"variants":["regular"],"category":"display"},"Annie Use Your Telescope":{"variants":["regular"],"category":"handwriting"},"Anonymous Pro":{"variants":["regular","italic","700","700italic"],"category":"monospace"},"Antic":{"variants":["regular"],"category":"sans-serif"},"Antic Didone":{"variants":["regular"],"category":"serif"},"Antic Slab":{"variants":["regular"],"category":"serif"},"Anton":{"variants":["regular"],"category":"sans-serif"},"Antonio":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"Arapey":{"variants":["regular","italic"],"category":"serif"},"Arbutus":{"variants":["regular"],"category":"display"},"Arbutus Slab":{"variants":["regular"],"category":"serif"},"Architects Daughter":{"variants":["regular"],"category":"handwriting"},"Archivo":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Archivo Black":{"variants":["regular"],"category":"sans-serif"},"Archivo Narrow":{"variants":["regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Are You Serious":{"variants":["regular"],"category":"handwriting"},"Aref Ruqaa":{"variants":["regular","700"],"category":"serif"},"Arima Madurai":{"variants":["100","200","300","regular","500","700","800","900"],"category":"display"},"Arimo":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"sans-serif"},"Arizonia":{"variants":["regular"],"category":"handwriting"},"Armata":{"variants":["regular"],"category":"sans-serif"},"Arsenal":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Artifika":{"variants":["regular"],"category":"serif"},"Arvo":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Arya":{"variants":["regular","700"],"category":"sans-serif"},"Asap":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"sans-serif"},"Asap Condensed":{"variants":["regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Asar":{"variants":["regular"],"category":"serif"},"Asset":{"variants":["regular"],"category":"display"},"Assistant":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Astloch":{"variants":["regular","700"],"category":"display"},"Asul":{"variants":["regular","700"],"category":"sans-serif"},"Athiti":{"variants":["200","300","regular","500","600","700"],"category":"sans-serif"},"Atkinson Hyperlegible":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Atma":{"variants":["300","regular","500","600","700"],"category":"display"},"Atomic Age":{"variants":["regular"],"category":"display"},"Aubrey":{"variants":["regular"],"category":"display"},"Audiowide":{"variants":["regular"],"category":"display"},"Autour One":{"variants":["regular"],"category":"display"},"Average":{"variants":["regular"],"category":"serif"},"Average Sans":{"variants":["regular"],"category":"sans-serif"},"Averia Gruesa Libre":{"variants":["regular"],"category":"display"},"Averia Libre":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"display"},"Averia Sans Libre":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"display"},"Averia Serif Libre":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"display"},"Azeret Mono":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"monospace"},"B612":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"B612 Mono":{"variants":["regular","italic","700","700italic"],"category":"monospace"},"Bad Script":{"variants":["regular"],"category":"handwriting"},"Bahiana":{"variants":["regular"],"category":"display"},"Bahianita":{"variants":["regular"],"category":"display"},"Bai Jamjuree":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Ballet":{"variants":["regular"],"category":"handwriting"},"Baloo 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Bhai 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Bhaina 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Chettan 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Da 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Paaji 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Tamma 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Tammudu 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Baloo Thambi 2":{"variants":["regular","500","600","700","800"],"category":"display"},"Balsamiq Sans":{"variants":["regular","italic","700","700italic"],"category":"display"},"Balthazar":{"variants":["regular"],"category":"serif"},"Bangers":{"variants":["regular"],"category":"display"},"Barlow":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Barlow Condensed":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Barlow Semi Condensed":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Barriecito":{"variants":["regular"],"category":"display"},"Barrio":{"variants":["regular"],"category":"display"},"Basic":{"variants":["regular"],"category":"sans-serif"},"Baskervville":{"variants":["regular","italic"],"category":"serif"},"Battambang":{"variants":["100","300","regular","700","900"],"category":"display"},"Baumans":{"variants":["regular"],"category":"display"},"Bayon":{"variants":["regular"],"category":"sans-serif"},"Be Vietnam":{"variants":["100","100italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic"],"category":"sans-serif"},"Be Vietnam Pro":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Bebas Neue":{"variants":["regular"],"category":"display"},"Belgrano":{"variants":["regular"],"category":"serif"},"Bellefair":{"variants":["regular"],"category":"serif"},"Belleza":{"variants":["regular"],"category":"sans-serif"},"Bellota":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"display"},"Bellota Text":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"display"},"BenchNine":{"variants":["300","regular","700"],"category":"sans-serif"},"Benne":{"variants":["regular"],"category":"serif"},"Bentham":{"variants":["regular"],"category":"serif"},"Berkshire Swash":{"variants":["regular"],"category":"handwriting"},"Besley":{"variants":["regular","500","600","700","800","900","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Beth Ellen":{"variants":["regular"],"category":"handwriting"},"Bevan":{"variants":["regular"],"category":"display"},"Big Shoulders Display":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Big Shoulders Inline Display":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Big Shoulders Inline Text":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Big Shoulders Stencil Display":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Big Shoulders Stencil Text":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Big Shoulders Text":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Bigelow Rules":{"variants":["regular"],"category":"display"},"Bigshot One":{"variants":["regular"],"category":"display"},"Bilbo":{"variants":["regular"],"category":"handwriting"},"Bilbo Swash Caps":{"variants":["regular"],"category":"handwriting"},"BioRhyme":{"variants":["200","300","regular","700","800"],"category":"serif"},"BioRhyme Expanded":{"variants":["200","300","regular","700","800"],"category":"serif"},"Birthstone":{"variants":["regular"],"category":"handwriting"},"Birthstone Bounce":{"variants":["regular","500"],"category":"handwriting"},"Biryani":{"variants":["200","300","regular","600","700","800","900"],"category":"sans-serif"},"Bitter":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Black And White Picture":{"variants":["regular"],"category":"sans-serif"},"Black Han Sans":{"variants":["regular"],"category":"sans-serif"},"Black Ops One":{"variants":["regular"],"category":"display"},"Blinker":{"variants":["100","200","300","regular","600","700","800","900"],"category":"sans-serif"},"Bodoni Moda":{"variants":["regular","500","600","700","800","900","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Bokor":{"variants":["regular"],"category":"display"},"Bona Nova":{"variants":["regular","italic","700"],"category":"serif"},"Bonbon":{"variants":["regular"],"category":"handwriting"},"Bonheur Royale":{"variants":["regular"],"category":"handwriting"},"Boogaloo":{"variants":["regular"],"category":"display"},"Bowlby One":{"variants":["regular"],"category":"display"},"Bowlby One SC":{"variants":["regular"],"category":"display"},"Brawler":{"variants":["regular"],"category":"serif"},"Bree Serif":{"variants":["regular"],"category":"serif"},"Brygada 1918":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"serif"},"Bubblegum Sans":{"variants":["regular"],"category":"display"},"Bubbler One":{"variants":["regular"],"category":"sans-serif"},"Buda":{"variants":["300"],"category":"display"},"Buenard":{"variants":["regular","700"],"category":"serif"},"Bungee":{"variants":["regular"],"category":"display"},"Bungee Hairline":{"variants":["regular"],"category":"display"},"Bungee Inline":{"variants":["regular"],"category":"display"},"Bungee Outline":{"variants":["regular"],"category":"display"},"Bungee Shade":{"variants":["regular"],"category":"display"},"Butcherman":{"variants":["regular"],"category":"display"},"Butterfly Kids":{"variants":["regular"],"category":"handwriting"},"Cabin":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"sans-serif"},"Cabin Condensed":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Cabin Sketch":{"variants":["regular","700"],"category":"display"},"Caesar Dressing":{"variants":["regular"],"category":"display"},"Cagliostro":{"variants":["regular"],"category":"sans-serif"},"Cairo":{"variants":["200","300","regular","600","700","900"],"category":"sans-serif"},"Caladea":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Calistoga":{"variants":["regular"],"category":"display"},"Calligraffitti":{"variants":["regular"],"category":"handwriting"},"Cambay":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Cambo":{"variants":["regular"],"category":"serif"},"Candal":{"variants":["regular"],"category":"sans-serif"},"Cantarell":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Cantata One":{"variants":["regular"],"category":"serif"},"Cantora One":{"variants":["regular"],"category":"sans-serif"},"Capriola":{"variants":["regular"],"category":"sans-serif"},"Caramel":{"variants":["regular"],"category":"handwriting"},"Carattere":{"variants":["regular"],"category":"handwriting"},"Cardo":{"variants":["regular","italic","700"],"category":"serif"},"Carme":{"variants":["regular"],"category":"sans-serif"},"Carrois Gothic":{"variants":["regular"],"category":"sans-serif"},"Carrois Gothic SC":{"variants":["regular"],"category":"sans-serif"},"Carter One":{"variants":["regular"],"category":"display"},"Castoro":{"variants":["regular","italic"],"category":"serif"},"Catamaran":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Caudex":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Caveat":{"variants":["regular","500","600","700"],"category":"handwriting"},"Caveat Brush":{"variants":["regular"],"category":"handwriting"},"Cedarville Cursive":{"variants":["regular"],"category":"handwriting"},"Ceviche One":{"variants":["regular"],"category":"display"},"Chakra Petch":{"variants":["300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Changa":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Changa One":{"variants":["regular","italic"],"category":"display"},"Chango":{"variants":["regular"],"category":"display"},"Charm":{"variants":["regular","700"],"category":"handwriting"},"Charmonman":{"variants":["regular","700"],"category":"handwriting"},"Chathura":{"variants":["100","300","regular","700","800"],"category":"sans-serif"},"Chau Philomene One":{"variants":["regular","italic"],"category":"sans-serif"},"Chela One":{"variants":["regular"],"category":"display"},"Chelsea Market":{"variants":["regular"],"category":"display"},"Chenla":{"variants":["regular"],"category":"display"},"Cherish":{"variants":["regular"],"category":"handwriting"},"Cherry Cream Soda":{"variants":["regular"],"category":"display"},"Cherry Swash":{"variants":["regular","700"],"category":"display"},"Chewy":{"variants":["regular"],"category":"display"},"Chicle":{"variants":["regular"],"category":"display"},"Chilanka":{"variants":["regular"],"category":"handwriting"},"Chivo":{"variants":["300","300italic","regular","italic","700","700italic","900","900italic"],"category":"sans-serif"},"Chonburi":{"variants":["regular"],"category":"display"},"Cinzel":{"variants":["regular","500","600","700","800","900"],"category":"serif"},"Cinzel Decorative":{"variants":["regular","700","900"],"category":"display"},"Clicker Script":{"variants":["regular"],"category":"handwriting"},"Coda":{"variants":["regular","800"],"category":"display"},"Coda Caption":{"variants":["800"],"category":"sans-serif"},"Codystar":{"variants":["300","regular"],"category":"display"},"Coiny":{"variants":["regular"],"category":"display"},"Combo":{"variants":["regular"],"category":"display"},"Comfortaa":{"variants":["300","regular","500","600","700"],"category":"display"},"Comic Neue":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"handwriting"},"Coming Soon":{"variants":["regular"],"category":"handwriting"},"Commissioner":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Concert One":{"variants":["regular"],"category":"display"},"Condiment":{"variants":["regular"],"category":"handwriting"},"Content":{"variants":["regular","700"],"category":"display"},"Contrail One":{"variants":["regular"],"category":"display"},"Convergence":{"variants":["regular"],"category":"sans-serif"},"Cookie":{"variants":["regular"],"category":"handwriting"},"Copse":{"variants":["regular"],"category":"serif"},"Corben":{"variants":["regular","700"],"category":"display"},"Cormorant":{"variants":["300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"serif"},"Cormorant Garamond":{"variants":["300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"serif"},"Cormorant Infant":{"variants":["300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"serif"},"Cormorant SC":{"variants":["300","regular","500","600","700"],"category":"serif"},"Cormorant Unicase":{"variants":["300","regular","500","600","700"],"category":"serif"},"Cormorant Upright":{"variants":["300","regular","500","600","700"],"category":"serif"},"Courgette":{"variants":["regular"],"category":"handwriting"},"Courier Prime":{"variants":["regular","italic","700","700italic"],"category":"monospace"},"Cousine":{"variants":["regular","italic","700","700italic"],"category":"monospace"},"Coustard":{"variants":["regular","900"],"category":"serif"},"Covered By Your Grace":{"variants":["regular"],"category":"handwriting"},"Crafty Girls":{"variants":["regular"],"category":"handwriting"},"Creepster":{"variants":["regular"],"category":"display"},"Crete Round":{"variants":["regular","italic"],"category":"serif"},"Crimson Pro":{"variants":["200","300","regular","500","600","700","800","900","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Crimson Text":{"variants":["regular","italic","600","600italic","700","700italic"],"category":"serif"},"Croissant One":{"variants":["regular"],"category":"display"},"Crushed":{"variants":["regular"],"category":"display"},"Cuprum":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"sans-serif"},"Cute Font":{"variants":["regular"],"category":"display"},"Cutive":{"variants":["regular"],"category":"serif"},"Cutive Mono":{"variants":["regular"],"category":"monospace"},"DM Mono":{"variants":["300","300italic","regular","italic","500","500italic"],"category":"monospace"},"DM Sans":{"variants":["regular","italic","500","500italic","700","700italic"],"category":"sans-serif"},"DM Serif Display":{"variants":["regular","italic"],"category":"serif"},"DM Serif Text":{"variants":["regular","italic"],"category":"serif"},"Damion":{"variants":["regular"],"category":"handwriting"},"Dancing Script":{"variants":["regular","500","600","700"],"category":"handwriting"},"Dangrek":{"variants":["regular"],"category":"display"},"Darker Grotesque":{"variants":["300","regular","500","600","700","800","900"],"category":"sans-serif"},"David Libre":{"variants":["regular","500","700"],"category":"serif"},"Dawning of a New Day":{"variants":["regular"],"category":"handwriting"},"Days One":{"variants":["regular"],"category":"sans-serif"},"Dekko":{"variants":["regular"],"category":"handwriting"},"Dela Gothic One":{"variants":["regular"],"category":"display"},"Delius":{"variants":["regular"],"category":"handwriting"},"Delius Swash Caps":{"variants":["regular"],"category":"handwriting"},"Delius Unicase":{"variants":["regular","700"],"category":"handwriting"},"Della Respira":{"variants":["regular"],"category":"serif"},"Denk One":{"variants":["regular"],"category":"sans-serif"},"Devonshire":{"variants":["regular"],"category":"handwriting"},"Dhurjati":{"variants":["regular"],"category":"sans-serif"},"Didact Gothic":{"variants":["regular"],"category":"sans-serif"},"Diplomata":{"variants":["regular"],"category":"display"},"Diplomata SC":{"variants":["regular"],"category":"display"},"Do Hyeon":{"variants":["regular"],"category":"sans-serif"},"Dokdo":{"variants":["regular"],"category":"handwriting"},"Domine":{"variants":["regular","500","600","700"],"category":"serif"},"Donegal One":{"variants":["regular"],"category":"serif"},"Doppio One":{"variants":["regular"],"category":"sans-serif"},"Dorsa":{"variants":["regular"],"category":"sans-serif"},"Dosis":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"DotGothic16":{"variants":["regular"],"category":"sans-serif"},"Dr Sugiyama":{"variants":["regular"],"category":"handwriting"},"Duru Sans":{"variants":["regular"],"category":"sans-serif"},"Dynalight":{"variants":["regular"],"category":"display"},"EB Garamond":{"variants":["regular","500","600","700","800","italic","500italic","600italic","700italic","800italic"],"category":"serif"},"Eagle Lake":{"variants":["regular"],"category":"handwriting"},"East Sea Dokdo":{"variants":["regular"],"category":"handwriting"},"Eater":{"variants":["regular"],"category":"display"},"Economica":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Eczar":{"variants":["regular","500","600","700","800"],"category":"serif"},"El Messiri":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Electrolize":{"variants":["regular"],"category":"sans-serif"},"Elsie":{"variants":["regular","900"],"category":"display"},"Elsie Swash Caps":{"variants":["regular","900"],"category":"display"},"Emblema One":{"variants":["regular"],"category":"display"},"Emilys Candy":{"variants":["regular"],"category":"display"},"Encode Sans":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Encode Sans Condensed":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Encode Sans Expanded":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Encode Sans SC":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Encode Sans Semi Condensed":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Encode Sans Semi Expanded":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Engagement":{"variants":["regular"],"category":"handwriting"},"Englebert":{"variants":["regular"],"category":"sans-serif"},"Enriqueta":{"variants":["regular","500","600","700"],"category":"serif"},"Ephesis":{"variants":["regular"],"category":"handwriting"},"Epilogue":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Erica One":{"variants":["regular"],"category":"display"},"Esteban":{"variants":["regular"],"category":"serif"},"Euphoria Script":{"variants":["regular"],"category":"handwriting"},"Ewert":{"variants":["regular"],"category":"display"},"Exo":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Exo 2":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Expletus Sans":{"variants":["regular","italic","500","500italic","600","600italic","700","700italic"],"category":"display"},"Explora":{"variants":["regular"],"category":"handwriting"},"Fahkwang":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Fanwood Text":{"variants":["regular","italic"],"category":"serif"},"Farro":{"variants":["300","regular","500","700"],"category":"sans-serif"},"Farsan":{"variants":["regular"],"category":"display"},"Fascinate":{"variants":["regular"],"category":"display"},"Fascinate Inline":{"variants":["regular"],"category":"display"},"Faster One":{"variants":["regular"],"category":"display"},"Fasthand":{"variants":["regular"],"category":"display"},"Fauna One":{"variants":["regular"],"category":"serif"},"Faustina":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"serif"},"Federant":{"variants":["regular"],"category":"display"},"Federo":{"variants":["regular"],"category":"sans-serif"},"Felipa":{"variants":["regular"],"category":"handwriting"},"Fenix":{"variants":["regular"],"category":"serif"},"Festive":{"variants":["regular"],"category":"handwriting"},"Finger Paint":{"variants":["regular"],"category":"display"},"Fira Code":{"variants":["300","regular","500","600","700"],"category":"monospace"},"Fira Mono":{"variants":["regular","500","700"],"category":"monospace"},"Fira Sans":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Fira Sans Condensed":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Fira Sans Extra Condensed":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Fjalla One":{"variants":["regular"],"category":"sans-serif"},"Fjord One":{"variants":["regular"],"category":"serif"},"Flamenco":{"variants":["300","regular"],"category":"display"},"Flavors":{"variants":["regular"],"category":"display"},"Fleur De Leah":{"variants":["regular"],"category":"handwriting"},"Fondamento":{"variants":["regular","italic"],"category":"handwriting"},"Fontdiner Swanky":{"variants":["regular"],"category":"display"},"Forum":{"variants":["regular"],"category":"display"},"Francois One":{"variants":["regular"],"category":"sans-serif"},"Frank Ruhl Libre":{"variants":["300","regular","500","700","900"],"category":"serif"},"Fraunces":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Freckle Face":{"variants":["regular"],"category":"display"},"Fredericka the Great":{"variants":["regular"],"category":"display"},"Fredoka One":{"variants":["regular"],"category":"display"},"Freehand":{"variants":["regular"],"category":"display"},"Fresca":{"variants":["regular"],"category":"sans-serif"},"Frijole":{"variants":["regular"],"category":"display"},"Fruktur":{"variants":["regular"],"category":"display"},"Fugaz One":{"variants":["regular"],"category":"display"},"Fuggles":{"variants":["regular"],"category":"handwriting"},"GFS Didot":{"variants":["regular"],"category":"serif"},"GFS Neohellenic":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Gabriela":{"variants":["regular"],"category":"serif"},"Gaegu":{"variants":["300","regular","700"],"category":"handwriting"},"Gafata":{"variants":["regular"],"category":"sans-serif"},"Galada":{"variants":["regular"],"category":"display"},"Galdeano":{"variants":["regular"],"category":"sans-serif"},"Galindo":{"variants":["regular"],"category":"display"},"Gamja Flower":{"variants":["regular"],"category":"handwriting"},"Gayathri":{"variants":["100","regular","700"],"category":"sans-serif"},"Gelasio":{"variants":["regular","italic","500","500italic","600","600italic","700","700italic"],"category":"serif"},"Gemunu Libre":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Gentium Basic":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Gentium Book Basic":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Geo":{"variants":["regular","italic"],"category":"sans-serif"},"Georama":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Geostar":{"variants":["regular"],"category":"display"},"Geostar Fill":{"variants":["regular"],"category":"display"},"Germania One":{"variants":["regular"],"category":"display"},"Gideon Roman":{"variants":["regular"],"category":"display"},"Gidugu":{"variants":["regular"],"category":"sans-serif"},"Gilda Display":{"variants":["regular"],"category":"serif"},"Girassol":{"variants":["regular"],"category":"display"},"Give You Glory":{"variants":["regular"],"category":"handwriting"},"Glass Antiqua":{"variants":["regular"],"category":"display"},"Glegoo":{"variants":["regular","700"],"category":"serif"},"Gloria Hallelujah":{"variants":["regular"],"category":"handwriting"},"Glory":{"variants":["100","200","300","regular","500","600","700","800","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic"],"category":"sans-serif"},"Gluten":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Goblin One":{"variants":["regular"],"category":"display"},"Gochi Hand":{"variants":["regular"],"category":"handwriting"},"Goldman":{"variants":["regular","700"],"category":"display"},"Gorditas":{"variants":["regular","700"],"category":"display"},"Gothic A1":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Gotu":{"variants":["regular"],"category":"sans-serif"},"Goudy Bookletter 1911":{"variants":["regular"],"category":"serif"},"Gowun Batang":{"variants":["regular","700"],"category":"serif"},"Gowun Dodum":{"variants":["regular"],"category":"sans-serif"},"Graduate":{"variants":["regular"],"category":"display"},"Grand Hotel":{"variants":["regular"],"category":"handwriting"},"Grandstander":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"display"},"Gravitas One":{"variants":["regular"],"category":"display"},"Great Vibes":{"variants":["regular"],"category":"handwriting"},"Grechen Fuemen":{"variants":["regular"],"category":"handwriting"},"Grenze":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"serif"},"Grenze Gotisch":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Grey Qo":{"variants":["regular"],"category":"handwriting"},"Griffy":{"variants":["regular"],"category":"display"},"Gruppo":{"variants":["regular"],"category":"display"},"Gudea":{"variants":["regular","italic","700"],"category":"sans-serif"},"Gugi":{"variants":["regular"],"category":"display"},"Gupter":{"variants":["regular","500","700"],"category":"serif"},"Gurajada":{"variants":["regular"],"category":"serif"},"Habibi":{"variants":["regular"],"category":"serif"},"Hachi Maru Pop":{"variants":["regular"],"category":"handwriting"},"Hahmlet":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Halant":{"variants":["300","regular","500","600","700"],"category":"serif"},"Hammersmith One":{"variants":["regular"],"category":"sans-serif"},"Hanalei":{"variants":["regular"],"category":"display"},"Hanalei Fill":{"variants":["regular"],"category":"display"},"Handlee":{"variants":["regular"],"category":"handwriting"},"Hanuman":{"variants":["100","300","regular","700","900"],"category":"serif"},"Happy Monkey":{"variants":["regular"],"category":"display"},"Harmattan":{"variants":["regular","700"],"category":"sans-serif"},"Headland One":{"variants":["regular"],"category":"serif"},"Heebo":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Henny Penny":{"variants":["regular"],"category":"display"},"Hepta Slab":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Herr Von Muellerhoff":{"variants":["regular"],"category":"handwriting"},"Hi Melody":{"variants":["regular"],"category":"handwriting"},"Hina Mincho":{"variants":["regular"],"category":"serif"},"Hind":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Hind Guntur":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Hind Madurai":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Hind Siliguri":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Hind Vadodara":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Holtwood One SC":{"variants":["regular"],"category":"serif"},"Homemade Apple":{"variants":["regular"],"category":"handwriting"},"Homenaje":{"variants":["regular"],"category":"sans-serif"},"IBM Plex Mono":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"monospace"},"IBM Plex Sans":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"IBM Plex Sans Arabic":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"IBM Plex Sans Condensed":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"IBM Plex Sans Devanagari":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"IBM Plex Sans Hebrew":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"IBM Plex Sans KR":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"IBM Plex Sans Thai":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"IBM Plex Sans Thai Looped":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"IBM Plex Serif":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"serif"},"IM Fell DW Pica":{"variants":["regular","italic"],"category":"serif"},"IM Fell DW Pica SC":{"variants":["regular"],"category":"serif"},"IM Fell Double Pica":{"variants":["regular","italic"],"category":"serif"},"IM Fell Double Pica SC":{"variants":["regular"],"category":"serif"},"IM Fell English":{"variants":["regular","italic"],"category":"serif"},"IM Fell English SC":{"variants":["regular"],"category":"serif"},"IM Fell French Canon":{"variants":["regular","italic"],"category":"serif"},"IM Fell French Canon SC":{"variants":["regular"],"category":"serif"},"IM Fell Great Primer":{"variants":["regular","italic"],"category":"serif"},"IM Fell Great Primer SC":{"variants":["regular"],"category":"serif"},"Ibarra Real Nova":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"serif"},"Iceberg":{"variants":["regular"],"category":"display"},"Iceland":{"variants":["regular"],"category":"display"},"Imbue":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Imprima":{"variants":["regular"],"category":"sans-serif"},"Inconsolata":{"variants":["200","300","regular","500","600","700","800","900"],"category":"monospace"},"Inder":{"variants":["regular"],"category":"sans-serif"},"Indie Flower":{"variants":["regular"],"category":"handwriting"},"Inika":{"variants":["regular","700"],"category":"serif"},"Inknut Antiqua":{"variants":["300","regular","500","600","700","800","900"],"category":"serif"},"Inria Sans":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"sans-serif"},"Inria Serif":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"serif"},"Inter":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Irish Grover":{"variants":["regular"],"category":"display"},"Istok Web":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Italiana":{"variants":["regular"],"category":"serif"},"Italianno":{"variants":["regular"],"category":"handwriting"},"Itim":{"variants":["regular"],"category":"handwriting"},"Jacques Francois":{"variants":["regular"],"category":"serif"},"Jacques Francois Shadow":{"variants":["regular"],"category":"display"},"Jaldi":{"variants":["regular","700"],"category":"sans-serif"},"JetBrains Mono":{"variants":["100","200","300","regular","500","600","700","800","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic"],"category":"monospace"},"Jim Nightshade":{"variants":["regular"],"category":"handwriting"},"Jockey One":{"variants":["regular"],"category":"sans-serif"},"Jolly Lodger":{"variants":["regular"],"category":"display"},"Jomhuria":{"variants":["regular"],"category":"display"},"Jomolhari":{"variants":["regular"],"category":"serif"},"Josefin Sans":{"variants":["100","200","300","regular","500","600","700","100italic","200italic","300italic","italic","500italic","600italic","700italic"],"category":"sans-serif"},"Josefin Slab":{"variants":["100","200","300","regular","500","600","700","100italic","200italic","300italic","italic","500italic","600italic","700italic"],"category":"serif"},"Jost":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Joti One":{"variants":["regular"],"category":"display"},"Jua":{"variants":["regular"],"category":"sans-serif"},"Judson":{"variants":["regular","italic","700"],"category":"serif"},"Julee":{"variants":["regular"],"category":"handwriting"},"Julius Sans One":{"variants":["regular"],"category":"sans-serif"},"Junge":{"variants":["regular"],"category":"serif"},"Jura":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Just Another Hand":{"variants":["regular"],"category":"handwriting"},"Just Me Again Down Here":{"variants":["regular"],"category":"handwriting"},"K2D":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic"],"category":"sans-serif"},"Kadwa":{"variants":["regular","700"],"category":"serif"},"Kaisei Decol":{"variants":["regular","500","700"],"category":"serif"},"Kaisei HarunoUmi":{"variants":["regular","500","700"],"category":"serif"},"Kaisei Opti":{"variants":["regular","500","700"],"category":"serif"},"Kaisei Tokumin":{"variants":["regular","500","700","800"],"category":"serif"},"Kalam":{"variants":["300","regular","700"],"category":"handwriting"},"Kameron":{"variants":["regular","700"],"category":"serif"},"Kanit":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Kantumruy":{"variants":["300","regular","700"],"category":"sans-serif"},"Karantina":{"variants":["300","regular","700"],"category":"display"},"Karla":{"variants":["200","300","regular","500","600","700","800","200italic","300italic","italic","500italic","600italic","700italic","800italic"],"category":"sans-serif"},"Karma":{"variants":["300","regular","500","600","700"],"category":"serif"},"Katibeh":{"variants":["regular"],"category":"display"},"Kaushan Script":{"variants":["regular"],"category":"handwriting"},"Kavivanar":{"variants":["regular"],"category":"handwriting"},"Kavoon":{"variants":["regular"],"category":"display"},"Kdam Thmor":{"variants":["regular"],"category":"display"},"Keania One":{"variants":["regular"],"category":"display"},"Kelly Slab":{"variants":["regular"],"category":"display"},"Kenia":{"variants":["regular"],"category":"display"},"Khand":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Khmer":{"variants":["regular"],"category":"display"},"Khula":{"variants":["300","regular","600","700","800"],"category":"sans-serif"},"Kirang Haerang":{"variants":["regular"],"category":"display"},"Kite One":{"variants":["regular"],"category":"sans-serif"},"Kiwi Maru":{"variants":["300","regular","500"],"category":"serif"},"Klee One":{"variants":["regular","600"],"category":"handwriting"},"Knewave":{"variants":["regular"],"category":"display"},"KoHo":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Kodchasan":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Koh Santepheap":{"variants":["100","300","regular","700","900"],"category":"display"},"Kosugi":{"variants":["regular"],"category":"sans-serif"},"Kosugi Maru":{"variants":["regular"],"category":"sans-serif"},"Kotta One":{"variants":["regular"],"category":"serif"},"Koulen":{"variants":["regular"],"category":"display"},"Kranky":{"variants":["regular"],"category":"display"},"Kreon":{"variants":["300","regular","500","600","700"],"category":"serif"},"Kristi":{"variants":["regular"],"category":"handwriting"},"Krona One":{"variants":["regular"],"category":"sans-serif"},"Krub":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Kufam":{"variants":["regular","500","600","700","800","900","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Kulim Park":{"variants":["200","200italic","300","300italic","regular","italic","600","600italic","700","700italic"],"category":"sans-serif"},"Kumar One":{"variants":["regular"],"category":"display"},"Kumar One Outline":{"variants":["regular"],"category":"display"},"Kumbh Sans":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Kurale":{"variants":["regular"],"category":"serif"},"La Belle Aurore":{"variants":["regular"],"category":"handwriting"},"Lacquer":{"variants":["regular"],"category":"display"},"Laila":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Lakki Reddy":{"variants":["regular"],"category":"handwriting"},"Lalezar":{"variants":["regular"],"category":"display"},"Lancelot":{"variants":["regular"],"category":"display"},"Langar":{"variants":["regular"],"category":"display"},"Lateef":{"variants":["regular"],"category":"handwriting"},"Lato":{"variants":["100","100italic","300","300italic","regular","italic","700","700italic","900","900italic"],"category":"sans-serif"},"League Script":{"variants":["regular"],"category":"handwriting"},"Leckerli One":{"variants":["regular"],"category":"handwriting"},"Ledger":{"variants":["regular"],"category":"serif"},"Lekton":{"variants":["regular","italic","700"],"category":"sans-serif"},"Lemon":{"variants":["regular"],"category":"display"},"Lemonada":{"variants":["300","regular","500","600","700"],"category":"display"},"Lexend":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Lexend Deca":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Lexend Exa":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Lexend Giga":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Lexend Mega":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Lexend Peta":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Lexend Tera":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Lexend Zetta":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Libre Barcode 128":{"variants":["regular"],"category":"display"},"Libre Barcode 128 Text":{"variants":["regular"],"category":"display"},"Libre Barcode 39":{"variants":["regular"],"category":"display"},"Libre Barcode 39 Extended":{"variants":["regular"],"category":"display"},"Libre Barcode 39 Extended Text":{"variants":["regular"],"category":"display"},"Libre Barcode 39 Text":{"variants":["regular"],"category":"display"},"Libre Barcode EAN13 Text":{"variants":["regular"],"category":"display"},"Libre Baskerville":{"variants":["regular","italic","700"],"category":"serif"},"Libre Caslon Display":{"variants":["regular"],"category":"serif"},"Libre Caslon Text":{"variants":["regular","italic","700"],"category":"serif"},"Libre Franklin":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Life Savers":{"variants":["regular","700","800"],"category":"display"},"Lilita One":{"variants":["regular"],"category":"display"},"Lily Script One":{"variants":["regular"],"category":"display"},"Limelight":{"variants":["regular"],"category":"display"},"Linden Hill":{"variants":["regular","italic"],"category":"serif"},"Literata":{"variants":["200","300","regular","500","600","700","800","900","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Liu Jian Mao Cao":{"variants":["regular"],"category":"handwriting"},"Livvic":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","900","900italic"],"category":"sans-serif"},"Lobster":{"variants":["regular"],"category":"display"},"Lobster Two":{"variants":["regular","italic","700","700italic"],"category":"display"},"Londrina Outline":{"variants":["regular"],"category":"display"},"Londrina Shadow":{"variants":["regular"],"category":"display"},"Londrina Sketch":{"variants":["regular"],"category":"display"},"Londrina Solid":{"variants":["100","300","regular","900"],"category":"display"},"Long Cang":{"variants":["regular"],"category":"handwriting"},"Lora":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"serif"},"Love Ya Like A Sister":{"variants":["regular"],"category":"display"},"Loved by the King":{"variants":["regular"],"category":"handwriting"},"Lovers Quarrel":{"variants":["regular"],"category":"handwriting"},"Luckiest Guy":{"variants":["regular"],"category":"display"},"Lusitana":{"variants":["regular","700"],"category":"serif"},"Lustria":{"variants":["regular"],"category":"serif"},"M PLUS 1p":{"variants":["100","300","regular","500","700","800","900"],"category":"sans-serif"},"M PLUS Rounded 1c":{"variants":["100","300","regular","500","700","800","900"],"category":"sans-serif"},"Ma Shan Zheng":{"variants":["regular"],"category":"handwriting"},"Macondo":{"variants":["regular"],"category":"display"},"Macondo Swash Caps":{"variants":["regular"],"category":"display"},"Mada":{"variants":["200","300","regular","500","600","700","900"],"category":"sans-serif"},"Magra":{"variants":["regular","700"],"category":"sans-serif"},"Maiden Orange":{"variants":["regular"],"category":"display"},"Maitree":{"variants":["200","300","regular","500","600","700"],"category":"serif"},"Major Mono Display":{"variants":["regular"],"category":"monospace"},"Mako":{"variants":["regular"],"category":"sans-serif"},"Mali":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"handwriting"},"Mallanna":{"variants":["regular"],"category":"sans-serif"},"Mandali":{"variants":["regular"],"category":"sans-serif"},"Manjari":{"variants":["100","regular","700"],"category":"sans-serif"},"Manrope":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Mansalva":{"variants":["regular"],"category":"handwriting"},"Manuale":{"variants":["300","regular","500","600","700","800","300italic","italic","500italic","600italic","700italic","800italic"],"category":"serif"},"Marcellus":{"variants":["regular"],"category":"serif"},"Marcellus SC":{"variants":["regular"],"category":"serif"},"Marck Script":{"variants":["regular"],"category":"handwriting"},"Margarine":{"variants":["regular"],"category":"display"},"Markazi Text":{"variants":["regular","500","600","700"],"category":"serif"},"Marko One":{"variants":["regular"],"category":"serif"},"Marmelad":{"variants":["regular"],"category":"sans-serif"},"Martel":{"variants":["200","300","regular","600","700","800","900"],"category":"serif"},"Martel Sans":{"variants":["200","300","regular","600","700","800","900"],"category":"sans-serif"},"Marvel":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Mate":{"variants":["regular","italic"],"category":"serif"},"Mate SC":{"variants":["regular"],"category":"serif"},"Maven Pro":{"variants":["regular","500","600","700","800","900"],"category":"sans-serif"},"McLaren":{"variants":["regular"],"category":"display"},"Meddon":{"variants":["regular"],"category":"handwriting"},"MedievalSharp":{"variants":["regular"],"category":"display"},"Medula One":{"variants":["regular"],"category":"display"},"Meera Inimai":{"variants":["regular"],"category":"sans-serif"},"Megrim":{"variants":["regular"],"category":"display"},"Meie Script":{"variants":["regular"],"category":"handwriting"},"Merienda":{"variants":["regular","700"],"category":"handwriting"},"Merienda One":{"variants":["regular"],"category":"handwriting"},"Merriweather":{"variants":["300","300italic","regular","italic","700","700italic","900","900italic"],"category":"serif"},"Merriweather Sans":{"variants":["300","regular","500","600","700","800","300italic","italic","500italic","600italic","700italic","800italic"],"category":"sans-serif"},"Metal":{"variants":["regular"],"category":"display"},"Metal Mania":{"variants":["regular"],"category":"display"},"Metamorphous":{"variants":["regular"],"category":"display"},"Metrophobic":{"variants":["regular"],"category":"sans-serif"},"Michroma":{"variants":["regular"],"category":"sans-serif"},"Milonga":{"variants":["regular"],"category":"display"},"Miltonian":{"variants":["regular"],"category":"display"},"Miltonian Tattoo":{"variants":["regular"],"category":"display"},"Mina":{"variants":["regular","700"],"category":"sans-serif"},"Miniver":{"variants":["regular"],"category":"display"},"Miriam Libre":{"variants":["regular","700"],"category":"sans-serif"},"Mirza":{"variants":["regular","500","600","700"],"category":"display"},"Miss Fajardose":{"variants":["regular"],"category":"handwriting"},"Mitr":{"variants":["200","300","regular","500","600","700"],"category":"sans-serif"},"Modak":{"variants":["regular"],"category":"display"},"Modern Antiqua":{"variants":["regular"],"category":"display"},"Mogra":{"variants":["regular"],"category":"display"},"Molengo":{"variants":["regular"],"category":"sans-serif"},"Molle":{"variants":["italic"],"category":"handwriting"},"Monda":{"variants":["regular","700"],"category":"sans-serif"},"Monofett":{"variants":["regular"],"category":"display"},"Monoton":{"variants":["regular"],"category":"display"},"Monsieur La Doulaise":{"variants":["regular"],"category":"handwriting"},"Montaga":{"variants":["regular"],"category":"serif"},"MonteCarlo":{"variants":["regular"],"category":"handwriting"},"Montez":{"variants":["regular"],"category":"handwriting"},"Montserrat":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Montserrat Alternates":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Montserrat Subrayada":{"variants":["regular","700"],"category":"sans-serif"},"Moul":{"variants":["regular"],"category":"display"},"Moulpali":{"variants":["regular"],"category":"display"},"Mountains of Christmas":{"variants":["regular","700"],"category":"display"},"Mouse Memoirs":{"variants":["regular"],"category":"sans-serif"},"Mr Bedfort":{"variants":["regular"],"category":"handwriting"},"Mr Dafoe":{"variants":["regular"],"category":"handwriting"},"Mr De Haviland":{"variants":["regular"],"category":"handwriting"},"Mrs Saint Delafield":{"variants":["regular"],"category":"handwriting"},"Mrs Sheppards":{"variants":["regular"],"category":"handwriting"},"Mukta":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Mukta Mahee":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Mukta Malar":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Mukta Vaani":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Mulish":{"variants":["200","300","regular","500","600","700","800","900","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"MuseoModerno":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"display"},"Mystery Quest":{"variants":["regular"],"category":"display"},"NTR":{"variants":["regular"],"category":"sans-serif"},"Nanum Brush Script":{"variants":["regular"],"category":"handwriting"},"Nanum Gothic":{"variants":["regular","700","800"],"category":"sans-serif"},"Nanum Gothic Coding":{"variants":["regular","700"],"category":"monospace"},"Nanum Myeongjo":{"variants":["regular","700","800"],"category":"serif"},"Nanum Pen Script":{"variants":["regular"],"category":"handwriting"},"Nerko One":{"variants":["regular"],"category":"handwriting"},"Neucha":{"variants":["regular"],"category":"handwriting"},"Neuton":{"variants":["200","300","regular","italic","700","800"],"category":"serif"},"New Rocker":{"variants":["regular"],"category":"display"},"New Tegomin":{"variants":["regular"],"category":"serif"},"News Cycle":{"variants":["regular","700"],"category":"sans-serif"},"Newsreader":{"variants":["200","300","regular","500","600","700","800","200italic","300italic","italic","500italic","600italic","700italic","800italic"],"category":"serif"},"Niconne":{"variants":["regular"],"category":"handwriting"},"Niramit":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"sans-serif"},"Nixie One":{"variants":["regular"],"category":"display"},"Nobile":{"variants":["regular","italic","500","500italic","700","700italic"],"category":"sans-serif"},"Nokora":{"variants":["regular","700"],"category":"serif"},"Norican":{"variants":["regular"],"category":"handwriting"},"Nosifer":{"variants":["regular"],"category":"display"},"Notable":{"variants":["regular"],"category":"sans-serif"},"Nothing You Could Do":{"variants":["regular"],"category":"handwriting"},"Noticia Text":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Noto Kufi Arabic":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Music":{"variants":["regular"],"category":"sans-serif"},"Noto Naskh Arabic":{"variants":["regular","500","600","700"],"category":"serif"},"Noto Nastaliq Urdu":{"variants":["regular","700"],"category":"serif"},"Noto Rashi Hebrew":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Sans":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Noto Sans Adlam":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Adlam Unjoined":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Anatolian Hieroglyphs":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Arabic":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Armenian":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Avestan":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Balinese":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Bamum":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Bassa Vah":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Batak":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Bengali":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Bhaiksuki":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Brahmi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Buginese":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Buhid":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Canadian Aboriginal":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Carian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Caucasian Albanian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Chakma":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Cham":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Cherokee":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Coptic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Cuneiform":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Cypriot":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Deseret":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Devanagari":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Display":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Noto Sans Duployan":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Egyptian Hieroglyphs":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Elbasan":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Elymaic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Georgian":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Glagolitic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Gothic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Grantha":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Gujarati":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Gunjala Gondi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Gurmukhi":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans HK":{"variants":["100","300","regular","500","700","900"],"category":"sans-serif"},"Noto Sans Hanifi Rohingya":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Hanunoo":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Hatran":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Hebrew":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Imperial Aramaic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Indic Siyaq Numbers":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Inscriptional Pahlavi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Inscriptional Parthian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans JP":{"variants":["100","300","regular","500","700","900"],"category":"sans-serif"},"Noto Sans Javanese":{"variants":["regular","700"],"category":"sans-serif"},"Noto Sans KR":{"variants":["100","300","regular","500","700","900"],"category":"sans-serif"},"Noto Sans Kaithi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Kannada":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Kayah Li":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Kharoshthi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Khmer":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Khojki":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Khudawadi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Lao":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Lepcha":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Limbu":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Linear A":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Linear B":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Lisu":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Lycian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Lydian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Mahajani":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Malayalam":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Mandaic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Manichaean":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Marchen":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Masaram Gondi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Math":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Mayan Numerals":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Medefaidrin":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Meroitic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Miao":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Modi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Mongolian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Mono":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"monospace"},"Noto Sans Mro":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Multani":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Myanmar":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans N Ko":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Nabataean":{"variants":["regular"],"category":"sans-serif"},"Noto Sans New Tai Lue":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Newa":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Nushu":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Ogham":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Ol Chiki":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Old Hungarian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Old Italic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Old North Arabian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Old Permic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Old Persian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Old Sogdian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Old South Arabian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Old Turkic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Oriya":{"variants":["100","regular","700","900"],"category":"sans-serif"},"Noto Sans Osage":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Osmanya":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Pahawh Hmong":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Palmyrene":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Pau Cin Hau":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Phags Pa":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Phoenician":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Psalter Pahlavi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Rejang":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Runic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans SC":{"variants":["100","300","regular","500","700","900"],"category":"sans-serif"},"Noto Sans Samaritan":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Saurashtra":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Sharada":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Shavian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Siddham":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Sinhala":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Sogdian":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Sora Sompeng":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Soyombo":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Sundanese":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Syloti Nagri":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Symbols":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Symbols 2":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Syriac":{"variants":["100","regular","900"],"category":"sans-serif"},"Noto Sans TC":{"variants":["100","300","regular","500","700","900"],"category":"sans-serif"},"Noto Sans Tagalog":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Tagbanwa":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Tai Le":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Tai Tham":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Noto Sans Tai Viet":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Takri":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Tamil":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Tamil Supplement":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Telugu":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Thaana":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Thai":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Thai Looped":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Noto Sans Tifinagh":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Tirhuta":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Ugaritic":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Vai":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Wancho":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Warang Citi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Yi":{"variants":["regular"],"category":"sans-serif"},"Noto Sans Zanabazar Square":{"variants":["regular"],"category":"sans-serif"},"Noto Serif":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Noto Serif Ahom":{"variants":["regular"],"category":"serif"},"Noto Serif Armenian":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Balinese":{"variants":["regular"],"category":"serif"},"Noto Serif Bengali":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Devanagari":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Display":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Noto Serif Dogra":{"variants":["regular"],"category":"serif"},"Noto Serif Ethiopic":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Georgian":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Grantha":{"variants":["regular"],"category":"serif"},"Noto Serif Gujarati":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Gurmukhi":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Hebrew":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif JP":{"variants":["200","300","regular","500","600","700","900"],"category":"serif"},"Noto Serif KR":{"variants":["200","300","regular","500","600","700","900"],"category":"serif"},"Noto Serif Kannada":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Khmer":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Lao":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Malayalam":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Myanmar":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Nyiakeng Puachue Hmong":{"variants":["regular","500","600","700"],"category":"serif"},"Noto Serif SC":{"variants":["200","300","regular","500","600","700","900"],"category":"serif"},"Noto Serif Sinhala":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif TC":{"variants":["200","300","regular","500","600","700","900"],"category":"serif"},"Noto Serif Tamil":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Noto Serif Tangut":{"variants":["regular"],"category":"serif"},"Noto Serif Telugu":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Thai":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Tibetan":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Noto Serif Yezidi":{"variants":["regular","500","600","700"],"category":"serif"},"Noto Traditional Nushu":{"variants":["regular"],"category":"sans-serif"},"Nova Cut":{"variants":["regular"],"category":"display"},"Nova Flat":{"variants":["regular"],"category":"display"},"Nova Mono":{"variants":["regular"],"category":"monospace"},"Nova Oval":{"variants":["regular"],"category":"display"},"Nova Round":{"variants":["regular"],"category":"display"},"Nova Script":{"variants":["regular"],"category":"display"},"Nova Slim":{"variants":["regular"],"category":"display"},"Nova Square":{"variants":["regular"],"category":"display"},"Numans":{"variants":["regular"],"category":"sans-serif"},"Nunito":{"variants":["200","200italic","300","300italic","regular","italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Nunito Sans":{"variants":["200","200italic","300","300italic","regular","italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Odibee Sans":{"variants":["regular"],"category":"display"},"Odor Mean Chey":{"variants":["regular"],"category":"serif"},"Offside":{"variants":["regular"],"category":"display"},"Oi":{"variants":["regular"],"category":"display"},"Old Standard TT":{"variants":["regular","italic","700"],"category":"serif"},"Oldenburg":{"variants":["regular"],"category":"display"},"Oleo Script":{"variants":["regular","700"],"category":"display"},"Oleo Script Swash Caps":{"variants":["regular","700"],"category":"display"},"Open Sans":{"variants":["300","300italic","regular","italic","600","600italic","700","700italic","800","800italic"],"category":"sans-serif"},"Open Sans Condensed":{"variants":["300","300italic","700"],"category":"sans-serif"},"Oranienbaum":{"variants":["regular"],"category":"serif"},"Orbitron":{"variants":["regular","500","600","700","800","900"],"category":"sans-serif"},"Oregano":{"variants":["regular","italic"],"category":"display"},"Orelega One":{"variants":["regular"],"category":"display"},"Orienta":{"variants":["regular"],"category":"sans-serif"},"Original Surfer":{"variants":["regular"],"category":"display"},"Oswald":{"variants":["200","300","regular","500","600","700"],"category":"sans-serif"},"Otomanopee One":{"variants":["regular"],"category":"sans-serif"},"Over the Rainbow":{"variants":["regular"],"category":"handwriting"},"Overlock":{"variants":["regular","italic","700","700italic","900","900italic"],"category":"display"},"Overlock SC":{"variants":["regular"],"category":"display"},"Overpass":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Overpass Mono":{"variants":["300","regular","600","700"],"category":"monospace"},"Ovo":{"variants":["regular"],"category":"serif"},"Oxanium":{"variants":["200","300","regular","500","600","700","800"],"category":"display"},"Oxygen":{"variants":["300","regular","700"],"category":"sans-serif"},"Oxygen Mono":{"variants":["regular"],"category":"monospace"},"PT Mono":{"variants":["regular"],"category":"monospace"},"PT Sans":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"PT Sans Caption":{"variants":["regular","700"],"category":"sans-serif"},"PT Sans Narrow":{"variants":["regular","700"],"category":"sans-serif"},"PT Serif":{"variants":["regular","italic","700","700italic"],"category":"serif"},"PT Serif Caption":{"variants":["regular","italic"],"category":"serif"},"Pacifico":{"variants":["regular"],"category":"handwriting"},"Padauk":{"variants":["regular","700"],"category":"sans-serif"},"Palanquin":{"variants":["100","200","300","regular","500","600","700"],"category":"sans-serif"},"Palanquin Dark":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Palette Mosaic":{"variants":["regular"],"category":"display"},"Pangolin":{"variants":["regular"],"category":"handwriting"},"Paprika":{"variants":["regular"],"category":"display"},"Parisienne":{"variants":["regular"],"category":"handwriting"},"Passero One":{"variants":["regular"],"category":"display"},"Passion One":{"variants":["regular","700","900"],"category":"display"},"Pathway Gothic One":{"variants":["regular"],"category":"sans-serif"},"Patrick Hand":{"variants":["regular"],"category":"handwriting"},"Patrick Hand SC":{"variants":["regular"],"category":"handwriting"},"Pattaya":{"variants":["regular"],"category":"sans-serif"},"Patua One":{"variants":["regular"],"category":"display"},"Pavanam":{"variants":["regular"],"category":"sans-serif"},"Paytone One":{"variants":["regular"],"category":"sans-serif"},"Peddana":{"variants":["regular"],"category":"serif"},"Peralta":{"variants":["regular"],"category":"display"},"Permanent Marker":{"variants":["regular"],"category":"handwriting"},"Petit Formal Script":{"variants":["regular"],"category":"handwriting"},"Petrona":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Philosopher":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Piazzolla":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Piedra":{"variants":["regular"],"category":"display"},"Pinyon Script":{"variants":["regular"],"category":"handwriting"},"Pirata One":{"variants":["regular"],"category":"display"},"Plaster":{"variants":["regular"],"category":"display"},"Play":{"variants":["regular","700"],"category":"sans-serif"},"Playball":{"variants":["regular"],"category":"display"},"Playfair Display":{"variants":["regular","500","600","700","800","900","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Playfair Display SC":{"variants":["regular","italic","700","700italic","900","900italic"],"category":"serif"},"Podkova":{"variants":["regular","500","600","700","800"],"category":"serif"},"Poiret One":{"variants":["regular"],"category":"display"},"Poller One":{"variants":["regular"],"category":"display"},"Poly":{"variants":["regular","italic"],"category":"serif"},"Pompiere":{"variants":["regular"],"category":"display"},"Pontano Sans":{"variants":["regular"],"category":"sans-serif"},"Poor Story":{"variants":["regular"],"category":"display"},"Poppins":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Port Lligat Sans":{"variants":["regular"],"category":"sans-serif"},"Port Lligat Slab":{"variants":["regular"],"category":"serif"},"Potta One":{"variants":["regular"],"category":"display"},"Pragati Narrow":{"variants":["regular","700"],"category":"sans-serif"},"Prata":{"variants":["regular"],"category":"serif"},"Preahvihear":{"variants":["regular"],"category":"sans-serif"},"Press Start 2P":{"variants":["regular"],"category":"display"},"Pridi":{"variants":["200","300","regular","500","600","700"],"category":"serif"},"Princess Sofia":{"variants":["regular"],"category":"handwriting"},"Prociono":{"variants":["regular"],"category":"serif"},"Prompt":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Prosto One":{"variants":["regular"],"category":"display"},"Proza Libre":{"variants":["regular","italic","500","500italic","600","600italic","700","700italic","800","800italic"],"category":"sans-serif"},"Public Sans":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Puritan":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Purple Purse":{"variants":["regular"],"category":"display"},"Qahiri":{"variants":["regular"],"category":"sans-serif"},"Quando":{"variants":["regular"],"category":"serif"},"Quantico":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Quattrocento":{"variants":["regular","700"],"category":"serif"},"Quattrocento Sans":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Questrial":{"variants":["regular"],"category":"sans-serif"},"Quicksand":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Quintessential":{"variants":["regular"],"category":"handwriting"},"Qwigley":{"variants":["regular"],"category":"handwriting"},"Racing Sans One":{"variants":["regular"],"category":"display"},"Radley":{"variants":["regular","italic"],"category":"serif"},"Rajdhani":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Rakkas":{"variants":["regular"],"category":"display"},"Raleway":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Raleway Dots":{"variants":["regular"],"category":"display"},"Ramabhadra":{"variants":["regular"],"category":"sans-serif"},"Ramaraja":{"variants":["regular"],"category":"serif"},"Rambla":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Rammetto One":{"variants":["regular"],"category":"display"},"Rampart One":{"variants":["regular"],"category":"display"},"Ranchers":{"variants":["regular"],"category":"display"},"Rancho":{"variants":["regular"],"category":"handwriting"},"Ranga":{"variants":["regular","700"],"category":"display"},"Rasa":{"variants":["300","regular","500","600","700","300italic","italic","500italic","600italic","700italic"],"category":"serif"},"Rationale":{"variants":["regular"],"category":"sans-serif"},"Ravi Prakash":{"variants":["regular"],"category":"display"},"Recursive":{"variants":["300","regular","500","600","700","800","900"],"category":"sans-serif"},"Red Hat Display":{"variants":["300","regular","500","600","700","800","900","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Red Hat Text":{"variants":["300","regular","500","600","700","300italic","italic","500italic","600italic","700italic"],"category":"sans-serif"},"Red Rose":{"variants":["300","regular","500","600","700"],"category":"display"},"Redressed":{"variants":["regular"],"category":"handwriting"},"Reem Kufi":{"variants":["regular","500","600","700"],"category":"sans-serif"},"Reenie Beanie":{"variants":["regular"],"category":"handwriting"},"Reggae One":{"variants":["regular"],"category":"display"},"Revalia":{"variants":["regular"],"category":"display"},"Rhodium Libre":{"variants":["regular"],"category":"serif"},"Ribeye":{"variants":["regular"],"category":"display"},"Ribeye Marrow":{"variants":["regular"],"category":"display"},"Righteous":{"variants":["regular"],"category":"display"},"Risque":{"variants":["regular"],"category":"display"},"Roboto":{"variants":["100","100italic","300","300italic","regular","italic","500","500italic","700","700italic","900","900italic"],"category":"sans-serif"},"Roboto Condensed":{"variants":["300","300italic","regular","italic","700","700italic"],"category":"sans-serif"},"Roboto Mono":{"variants":["100","200","300","regular","500","600","700","100italic","200italic","300italic","italic","500italic","600italic","700italic"],"category":"monospace"},"Roboto Slab":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Rochester":{"variants":["regular"],"category":"handwriting"},"Rock Salt":{"variants":["regular"],"category":"handwriting"},"RocknRoll One":{"variants":["regular"],"category":"sans-serif"},"Rokkitt":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"serif"},"Romanesco":{"variants":["regular"],"category":"handwriting"},"Ropa Sans":{"variants":["regular","italic"],"category":"sans-serif"},"Rosario":{"variants":["300","regular","500","600","700","300italic","italic","500italic","600italic","700italic"],"category":"sans-serif"},"Rosarivo":{"variants":["regular","italic"],"category":"serif"},"Rouge Script":{"variants":["regular"],"category":"handwriting"},"Rowdies":{"variants":["300","regular","700"],"category":"display"},"Rozha One":{"variants":["regular"],"category":"serif"},"Rubik":{"variants":["300","regular","500","600","700","800","900","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Rubik Beastly":{"variants":["regular"],"category":"display"},"Rubik Mono One":{"variants":["regular"],"category":"sans-serif"},"Ruda":{"variants":["regular","500","600","700","800","900"],"category":"sans-serif"},"Rufina":{"variants":["regular","700"],"category":"serif"},"Ruge Boogie":{"variants":["regular"],"category":"handwriting"},"Ruluko":{"variants":["regular"],"category":"sans-serif"},"Rum Raisin":{"variants":["regular"],"category":"sans-serif"},"Ruslan Display":{"variants":["regular"],"category":"display"},"Russo One":{"variants":["regular"],"category":"sans-serif"},"Ruthie":{"variants":["regular"],"category":"handwriting"},"Rye":{"variants":["regular"],"category":"display"},"STIX Two Text":{"variants":["regular","500","600","700","italic","500italic","600italic","700italic"],"category":"serif"},"Sacramento":{"variants":["regular"],"category":"handwriting"},"Sahitya":{"variants":["regular","700"],"category":"serif"},"Sail":{"variants":["regular"],"category":"display"},"Saira":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Saira Condensed":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Saira Extra Condensed":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Saira Semi Condensed":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Saira Stencil One":{"variants":["regular"],"category":"display"},"Salsa":{"variants":["regular"],"category":"display"},"Sanchez":{"variants":["regular","italic"],"category":"serif"},"Sancreek":{"variants":["regular"],"category":"display"},"Sansita":{"variants":["regular","italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Sansita Swashed":{"variants":["300","regular","500","600","700","800","900"],"category":"display"},"Sarabun":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic"],"category":"sans-serif"},"Sarala":{"variants":["regular","700"],"category":"sans-serif"},"Sarina":{"variants":["regular"],"category":"display"},"Sarpanch":{"variants":["regular","500","600","700","800","900"],"category":"sans-serif"},"Satisfy":{"variants":["regular"],"category":"handwriting"},"Sawarabi Gothic":{"variants":["regular"],"category":"sans-serif"},"Sawarabi Mincho":{"variants":["regular"],"category":"sans-serif"},"Scada":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"Scheherazade":{"variants":["regular","700"],"category":"serif"},"Scheherazade New":{"variants":["regular","700"],"category":"serif"},"Schoolbell":{"variants":["regular"],"category":"handwriting"},"Scope One":{"variants":["regular"],"category":"serif"},"Seaweed Script":{"variants":["regular"],"category":"display"},"Secular One":{"variants":["regular"],"category":"sans-serif"},"Sedgwick Ave":{"variants":["regular"],"category":"handwriting"},"Sedgwick Ave Display":{"variants":["regular"],"category":"handwriting"},"Sen":{"variants":["regular","700","800"],"category":"sans-serif"},"Sevillana":{"variants":["regular"],"category":"display"},"Seymour One":{"variants":["regular"],"category":"sans-serif"},"Shadows Into Light":{"variants":["regular"],"category":"handwriting"},"Shadows Into Light Two":{"variants":["regular"],"category":"handwriting"},"Shanti":{"variants":["regular"],"category":"sans-serif"},"Share":{"variants":["regular","italic","700","700italic"],"category":"display"},"Share Tech":{"variants":["regular"],"category":"sans-serif"},"Share Tech Mono":{"variants":["regular"],"category":"monospace"},"Shippori Mincho":{"variants":["regular","500","600","700","800"],"category":"serif"},"Shippori Mincho B1":{"variants":["regular","500","600","700","800"],"category":"serif"},"Shojumaru":{"variants":["regular"],"category":"display"},"Short Stack":{"variants":["regular"],"category":"handwriting"},"Shrikhand":{"variants":["regular"],"category":"display"},"Siemreap":{"variants":["regular"],"category":"display"},"Sigmar One":{"variants":["regular"],"category":"display"},"Signika":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Signika Negative":{"variants":["300","regular","600","700"],"category":"sans-serif"},"Simonetta":{"variants":["regular","italic","900","900italic"],"category":"display"},"Single Day":{"variants":["regular"],"category":"display"},"Sintony":{"variants":["regular","700"],"category":"sans-serif"},"Sirin Stencil":{"variants":["regular"],"category":"display"},"Six Caps":{"variants":["regular"],"category":"sans-serif"},"Skranji":{"variants":["regular","700"],"category":"display"},"Slabo 13px":{"variants":["regular"],"category":"serif"},"Slabo 27px":{"variants":["regular"],"category":"serif"},"Slackey":{"variants":["regular"],"category":"display"},"Smokum":{"variants":["regular"],"category":"display"},"Smythe":{"variants":["regular"],"category":"display"},"Sniglet":{"variants":["regular","800"],"category":"display"},"Snippet":{"variants":["regular"],"category":"sans-serif"},"Snowburst One":{"variants":["regular"],"category":"display"},"Sofadi One":{"variants":["regular"],"category":"display"},"Sofia":{"variants":["regular"],"category":"handwriting"},"Solway":{"variants":["300","regular","500","700","800"],"category":"serif"},"Song Myung":{"variants":["regular"],"category":"serif"},"Sonsie One":{"variants":["regular"],"category":"display"},"Sora":{"variants":["100","200","300","regular","500","600","700","800"],"category":"sans-serif"},"Sorts Mill Goudy":{"variants":["regular","italic"],"category":"serif"},"Source Code Pro":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","900","900italic"],"category":"monospace"},"Source Sans Pro":{"variants":["200","200italic","300","300italic","regular","italic","600","600italic","700","700italic","900","900italic"],"category":"sans-serif"},"Source Serif Pro":{"variants":["200","200italic","300","300italic","regular","italic","600","600italic","700","700italic","900","900italic"],"category":"serif"},"Space Grotesk":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Space Mono":{"variants":["regular","italic","700","700italic"],"category":"monospace"},"Spartan":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Special Elite":{"variants":["regular"],"category":"display"},"Spectral":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic"],"category":"serif"},"Spectral SC":{"variants":["200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic"],"category":"serif"},"Spicy Rice":{"variants":["regular"],"category":"display"},"Spinnaker":{"variants":["regular"],"category":"sans-serif"},"Spirax":{"variants":["regular"],"category":"display"},"Squada One":{"variants":["regular"],"category":"display"},"Sree Krushnadevaraya":{"variants":["regular"],"category":"serif"},"Sriracha":{"variants":["regular"],"category":"handwriting"},"Srisakdi":{"variants":["regular","700"],"category":"display"},"Staatliches":{"variants":["regular"],"category":"display"},"Stalemate":{"variants":["regular"],"category":"handwriting"},"Stalinist One":{"variants":["regular"],"category":"display"},"Stardos Stencil":{"variants":["regular","700"],"category":"display"},"Stick":{"variants":["regular"],"category":"sans-serif"},"Stick No Bills":{"variants":["200","300","regular","500","600","700","800"],"category":"sans-serif"},"Stint Ultra Condensed":{"variants":["regular"],"category":"display"},"Stint Ultra Expanded":{"variants":["regular"],"category":"display"},"Stoke":{"variants":["300","regular"],"category":"serif"},"Strait":{"variants":["regular"],"category":"sans-serif"},"Style Script":{"variants":["regular"],"category":"handwriting"},"Stylish":{"variants":["regular"],"category":"sans-serif"},"Sue Ellen Francisco":{"variants":["regular"],"category":"handwriting"},"Suez One":{"variants":["regular"],"category":"serif"},"Sulphur Point":{"variants":["300","regular","700"],"category":"sans-serif"},"Sumana":{"variants":["regular","700"],"category":"serif"},"Sunflower":{"variants":["300","500","700"],"category":"sans-serif"},"Sunshiney":{"variants":["regular"],"category":"handwriting"},"Supermercado One":{"variants":["regular"],"category":"display"},"Sura":{"variants":["regular","700"],"category":"serif"},"Suranna":{"variants":["regular"],"category":"serif"},"Suravaram":{"variants":["regular"],"category":"serif"},"Suwannaphum":{"variants":["100","300","regular","700","900"],"category":"serif"},"Swanky and Moo Moo":{"variants":["regular"],"category":"handwriting"},"Syncopate":{"variants":["regular","700"],"category":"sans-serif"},"Syne":{"variants":["regular","500","600","700","800"],"category":"sans-serif"},"Syne Mono":{"variants":["regular"],"category":"monospace"},"Syne Tactile":{"variants":["regular"],"category":"display"},"Tajawal":{"variants":["200","300","regular","500","700","800","900"],"category":"sans-serif"},"Tangerine":{"variants":["regular","700"],"category":"handwriting"},"Taprom":{"variants":["regular"],"category":"display"},"Tauri":{"variants":["regular"],"category":"sans-serif"},"Taviraj":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"serif"},"Teko":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Telex":{"variants":["regular"],"category":"sans-serif"},"Tenali Ramakrishna":{"variants":["regular"],"category":"sans-serif"},"Tenor Sans":{"variants":["regular"],"category":"sans-serif"},"Text Me One":{"variants":["regular"],"category":"sans-serif"},"Texturina":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Thasadith":{"variants":["regular","italic","700","700italic"],"category":"sans-serif"},"The Girl Next Door":{"variants":["regular"],"category":"handwriting"},"Tienne":{"variants":["regular","700","900"],"category":"serif"},"Tillana":{"variants":["regular","500","600","700","800"],"category":"handwriting"},"Timmana":{"variants":["regular"],"category":"sans-serif"},"Tinos":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Titan One":{"variants":["regular"],"category":"display"},"Titillium Web":{"variants":["200","200italic","300","300italic","regular","italic","600","600italic","700","700italic","900"],"category":"sans-serif"},"Tomorrow":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"sans-serif"},"Tourney":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"display"},"Trade Winds":{"variants":["regular"],"category":"display"},"Train One":{"variants":["regular"],"category":"display"},"Trirong":{"variants":["100","100italic","200","200italic","300","300italic","regular","italic","500","500italic","600","600italic","700","700italic","800","800italic","900","900italic"],"category":"serif"},"Trispace":{"variants":["100","200","300","regular","500","600","700","800"],"category":"sans-serif"},"Trocchi":{"variants":["regular"],"category":"serif"},"Trochut":{"variants":["regular","italic","700"],"category":"display"},"Truculenta":{"variants":["100","200","300","regular","500","600","700","800","900"],"category":"sans-serif"},"Trykker":{"variants":["regular"],"category":"serif"},"Tulpen One":{"variants":["regular"],"category":"display"},"Turret Road":{"variants":["200","300","regular","500","700","800"],"category":"display"},"Ubuntu":{"variants":["300","300italic","regular","italic","500","500italic","700","700italic"],"category":"sans-serif"},"Ubuntu Condensed":{"variants":["regular"],"category":"sans-serif"},"Ubuntu Mono":{"variants":["regular","italic","700","700italic"],"category":"monospace"},"Uchen":{"variants":["regular"],"category":"serif"},"Ultra":{"variants":["regular"],"category":"serif"},"Uncial Antiqua":{"variants":["regular"],"category":"display"},"Underdog":{"variants":["regular"],"category":"display"},"Unica One":{"variants":["regular"],"category":"display"},"UnifrakturCook":{"variants":["700"],"category":"display"},"UnifrakturMaguntia":{"variants":["regular"],"category":"display"},"Unkempt":{"variants":["regular","700"],"category":"display"},"Unlock":{"variants":["regular"],"category":"display"},"Unna":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Urbanist":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"VT323":{"variants":["regular"],"category":"monospace"},"Vampiro One":{"variants":["regular"],"category":"display"},"Varela":{"variants":["regular"],"category":"sans-serif"},"Varela Round":{"variants":["regular"],"category":"sans-serif"},"Varta":{"variants":["300","regular","500","600","700"],"category":"sans-serif"},"Vast Shadow":{"variants":["regular"],"category":"display"},"Vesper Libre":{"variants":["regular","500","700","900"],"category":"serif"},"Viaoda Libre":{"variants":["regular"],"category":"display"},"Vibes":{"variants":["regular"],"category":"display"},"Vibur":{"variants":["regular"],"category":"handwriting"},"Vidaloka":{"variants":["regular"],"category":"serif"},"Viga":{"variants":["regular"],"category":"sans-serif"},"Voces":{"variants":["regular"],"category":"display"},"Volkhov":{"variants":["regular","italic","700","700italic"],"category":"serif"},"Vollkorn":{"variants":["regular","500","600","700","800","900","italic","500italic","600italic","700italic","800italic","900italic"],"category":"serif"},"Vollkorn SC":{"variants":["regular","600","700","900"],"category":"serif"},"Voltaire":{"variants":["regular"],"category":"sans-serif"},"Waiting for the Sunrise":{"variants":["regular"],"category":"handwriting"},"Wallpoet":{"variants":["regular"],"category":"display"},"Walter Turncoat":{"variants":["regular"],"category":"handwriting"},"Warnes":{"variants":["regular"],"category":"display"},"Wellfleet":{"variants":["regular"],"category":"display"},"Wendy One":{"variants":["regular"],"category":"sans-serif"},"WindSong":{"variants":["regular","500"],"category":"handwriting"},"Wire One":{"variants":["regular"],"category":"sans-serif"},"Work Sans":{"variants":["100","200","300","regular","500","600","700","800","900","100italic","200italic","300italic","italic","500italic","600italic","700italic","800italic","900italic"],"category":"sans-serif"},"Xanh Mono":{"variants":["regular","italic"],"category":"monospace"},"Yaldevi":{"variants":["200","300","regular","500","600","700"],"category":"sans-serif"},"Yanone Kaffeesatz":{"variants":["200","300","regular","500","600","700"],"category":"sans-serif"},"Yantramanav":{"variants":["100","300","regular","500","700","900"],"category":"sans-serif"},"Yatra One":{"variants":["regular"],"category":"display"},"Yellowtail":{"variants":["regular"],"category":"handwriting"},"Yeon Sung":{"variants":["regular"],"category":"display"},"Yeseva One":{"variants":["regular"],"category":"display"},"Yesteryear":{"variants":["regular"],"category":"handwriting"},"Yomogi":{"variants":["regular"],"category":"handwriting"},"Yrsa":{"variants":["300","regular","500","600","700","300italic","italic","500italic","600italic","700italic"],"category":"serif"},"Yusei Magic":{"variants":["regular"],"category":"sans-serif"},"ZCOOL KuaiLe":{"variants":["regular"],"category":"display"},"ZCOOL QingKe HuangYou":{"variants":["regular"],"category":"display"},"ZCOOL XiaoWei":{"variants":["regular"],"category":"serif"},"Zen Dots":{"variants":["regular"],"category":"display"},"Zen Loop":{"variants":["regular","italic"],"category":"display"},"Zen Tokyo Zoo":{"variants":["regular"],"category":"display"},"Zeyada":{"variants":["regular"],"category":"handwriting"},"Zhi Mang Xing":{"variants":["regular"],"category":"handwriting"},"Zilla Slab":{"variants":["300","300italic","regular","italic","500","500italic","600","600italic","700","700italic"],"category":"serif"},"Zilla Slab Highlight":{"variants":["regular","700"],"category":"display"}}');function Fe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fe(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ve(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(t.includes(a))continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)r=i[a],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var Be=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function He(){return He=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},He.apply(null,arguments)}function ze(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,i(a.key),a)}}function Ue(e,t){return Ue=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ue(e,t)}function Ge(e){return Ge=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ge(e)}function Ke(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Ke=function(){return!!e})()}var $e=function(){function e(e){var t=this;this._insertTag=function(e){var r;r=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,r),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var r=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{r.insertRule(e,r.cssRules.length)}catch(e){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),We=Math.abs,qe=String.fromCharCode,Ye=Object.assign;function Je(e){return e.trim()}function Xe(e,t,r){return e.replace(t,r)}function Ze(e,t){return e.indexOf(t)}function Qe(e,t){return 0|e.charCodeAt(t)}function et(e,t,r){return e.slice(t,r)}function tt(e){return e.length}function rt(e){return e.length}function at(e,t){return t.push(e),e}var nt=1,it=1,ot=0,st=0,lt=0,ct="";function ut(e,t,r,a,n,i,o){return{value:e,root:t,parent:r,type:a,props:n,children:i,line:nt,column:it,length:o,return:""}}function gt(e,t){return Ye(ut("",null,null,"",null,null,0),e,{length:-e.length},t)}function dt(){return lt=st>0?Qe(ct,--st):0,it--,10===lt&&(it=1,nt--),lt}function ft(){return lt=st<ot?Qe(ct,st++):0,it++,10===lt&&(it=1,nt++),lt}function pt(){return Qe(ct,st)}function vt(){return st}function ht(e,t){return et(ct,e,t)}function yt(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function mt(e){return nt=it=1,ot=tt(ct=e),st=0,[]}function bt(e){return ct="",e}function St(e){return Je(ht(st-1,Ct(91===e?e+2:40===e?e+1:e)))}function wt(e){for(;(lt=pt())&&lt<33;)ft();return yt(e)>2||yt(lt)>3?"":" "}function Ot(e,t){for(;--t&&ft()&&!(lt<48||lt>102||lt>57&&lt<65||lt>70&&lt<97););return ht(e,vt()+(t<6&&32==pt()&&32==ft()))}function Ct(e){for(;ft();)switch(lt){case e:return st;case 34:case 39:34!==e&&39!==e&&Ct(lt);break;case 40:41===e&&Ct(e);break;case 92:ft()}return st}function Et(e,t){for(;ft()&&e+lt!==57&&(e+lt!==84||47!==pt()););return"/*"+ht(t,st-1)+"*"+qe(47===e?e:ft())}function Dt(e){for(;!yt(pt());)ft();return ht(e,st)}var Nt="-ms-",xt="-moz-",Tt="-webkit-",It="comm",Mt="rule",kt="decl",Pt="@keyframes";function _t(e,t){for(var r="",a=rt(e),n=0;n<a;n++)r+=t(e[n],n,e,t)||"";return r}function Rt(e,t,r,a){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case kt:return e.return=e.return||e.value;case It:return"";case Pt:return e.return=e.value+"{"+_t(e.children,a)+"}";case Mt:e.value=e.props.join(",")}return tt(r=_t(e.children,a))?e.return=e.value+"{"+r+"}":""}function Lt(e){return bt(At("",null,null,null,[""],e=mt(e),0,[0],e))}function At(e,t,r,a,n,i,o,s,l){for(var c=0,u=0,g=o,d=0,f=0,p=0,v=1,h=1,y=1,m=0,b="",S=n,w=i,O=a,C=b;h;)switch(p=m,m=ft()){case 40:if(108!=p&&58==Qe(C,g-1)){-1!=Ze(C+=Xe(St(m),"&","&\f"),"&\f")&&(y=-1);break}case 34:case 39:case 91:C+=St(m);break;case 9:case 10:case 13:case 32:C+=wt(p);break;case 92:C+=Ot(vt()-1,7);continue;case 47:switch(pt()){case 42:case 47:at(jt(Et(ft(),vt()),t,r),l);break;default:C+="/"}break;case 123*v:s[c++]=tt(C)*y;case 125*v:case 59:case 0:switch(m){case 0:case 125:h=0;case 59+u:-1==y&&(C=Xe(C,/\f/g,"")),f>0&&tt(C)-g&&at(f>32?Vt(C+";",a,r,g-1):Vt(Xe(C," ","")+";",a,r,g-2),l);break;case 59:C+=";";default:if(at(O=Ft(C,t,r,c,u,n,s,b,S=[],w=[],g),i),123===m)if(0===u)At(C,t,O,O,S,i,g,s,w);else switch(99===d&&110===Qe(C,3)?100:d){case 100:case 108:case 109:case 115:At(e,O,O,a&&at(Ft(e,O,O,0,0,n,s,b,n,S=[],g),w),n,w,g,s,a?S:w);break;default:At(C,O,O,O,[""],w,0,s,w)}}c=u=f=0,v=y=1,b=C="",g=o;break;case 58:g=1+tt(C),f=p;default:if(v<1)if(123==m)--v;else if(125==m&&0==v++&&125==dt())continue;switch(C+=qe(m),m*v){case 38:y=u>0?1:(C+="\f",-1);break;case 44:s[c++]=(tt(C)-1)*y,y=1;break;case 64:45===pt()&&(C+=St(ft())),d=pt(),u=g=tt(b=C+=Dt(vt())),m++;break;case 45:45===p&&2==tt(C)&&(v=0)}}return i}function Ft(e,t,r,a,n,i,o,s,l,c,u){for(var g=n-1,d=0===n?i:[""],f=rt(d),p=0,v=0,h=0;p<a;++p)for(var y=0,m=et(e,g+1,g=We(v=o[p])),b=e;y<f;++y)(b=Je(v>0?d[y]+" "+m:Xe(m,/&\f/g,d[y])))&&(l[h++]=b);return ut(e,t,r,0===n?Mt:s,l,c,u)}function jt(e,t,r){return ut(e,t,r,It,qe(lt),et(e,2,-2),0)}function Vt(e,t,r,a){return ut(e,t,r,kt,et(e,0,a),et(e,a+1,-1),a)}var Bt=function(e,t,r){for(var a=0,n=0;a=n,n=pt(),38===a&&12===n&&(t[r]=1),!yt(n);)ft();return ht(e,st)},Ht=new WeakMap,zt=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,r=e.parent,a=e.column===r.column&&e.line===r.line;"rule"!==r.type;)if(!(r=r.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||Ht.get(r))&&!a){Ht.set(e,!0);for(var n=[],i=function(e,t){return bt(function(e,t){var r=-1,a=44;do{switch(yt(a)){case 0:38===a&&12===pt()&&(t[r]=1),e[r]+=Bt(st-1,t,r);break;case 2:e[r]+=St(a);break;case 4:if(44===a){e[++r]=58===pt()?"&\f":"",t[r]=e[r].length;break}default:e[r]+=qe(a)}}while(a=ft());return e}(mt(e),t))}(t,n),o=r.props,s=0,l=0;s<i.length;s++)for(var c=0;c<o.length;c++,l++)e.props[l]=n[s]?i[s].replace(/&\f/g,o[c]):o[c]+" "+i[s]}}},Ut=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function Gt(e,t){switch(function(e,t){return 45^Qe(e,0)?(((t<<2^Qe(e,0))<<2^Qe(e,1))<<2^Qe(e,2))<<2^Qe(e,3):0}(e,t)){case 5103:return Tt+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Tt+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Tt+e+xt+e+Nt+e+e;case 6828:case 4268:return Tt+e+Nt+e+e;case 6165:return Tt+e+Nt+"flex-"+e+e;case 5187:return Tt+e+Xe(e,/(\w+).+(:[^]+)/,Tt+"box-$1$2"+Nt+"flex-$1$2")+e;case 5443:return Tt+e+Nt+"flex-item-"+Xe(e,/flex-|-self/,"")+e;case 4675:return Tt+e+Nt+"flex-line-pack"+Xe(e,/align-content|flex-|-self/,"")+e;case 5548:return Tt+e+Nt+Xe(e,"shrink","negative")+e;case 5292:return Tt+e+Nt+Xe(e,"basis","preferred-size")+e;case 6060:return Tt+"box-"+Xe(e,"-grow","")+Tt+e+Nt+Xe(e,"grow","positive")+e;case 4554:return Tt+Xe(e,/([^-])(transform)/g,"$1"+Tt+"$2")+e;case 6187:return Xe(Xe(Xe(e,/(zoom-|grab)/,Tt+"$1"),/(image-set)/,Tt+"$1"),e,"")+e;case 5495:case 3959:return Xe(e,/(image-set\([^]*)/,Tt+"$1$`$1");case 4968:return Xe(Xe(e,/(.+:)(flex-)?(.*)/,Tt+"box-pack:$3"+Nt+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Tt+e+e;case 4095:case 3583:case 4068:case 2532:return Xe(e,/(.+)-inline(.+)/,Tt+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(tt(e)-1-t>6)switch(Qe(e,t+1)){case 109:if(45!==Qe(e,t+4))break;case 102:return Xe(e,/(.+:)(.+)-([^]+)/,"$1"+Tt+"$2-$3$1"+xt+(108==Qe(e,t+3)?"$3":"$2-$3"))+e;case 115:return~Ze(e,"stretch")?Gt(Xe(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==Qe(e,t+1))break;case 6444:switch(Qe(e,tt(e)-3-(~Ze(e,"!important")&&10))){case 107:return Xe(e,":",":"+Tt)+e;case 101:return Xe(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Tt+(45===Qe(e,14)?"inline-":"")+"box$3$1"+Tt+"$2$3$1"+Nt+"$2box$3")+e}break;case 5936:switch(Qe(e,t+11)){case 114:return Tt+e+Nt+Xe(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Tt+e+Nt+Xe(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Tt+e+Nt+Xe(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return Tt+e+Nt+e+e}return e}var Kt=[function(e,t,r,a){if(e.length>-1&&!e.return)switch(e.type){case kt:e.return=Gt(e.value,e.length);break;case Pt:return _t([gt(e,{value:Xe(e.value,"@","@"+Tt)})],a);case Mt:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e){return(e=/(::plac\w+|:read-\w+)/.exec(e))?e[0]:e}(t)){case":read-only":case":read-write":return _t([gt(e,{props:[Xe(t,/:(read-\w+)/,":-moz-$1")]})],a);case"::placeholder":return _t([gt(e,{props:[Xe(t,/:(plac\w+)/,":"+Tt+"input-$1")]}),gt(e,{props:[Xe(t,/:(plac\w+)/,":-moz-$1")]}),gt(e,{props:[Xe(t,/:(plac\w+)/,Nt+"input-$1")]})],a)}return""}))}}],$t=function(e){var t=e.key;if("css"===t){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var a,n,i=e.stylisPlugins||Kt,o={},s=[];a=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),r=1;r<t.length;r++)o[t[r]]=!0;s.push(e)}));var l,c,u,g,d=[Rt,(g=function(e){l.insert(e)},function(e){e.root||(e=e.return)&&g(e)})],f=(c=[zt,Ut].concat(i,d),u=rt(c),function(e,t,r,a){for(var n="",i=0;i<u;i++)n+=c[i](e,t,r,a)||"";return n});n=function(e,t,r,a){l=r,_t(Lt(e?e+"{"+t.styles+"}":t.styles),f),a&&(p.inserted[t.name]=!0)};var p={key:t,sheet:new $e({key:t,container:a,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:o,registered:{},insert:n};return p.sheet.hydrate(s),p},Wt=function(e,t,r){var a=e.key+"-"+t.name;!1===r&&void 0===e.registered[a]&&(e.registered[a]=t.styles)},qt={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function Yt(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}var Jt=!1,Xt=/[A-Z]|^ms/g,Zt=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Qt=function(e){return 45===e.charCodeAt(1)},er=function(e){return null!=e&&"boolean"!=typeof e},tr=Yt((function(e){return Qt(e)?e:e.replace(Xt,"-$&").toLowerCase()})),rr=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(Zt,(function(e,t,r){return ir={name:t,styles:r,next:ir},t}))}return 1===qt[e]||Qt(e)||"number"!=typeof t||0===t?t:t+"px"},ar="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function nr(e,t,r){if(null==r)return"";var a=r;if(void 0!==a.__emotion_styles)return a;switch(typeof r){case"boolean":return"";case"object":var n=r;if(1===n.anim)return ir={name:n.name,styles:n.styles,next:ir},n.name;var i=r;if(void 0!==i.styles){var o=i.next;if(void 0!==o)for(;void 0!==o;)ir={name:o.name,styles:o.styles,next:ir},o=o.next;return i.styles+";"}return function(e,t,r){var a="";if(Array.isArray(r))for(var n=0;n<r.length;n++)a+=nr(e,t,r[n])+";";else for(var i in r){var o=r[i];if("object"!=typeof o){var s=o;null!=t&&void 0!==t[s]?a+=i+"{"+t[s]+"}":er(s)&&(a+=tr(i)+":"+rr(i,s)+";")}else{if("NO_COMPONENT_SELECTOR"===i&&Jt)throw new Error(ar);if(!Array.isArray(o)||"string"!=typeof o[0]||null!=t&&void 0!==t[o[0]]){var l=nr(e,t,o);switch(i){case"animation":case"animationName":a+=tr(i)+":"+l+";";break;default:a+=i+"{"+l+"}"}}else for(var c=0;c<o.length;c++)er(o[c])&&(a+=tr(i)+":"+rr(i,o[c])+";")}}return a}(e,t,r);case"function":if(void 0!==e){var s=ir,l=r(e);return ir=s,nr(e,t,l)}}var c=r;if(null==t)return c;var u=t[c];return void 0!==u?u:c}var ir,or=/label:\s*([^\s;\n{]+)\s*(;|$)/g;function sr(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var a=!0,n="";ir=void 0;var i=e[0];null==i||void 0===i.raw?(a=!1,n+=nr(r,t,i)):n+=i[0];for(var o=1;o<e.length;o++)n+=nr(r,t,e[o]),a&&(n+=i[o]);or.lastIndex=0;for(var s,l="";null!==(s=or.exec(n));)l+="-"+s[1];var c=function(e){for(var t,r=0,a=0,n=e.length;n>=4;++a,n-=4)t=***********(65535&(t=255&e.charCodeAt(a)|(255&e.charCodeAt(++a))<<8|(255&e.charCodeAt(++a))<<16|(255&e.charCodeAt(++a))<<24))+(59797*(t>>>16)<<16),r=***********(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^***********(65535&r)+(59797*(r>>>16)<<16);switch(n){case 3:r^=(255&e.charCodeAt(a+2))<<16;case 2:r^=(255&e.charCodeAt(a+1))<<8;case 1:r=***********(65535&(r^=255&e.charCodeAt(a)))+(59797*(r>>>16)<<16)}return(((r=***********(65535&(r^=r>>>13))+(59797*(r>>>16)<<16))^r>>>15)>>>0).toString(36)}(n)+l;return{name:c,styles:n,next:ir}}var lr=!!s.useInsertionEffect&&s.useInsertionEffect,cr=lr||function(e){return e()},ur=(lr||s.useLayoutEffect,s.createContext("undefined"!=typeof HTMLElement?$t({key:"css"}):null)),gr=(ur.Provider,function(e){return(0,s.forwardRef)((function(t,r){var a=(0,s.useContext)(ur);return e(t,a,r)}))}),dr=s.createContext({}),fr={}.hasOwnProperty,pr="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",vr=function(e){var t=e.cache,r=e.serialized,a=e.isStringTag;return Wt(t,r,a),cr((function(){return function(e,t,r){Wt(e,t,r);var a=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var n=t;do{e.insert(t===n?"."+a:"",n,e.sheet,!0),n=n.next}while(void 0!==n)}}(t,r,a)})),null},hr=gr((function(e,t,r){var a=e.css;"string"==typeof a&&void 0!==t.registered[a]&&(a=t.registered[a]);var n=e[pr],i=[a],o="";"string"==typeof e.className?o=function(e,t,r){var a="";return r.split(" ").forEach((function(r){void 0!==e[r]?t.push(e[r]+";"):a+=r+" "})),a}(t.registered,i,e.className):null!=e.className&&(o=e.className+" ");var l=sr(i,void 0,s.useContext(dr));o+=t.key+"-"+l.name;var c={};for(var u in e)fr.call(e,u)&&"css"!==u&&u!==pr&&(c[u]=e[u]);return c.className=o,r&&(c.ref=r),s.createElement(s.Fragment,null,s.createElement(vr,{cache:t,serialized:l,isStringTag:"string"==typeof n}),s.createElement(n,c))})),yr=hr,mr=(r(146),function(e,t){var r=arguments;if(null==t||!fr.call(t,"css"))return s.createElement.apply(void 0,r);var a=r.length,n=new Array(a);n[0]=yr,n[1]=function(e,t){var r={};for(var a in t)fr.call(t,a)&&(r[a]=t[a]);return r[pr]=e,r}(e,t);for(var i=2;i<a;i++)n[i]=r[i];return s.createElement.apply(null,n)});function br(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return sr(t)}var Sr=window.ReactDOM;const wr=Math.min,Or=Math.max,Cr=Math.round,Er=Math.floor,Dr=e=>({x:e,y:e});function Nr(e){return Ir(e)?(e.nodeName||"").toLowerCase():"#document"}function xr(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Tr(e){var t;return null==(t=(Ir(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Ir(e){return e instanceof Node||e instanceof xr(e).Node}function Mr(e){return e instanceof Element||e instanceof xr(e).Element}function kr(e){return e instanceof HTMLElement||e instanceof xr(e).HTMLElement}function Pr(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof xr(e).ShadowRoot)}function _r(e){const{overflow:t,overflowX:r,overflowY:a,display:n}=Rr(e);return/auto|scroll|overlay|hidden|clip/.test(t+a+r)&&!["inline","contents"].includes(n)}function Rr(e){return xr(e).getComputedStyle(e)}function Lr(e){const t=function(e){if("html"===Nr(e))return e;const t=e.assignedSlot||e.parentNode||Pr(e)&&e.host||Tr(e);return Pr(t)?t.host:t}(e);return function(e){return["html","body","#document"].includes(Nr(e))}(t)?e.ownerDocument?e.ownerDocument.body:e.body:kr(t)&&_r(t)?t:Lr(t)}function Ar(e,t,r){var a;void 0===t&&(t=[]),void 0===r&&(r=!0);const n=Lr(e),i=n===(null==(a=e.ownerDocument)?void 0:a.body),o=xr(n);if(i){const e=Fr(o);return t.concat(o,o.visualViewport||[],_r(n)?n:[],e&&r?Ar(e):[])}return t.concat(n,Ar(n,[],r))}function Fr(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function jr(e){return Mr(e)?e:e.contextElement}function Vr(e){const t=jr(e);if(!kr(t))return Dr(1);const r=t.getBoundingClientRect(),{width:a,height:n,$:i}=function(e){const t=Rr(e);let r=parseFloat(t.width)||0,a=parseFloat(t.height)||0;const n=kr(e),i=n?e.offsetWidth:r,o=n?e.offsetHeight:a,s=Cr(r)!==i||Cr(a)!==o;return s&&(r=i,a=o),{width:r,height:a,$:s}}(t);let o=(i?Cr(r.width):r.width)/a,s=(i?Cr(r.height):r.height)/n;return o&&Number.isFinite(o)||(o=1),s&&Number.isFinite(s)||(s=1),{x:o,y:s}}const Br=Dr(0);function Hr(e){const t=xr(e);return"undefined"!=typeof CSS&&CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Br}function zr(e,t,r,a){void 0===t&&(t=!1),void 0===r&&(r=!1);const n=e.getBoundingClientRect(),i=jr(e);let o=Dr(1);t&&(a?Mr(a)&&(o=Vr(a)):o=Vr(e));const s=function(e,t,r){return void 0===t&&(t=!1),!(!r||t&&r!==xr(e))&&t}(i,r,a)?Hr(i):Dr(0);let l=(n.left+s.x)/o.x,c=(n.top+s.y)/o.y,u=n.width/o.x,g=n.height/o.y;if(i){const e=xr(i),t=a&&Mr(a)?xr(a):a;let r=e,n=Fr(r);for(;n&&a&&t!==r;){const e=Vr(n),t=n.getBoundingClientRect(),a=Rr(n),i=t.left+(n.clientLeft+parseFloat(a.paddingLeft))*e.x,o=t.top+(n.clientTop+parseFloat(a.paddingTop))*e.y;l*=e.x,c*=e.y,u*=e.x,g*=e.y,l+=i,c+=o,r=xr(n),n=Fr(r)}}return function(e){const{x:t,y:r,width:a,height:n}=e;return{width:a,height:n,top:r,left:t,right:t+a,bottom:r+n,x:t,y:r}}({width:u,height:g,x:l,y:c})}var Ur=s.useLayoutEffect,Gr=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],Kr=function(){};function $r(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function Wr(e,t){for(var r=arguments.length,a=new Array(r>2?r-2:0),n=2;n<r;n++)a[n-2]=arguments[n];var i=[].concat(a);if(t&&e)for(var o in t)t.hasOwnProperty(o)&&t[o]&&i.push("".concat($r(e,o)));return i.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var qr=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===n(e)&&null!==e?[e]:[];var t},Yr=function(e){return e.className,e.clearValue,e.cx,e.getStyles,e.getClassNames,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme,je({},Ve(e,Gr))},Jr=function(e,t,r){var a=e.cx,n=e.getStyles,i=e.getClassNames,o=e.className;return{css:n(t,e),className:a(null!=r?r:{},i(t,e),o)}};function Xr(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function Zr(e){return Xr(e)?window.pageYOffset:e.scrollTop}function Qr(e,t){Xr(e)?window.scrollTo(0,t):e.scrollTop=t}function ea(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Kr,n=Zr(e),i=t-n,o=0;!function t(){var s=function(e,t,r,a){return r*((e=e/a-1)*e*e+1)+t}(o+=10,n,i,r);Qr(e,s),o<r?window.requestAnimationFrame(t):a(e)}()}function ta(e,t){var r=e.getBoundingClientRect(),a=t.getBoundingClientRect(),n=t.offsetHeight/3;a.bottom+n>r.bottom?Qr(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+n,e.scrollHeight)):a.top-n<r.top&&Qr(e,Math.max(t.offsetTop-n,0))}function ra(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}var aa=!1,na={get passive(){return aa=!0}},ia="undefined"!=typeof window?window:{};ia.addEventListener&&ia.removeEventListener&&(ia.addEventListener("p",Kr,na),ia.removeEventListener("p",Kr,!1));var oa=aa;function sa(e){return null!=e}function la(e,t,r){return e?t:r}var ca=function(e){return"auto"===e?"bottom":e},ua=(0,s.createContext)(null),ga=function(e){var t=e.children,r=e.minMenuHeight,a=e.maxMenuHeight,n=e.menuPlacement,i=e.menuPosition,o=e.menuShouldScrollIntoView,l=e.theme,c=((0,s.useContext)(ua)||{}).setPortalPlacement,u=(0,s.useRef)(null),g=y((0,s.useState)(a),2),d=g[0],f=g[1],p=y((0,s.useState)(null),2),v=p[0],h=p[1],m=l.spacing.controlHeight;return Ur((function(){var e=u.current;if(e){var t="fixed"===i,s=function(e){var t=e.maxHeight,r=e.menuEl,a=e.minHeight,n=e.placement,i=e.shouldScroll,o=e.isFixedPosition,s=e.controlHeight,l=function(e){var t=getComputedStyle(e),r="absolute"===t.position,a=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var n=e;n=n.parentElement;)if(t=getComputedStyle(n),(!r||"static"!==t.position)&&a.test(t.overflow+t.overflowY+t.overflowX))return n;return document.documentElement}(r),c={placement:"bottom",maxHeight:t};if(!r||!r.offsetParent)return c;var u,g=l.getBoundingClientRect().height,d=r.getBoundingClientRect(),f=d.bottom,p=d.height,v=d.top,h=r.offsetParent.getBoundingClientRect().top,y=o||Xr(u=l)?window.innerHeight:u.clientHeight,m=Zr(l),b=parseInt(getComputedStyle(r).marginBottom,10),S=parseInt(getComputedStyle(r).marginTop,10),w=h-S,O=y-v,C=w+m,E=g-m-v,D=f-y+m+b,N=m+v-S,x=160;switch(n){case"auto":case"bottom":if(O>=p)return{placement:"bottom",maxHeight:t};if(E>=p&&!o)return i&&ea(l,D,x),{placement:"bottom",maxHeight:t};if(!o&&E>=a||o&&O>=a)return i&&ea(l,D,x),{placement:"bottom",maxHeight:o?O-b:E-b};if("auto"===n||o){var T=t,I=o?w:C;return I>=a&&(T=Math.min(I-b-s,t)),{placement:"top",maxHeight:T}}if("bottom"===n)return i&&Qr(l,D),{placement:"bottom",maxHeight:t};break;case"top":if(w>=p)return{placement:"top",maxHeight:t};if(C>=p&&!o)return i&&ea(l,N,x),{placement:"top",maxHeight:t};if(!o&&C>=a||o&&w>=a){var M=t;return(!o&&C>=a||o&&w>=a)&&(M=o?w-S:C-S),i&&ea(l,N,x),{placement:"top",maxHeight:M}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(n,'".'))}return c}({maxHeight:a,menuEl:e,minHeight:r,placement:n,shouldScroll:o&&!t,isFixedPosition:t,controlHeight:m});f(s.maxHeight),h(s.placement),null==c||c(s.placement)}}),[a,n,i,o,r,c,m]),t({ref:u,placerProps:je(je({},e),{},{placement:v||ca(n),maxHeight:d})})},da=function(e,t){var r=e.theme,a=r.spacing.baseUnit,n=r.colors;return je({textAlign:"center"},t?{}:{color:n.neutral40,padding:"".concat(2*a,"px ").concat(3*a,"px")})},fa=da,pa=da,va=function(e){var t=e.children,r=e.innerProps;return mr("div",He({},Jr(e,"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),r),t)};va.defaultProps={children:"No options"};var ha=function(e){var t=e.children,r=e.innerProps;return mr("div",He({},Jr(e,"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),r),t)};ha.defaultProps={children:"Loading..."};var ya,ma=["size"],ba={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},Sa=function(e){var t=e.size,r=Ve(e,ma);return mr("svg",He({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:ba},r))},wa=function(e){return mr(Sa,He({size:20},e),mr("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},Oa=function(e){return mr(Sa,He({size:20},e),mr("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},Ca=function(e,t){var r=e.isFocused,a=e.theme,n=a.spacing.baseUnit,i=a.colors;return je({label:"indicatorContainer",display:"flex",transition:"color 150ms"},t?{}:{color:r?i.neutral60:i.neutral20,padding:2*n,":hover":{color:r?i.neutral80:i.neutral40}})},Ea=Ca,Da=Ca,Na=function(){var e=br.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}(ya||(ya=function(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"]))),xa=function(e){var t=e.delay,r=e.offset;return mr("span",{css:br({animation:"".concat(Na," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:r?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},Ta=function(e){var t=e.innerProps,r=e.isRtl;return mr("div",He({},Jr(e,"loadingIndicator",{indicator:!0,"loading-indicator":!0}),t),mr(xa,{delay:0,offset:r}),mr(xa,{delay:160,offset:!0}),mr(xa,{delay:320,offset:!r}))};Ta.defaultProps={size:4};var Ia=["data"],Ma=["innerRef","isDisabled","isHidden","inputClassName"],ka={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Pa={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":je({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},ka)},_a=function(e){return je({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},ka)},Ra=function(e){var t=e.children,r=e.innerProps;return mr("div",r,t)},La={ClearIndicator:function(e){var t=e.children,r=e.innerProps;return mr("div",He({},Jr(e,"clearIndicator",{indicator:!0,"clear-indicator":!0}),r),t||mr(wa,null))},Control:function(e){var t=e.children,r=e.isDisabled,a=e.isFocused,n=e.innerRef,i=e.innerProps,o=e.menuIsOpen;return mr("div",He({ref:n},Jr(e,"control",{control:!0,"control--is-disabled":r,"control--is-focused":a,"control--menu-is-open":o}),i),t)},DropdownIndicator:function(e){var t=e.children,r=e.innerProps;return mr("div",He({},Jr(e,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),r),t||mr(Oa,null))},DownChevron:Oa,CrossIcon:wa,Group:function(e){var t=e.children,r=e.cx,a=e.getStyles,n=e.getClassNames,i=e.Heading,o=e.headingProps,s=e.innerProps,l=e.label,c=e.theme,u=e.selectProps;return mr("div",He({},Jr(e,"group",{group:!0}),s),mr(i,He({},o,{selectProps:u,theme:c,getStyles:a,getClassNames:n,cx:r}),l),mr("div",null,t))},GroupHeading:function(e){var t=Yr(e);t.data;var r=Ve(t,Ia);return mr("div",He({},Jr(e,"groupHeading",{"group-heading":!0}),r))},IndicatorsContainer:function(e){var t=e.children,r=e.innerProps;return mr("div",He({},Jr(e,"indicatorsContainer",{indicators:!0}),r),t)},IndicatorSeparator:function(e){var t=e.innerProps;return mr("span",He({},t,Jr(e,"indicatorSeparator",{"indicator-separator":!0})))},Input:function(e){var t=e.cx,r=e.value,a=Yr(e),n=a.innerRef,i=a.isDisabled,o=a.isHidden,s=a.inputClassName,l=Ve(a,Ma);return mr("div",He({},Jr(e,"input",{"input-container":!0}),{"data-value":r||""}),mr("input",He({className:t({input:!0},s),ref:n,style:_a(o),disabled:i},l)))},LoadingIndicator:Ta,Menu:function(e){var t=e.children,r=e.innerRef,a=e.innerProps;return mr("div",He({},Jr(e,"menu",{menu:!0}),{ref:r},a),t)},MenuList:function(e){var t=e.children,r=e.innerProps,a=e.innerRef,n=e.isMulti;return mr("div",He({},Jr(e,"menuList",{"menu-list":!0,"menu-list--is-multi":n}),{ref:a},r),t)},MenuPortal:function(e){var t=e.appendTo,r=e.children,a=e.controlElement,n=e.innerProps,i=e.menuPlacement,o=e.menuPosition,l=(0,s.useRef)(null),c=(0,s.useRef)(null),u=y((0,s.useState)(ca(i)),2),g=u[0],d=u[1],f=(0,s.useMemo)((function(){return{setPortalPlacement:d}}),[]),p=y((0,s.useState)(null),2),v=p[0],h=p[1],m=(0,s.useCallback)((function(){if(a){var e=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(a),t="fixed"===o?0:window.pageYOffset,r=e[g]+t;r===(null==v?void 0:v.offset)&&e.left===(null==v?void 0:v.rect.left)&&e.width===(null==v?void 0:v.rect.width)||h({offset:r,rect:e})}}),[a,o,g,null==v?void 0:v.offset,null==v?void 0:v.rect.left,null==v?void 0:v.rect.width]);Ur((function(){m()}),[m]);var b=(0,s.useCallback)((function(){"function"==typeof c.current&&(c.current(),c.current=null),a&&l.current&&(c.current=function(e,t,r,a){void 0===a&&(a={});const{ancestorScroll:n=!0,ancestorResize:i=!0,elementResize:o="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:l=!1}=a,c=jr(e),u=n||i?[...c?Ar(c):[],...Ar(t)]:[];u.forEach((e=>{n&&e.addEventListener("scroll",r,{passive:!0}),i&&e.addEventListener("resize",r)}));const g=c&&s?function(e,t){let r,a=null;const n=Tr(e);function i(){var e;clearTimeout(r),null==(e=a)||e.disconnect(),a=null}return function o(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),i();const{left:c,top:u,width:g,height:d}=e.getBoundingClientRect();if(s||t(),!g||!d)return;const f={rootMargin:-Er(u)+"px "+-Er(n.clientWidth-(c+g))+"px "+-Er(n.clientHeight-(u+d))+"px "+-Er(c)+"px",threshold:Or(0,wr(1,l))||1};let p=!0;function v(e){const t=e[0].intersectionRatio;if(t!==l){if(!p)return o();t?o(!1,t):r=setTimeout((()=>{o(!1,1e-7)}),1e3)}p=!1}try{a=new IntersectionObserver(v,{...f,root:n.ownerDocument})}catch(e){a=new IntersectionObserver(v,f)}a.observe(e)}(!0),i}(c,r):null;let d,f=-1,p=null;o&&(p=new ResizeObserver((e=>{let[a]=e;a&&a.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame((()=>{var e;null==(e=p)||e.observe(t)}))),r()})),c&&!l&&p.observe(c),p.observe(t));let v=l?zr(e):null;return l&&function t(){const a=zr(e);!v||a.x===v.x&&a.y===v.y&&a.width===v.width&&a.height===v.height||r(),v=a,d=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach((e=>{n&&e.removeEventListener("scroll",r),i&&e.removeEventListener("resize",r)})),null==g||g(),null==(e=p)||e.disconnect(),p=null,l&&cancelAnimationFrame(d)}}(a,l.current,m,{elementResize:"ResizeObserver"in window}))}),[a,m]);Ur((function(){b()}),[b]);var S=(0,s.useCallback)((function(e){l.current=e,b()}),[b]);if(!t&&"fixed"!==o||!v)return null;var w=mr("div",He({ref:S},Jr(je(je({},e),{},{offset:v.offset,position:o,rect:v.rect}),"menuPortal",{"menu-portal":!0}),n),r);return mr(ua.Provider,{value:f},t?(0,Sr.createPortal)(w,t):w)},LoadingMessage:ha,NoOptionsMessage:va,MultiValue:function(e){var t=e.children,r=e.components,a=e.data,n=e.innerProps,i=e.isDisabled,o=e.removeProps,s=e.selectProps,l=r.Container,c=r.Label,u=r.Remove;return mr(l,{data:a,innerProps:je(je({},Jr(e,"multiValue",{"multi-value":!0,"multi-value--is-disabled":i})),n),selectProps:s},mr(c,{data:a,innerProps:je({},Jr(e,"multiValueLabel",{"multi-value__label":!0})),selectProps:s},t),mr(u,{data:a,innerProps:je(je({},Jr(e,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(t||"option")},o),selectProps:s}))},MultiValueContainer:Ra,MultiValueLabel:Ra,MultiValueRemove:function(e){var t=e.children,r=e.innerProps;return mr("div",He({role:"button"},r),t||mr(wa,{size:14}))},Option:function(e){var t=e.children,r=e.isDisabled,a=e.isFocused,n=e.isSelected,i=e.innerRef,o=e.innerProps;return mr("div",He({},Jr(e,"option",{option:!0,"option--is-disabled":r,"option--is-focused":a,"option--is-selected":n}),{ref:i,"aria-disabled":r},o),t)},Placeholder:function(e){var t=e.children,r=e.innerProps;return mr("div",He({},Jr(e,"placeholder",{placeholder:!0}),r),t)},SelectContainer:function(e){var t=e.children,r=e.innerProps,a=e.isDisabled,n=e.isRtl;return mr("div",He({},Jr(e,"container",{"--is-disabled":a,"--is-rtl":n}),r),t)},SingleValue:function(e){var t=e.children,r=e.isDisabled,a=e.innerProps;return mr("div",He({},Jr(e,"singleValue",{"single-value":!0,"single-value--is-disabled":r}),a),t)},ValueContainer:function(e){var t=e.children,r=e.innerProps,a=e.isMulti,n=e.hasValue;return mr("div",He({},Jr(e,"valueContainer",{"value-container":!0,"value-container--is-multi":a,"value-container--has-value":n}),r),t)}},Aa=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function Fa(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(!((a=e[r])===(n=t[r])||Aa(a)&&Aa(n)))return!1;var a,n;return!0}for(var ja={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},Va=function(e){return mr("span",He({css:ja},e))},Ba={guidance:function(e){var t=e.isSearchable,r=e.isMulti,a=e.isDisabled,n=e.tabSelectsValue;switch(e.context){case"menu":return"Use Up and Down to choose options".concat(a?"":", press Enter to select the currently focused option",", press Escape to exit the menu").concat(n?", press Tab to select the option and exit the menu":"",".");case"input":return"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(r?" press left to focus selected values":"");case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,r=e.label,a=void 0===r?"":r,n=e.labels,i=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(a,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(n.length>1?"s":""," ").concat(n.join(","),", selected.");case"select-option":return"option ".concat(a,i?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,r=e.focused,a=e.options,n=e.label,i=void 0===n?"":n,o=e.selectValue,s=e.isDisabled,l=e.isSelected,c=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&o)return"value ".concat(i," focused, ").concat(c(o,r),".");if("menu"===t){var u=s?" disabled":"",g="".concat(l?"selected":"focused").concat(u);return"option ".concat(i," ").concat(g,", ").concat(c(a,r),".")}return""},onFilter:function(e){var t=e.inputValue,r=e.resultsMessage;return"".concat(r).concat(t?" for search term "+t:"",".")}},Ha=function(e){var t=e.ariaSelection,r=e.focusedOption,a=e.focusedValue,n=e.focusableOptions,i=e.isFocused,o=e.selectValue,l=e.selectProps,c=e.id,u=l.ariaLiveMessages,g=l.getOptionLabel,d=l.inputValue,f=l.isMulti,p=l.isOptionDisabled,v=l.isSearchable,h=l.menuIsOpen,y=l.options,m=l.screenReaderStatus,b=l.tabSelectsValue,S=l["aria-label"],w=l["aria-live"],O=(0,s.useMemo)((function(){return je(je({},Ba),u||{})}),[u]),C=(0,s.useMemo)((function(){var e,r="";if(t&&O.onChange){var a=t.option,n=t.options,i=t.removedValue,s=t.removedValues,l=t.value,c=i||a||(e=l,Array.isArray(e)?null:e),u=c?g(c):"",d=n||s||void 0,f=d?d.map(g):[],v=je({isDisabled:c&&p(c,o),label:u,labels:f},t);r=O.onChange(v)}return r}),[t,O,p,o,g]),E=(0,s.useMemo)((function(){var e="",t=r||a,i=!!(r&&o&&o.includes(r));if(t&&O.onFocus){var s={focused:t,label:g(t),isDisabled:p(t,o),isSelected:i,options:n,context:t===r?"menu":"value",selectValue:o};e=O.onFocus(s)}return e}),[r,a,g,p,O,n,o]),D=(0,s.useMemo)((function(){var e="";if(h&&y.length&&O.onFilter){var t=m({count:n.length});e=O.onFilter({inputValue:d,resultsMessage:t})}return e}),[n,d,h,O,y,m]),N=(0,s.useMemo)((function(){var e="";if(O.guidance){var t=a?"value":h?"menu":"input";e=O.guidance({"aria-label":S,context:t,isDisabled:r&&p(r,o),isMulti:f,isSearchable:v,tabSelectsValue:b})}return e}),[S,r,a,f,p,v,h,O,o,b]),x="".concat(E," ").concat(D," ").concat(N),T=mr(s.Fragment,null,mr("span",{id:"aria-selection"},C),mr("span",{id:"aria-context"},x)),I="initial-input-focus"===(null==t?void 0:t.action);return mr(s.Fragment,null,mr(Va,{id:c},I&&T),mr(Va,{"aria-live":w,"aria-atomic":"false","aria-relevant":"additions text"},i&&!I&&T))},za=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],Ua=new RegExp("["+za.map((function(e){return e.letters})).join("")+"]","g"),Ga={},Ka=0;Ka<za.length;Ka++)for(var $a=za[Ka],Wa=0;Wa<$a.letters.length;Wa++)Ga[$a.letters[Wa]]=$a.base;var qa=function(e){return e.replace(Ua,(function(e){return Ga[e]}))},Ya=function(e,t){void 0===t&&(t=Fa);var r=null;function a(){for(var a=[],n=0;n<arguments.length;n++)a[n]=arguments[n];if(r&&r.lastThis===this&&t(a,r.lastArgs))return r.lastResult;var i=e.apply(this,a);return r={lastResult:i,lastArgs:a,lastThis:this},i}return a.clear=function(){r=null},a}(qa),Ja=function(e){return e.replace(/^\s+|\s+$/g,"")},Xa=function(e){return"".concat(e.label," ").concat(e.value)},Za=["innerRef"];function Qa(e){var t=e.innerRef,r=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];var n=Object.entries(e).filter((function(e){var t=y(e,1)[0];return!r.includes(t)}));return n.reduce((function(e,t){var r=y(t,2),a=r[0],n=r[1];return e[a]=n,e}),{})}(Ve(e,Za),"onExited","in","enter","exit","appear");return mr("input",He({ref:t},r,{css:br({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var en=["boxSizing","height","overflow","paddingRight","position"],tn={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function rn(e){e.preventDefault()}function an(e){e.stopPropagation()}function nn(){var e=this.scrollTop,t=this.scrollHeight,r=e+this.offsetHeight;0===e?this.scrollTop=1:r===t&&(this.scrollTop=e-1)}function on(){return"ontouchstart"in window||navigator.maxTouchPoints}var sn=!("undefined"==typeof window||!window.document||!window.document.createElement),ln=0,cn={capture:!1,passive:!1},un=function(){return document.activeElement&&document.activeElement.blur()},gn={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function dn(e){var t=e.children,r=e.lockEnabled,a=e.captureEnabled,n=function(e){var t=e.isEnabled,r=e.onBottomArrive,a=e.onBottomLeave,n=e.onTopArrive,i=e.onTopLeave,o=(0,s.useRef)(!1),l=(0,s.useRef)(!1),c=(0,s.useRef)(0),u=(0,s.useRef)(null),g=(0,s.useCallback)((function(e,t){if(null!==u.current){var s=u.current,c=s.scrollTop,g=s.scrollHeight,d=s.clientHeight,f=u.current,p=t>0,v=g-d-c,h=!1;v>t&&o.current&&(a&&a(e),o.current=!1),p&&l.current&&(i&&i(e),l.current=!1),p&&t>v?(r&&!o.current&&r(e),f.scrollTop=g,h=!0,o.current=!0):!p&&-t>c&&(n&&!l.current&&n(e),f.scrollTop=0,h=!0,l.current=!0),h&&function(e){e.preventDefault(),e.stopPropagation()}(e)}}),[r,a,n,i]),d=(0,s.useCallback)((function(e){g(e,e.deltaY)}),[g]),f=(0,s.useCallback)((function(e){c.current=e.changedTouches[0].clientY}),[]),p=(0,s.useCallback)((function(e){var t=c.current-e.changedTouches[0].clientY;g(e,t)}),[g]),v=(0,s.useCallback)((function(e){if(e){var t=!!oa&&{passive:!1};e.addEventListener("wheel",d,t),e.addEventListener("touchstart",f,t),e.addEventListener("touchmove",p,t)}}),[p,f,d]),h=(0,s.useCallback)((function(e){e&&(e.removeEventListener("wheel",d,!1),e.removeEventListener("touchstart",f,!1),e.removeEventListener("touchmove",p,!1))}),[p,f,d]);return(0,s.useEffect)((function(){if(t){var e=u.current;return v(e),function(){h(e)}}}),[t,v,h]),function(e){u.current=e}}({isEnabled:void 0===a||a,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),i=function(e){var t=e.isEnabled,r=e.accountForScrollbars,a=void 0===r||r,n=(0,s.useRef)({}),i=(0,s.useRef)(null),o=(0,s.useCallback)((function(e){if(sn){var t=document.body,r=t&&t.style;if(a&&en.forEach((function(e){var t=r&&r[e];n.current[e]=t})),a&&ln<1){var i=parseInt(n.current.paddingRight,10)||0,o=document.body?document.body.clientWidth:0,s=window.innerWidth-o+i||0;Object.keys(tn).forEach((function(e){var t=tn[e];r&&(r[e]=t)})),r&&(r.paddingRight="".concat(s,"px"))}t&&on()&&(t.addEventListener("touchmove",rn,cn),e&&(e.addEventListener("touchstart",nn,cn),e.addEventListener("touchmove",an,cn))),ln+=1}}),[a]),l=(0,s.useCallback)((function(e){if(sn){var t=document.body,r=t&&t.style;ln=Math.max(ln-1,0),a&&ln<1&&en.forEach((function(e){var t=n.current[e];r&&(r[e]=t)})),t&&on()&&(t.removeEventListener("touchmove",rn,cn),e&&(e.removeEventListener("touchstart",nn,cn),e.removeEventListener("touchmove",an,cn)))}}),[a]);return(0,s.useEffect)((function(){if(t){var e=i.current;return o(e),function(){l(e)}}}),[t,o,l]),function(e){i.current=e}}({isEnabled:r});return mr(s.Fragment,null,r&&mr("div",{onClick:un,css:gn}),t((function(e){n(e),i(e)})))}var fn={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},pn=function(e){var t=e.name,r=e.onFocus;return mr("input",{required:!0,name:t,tabIndex:-1,"aria-hidden":"true",onFocus:r,css:fn,value:"",onChange:function(){}})},vn={clearIndicator:Da,container:function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},control:function(e,t){var r=e.isDisabled,a=e.isFocused,n=e.theme,i=n.colors,o=n.borderRadius;return je({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:n.spacing.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},t?{}:{backgroundColor:r?i.neutral5:i.neutral0,borderColor:r?i.neutral10:a?i.primary:i.neutral20,borderRadius:o,borderStyle:"solid",borderWidth:1,boxShadow:a?"0 0 0 1px ".concat(i.primary):void 0,"&:hover":{borderColor:a?i.primary:i.neutral30}})},dropdownIndicator:Ea,group:function(e,t){var r=e.theme.spacing;return t?{}:{paddingBottom:2*r.baseUnit,paddingTop:2*r.baseUnit}},groupHeading:function(e,t){var r=e.theme,a=r.colors,n=r.spacing;return je({label:"group",cursor:"default",display:"block"},t?{}:{color:a.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*n.baseUnit,paddingRight:3*n.baseUnit,textTransform:"uppercase"})},indicatorsContainer:function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},indicatorSeparator:function(e,t){var r=e.isDisabled,a=e.theme,n=a.spacing.baseUnit,i=a.colors;return je({label:"indicatorSeparator",alignSelf:"stretch",width:1},t?{}:{backgroundColor:r?i.neutral10:i.neutral20,marginBottom:2*n,marginTop:2*n})},input:function(e,t){var r=e.isDisabled,a=e.value,n=e.theme,i=n.spacing,o=n.colors;return je(je({visibility:r?"hidden":"visible",transform:a?"translateZ(0)":""},Pa),t?{}:{margin:i.baseUnit/2,paddingBottom:i.baseUnit/2,paddingTop:i.baseUnit/2,color:o.neutral80})},loadingIndicator:function(e,t){var r=e.isFocused,a=e.size,n=e.theme,i=n.colors,o=n.spacing.baseUnit;return je({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:a,lineHeight:1,marginRight:a,textAlign:"center",verticalAlign:"middle"},t?{}:{color:r?i.neutral60:i.neutral20,padding:2*o})},loadingMessage:pa,menu:function(e,t){var r,a=e.placement,n=e.theme,i=n.borderRadius,s=n.spacing,l=n.colors;return je((o(r={label:"menu"},function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(a),"100%"),o(r,"position","absolute"),o(r,"width","100%"),o(r,"zIndex",1),r),t?{}:{backgroundColor:l.neutral0,borderRadius:i,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:s.menuGutter,marginTop:s.menuGutter})},menuList:function(e,t){var r=e.maxHeight,a=e.theme.spacing.baseUnit;return je({maxHeight:r,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},t?{}:{paddingBottom:a,paddingTop:a})},menuPortal:function(e){var t=e.rect,r=e.offset,a=e.position;return{left:t.left,position:a,top:r,width:t.width,zIndex:1}},multiValue:function(e,t){var r=e.theme,a=r.spacing,n=r.borderRadius,i=r.colors;return je({label:"multiValue",display:"flex",minWidth:0},t?{}:{backgroundColor:i.neutral10,borderRadius:n/2,margin:a.baseUnit/2})},multiValueLabel:function(e,t){var r=e.theme,a=r.borderRadius,n=r.colors,i=e.cropWithEllipsis;return je({overflow:"hidden",textOverflow:i||void 0===i?"ellipsis":void 0,whiteSpace:"nowrap"},t?{}:{borderRadius:a/2,color:n.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},multiValueRemove:function(e,t){var r=e.theme,a=r.spacing,n=r.borderRadius,i=r.colors,o=e.isFocused;return je({alignItems:"center",display:"flex"},t?{}:{borderRadius:n/2,backgroundColor:o?i.dangerLight:void 0,paddingLeft:a.baseUnit,paddingRight:a.baseUnit,":hover":{backgroundColor:i.dangerLight,color:i.danger}})},noOptionsMessage:fa,option:function(e,t){var r=e.isDisabled,a=e.isFocused,n=e.isSelected,i=e.theme,o=i.spacing,s=i.colors;return je({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},t?{}:{backgroundColor:n?s.primary:a?s.primary25:"transparent",color:r?s.neutral20:n?s.neutral0:"inherit",padding:"".concat(2*o.baseUnit,"px ").concat(3*o.baseUnit,"px"),":active":{backgroundColor:r?void 0:n?s.primary:s.primary50}})},placeholder:function(e,t){var r=e.theme,a=r.spacing,n=r.colors;return je({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},t?{}:{color:n.neutral50,marginLeft:a.baseUnit/2,marginRight:a.baseUnit/2})},singleValue:function(e,t){var r=e.isDisabled,a=e.theme,n=a.spacing,i=a.colors;return je({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},t?{}:{color:r?i.neutral40:i.neutral80,marginLeft:n.baseUnit/2,marginRight:n.baseUnit/2})},valueContainer:function(e,t){var r=e.theme.spacing,a=e.isMulti,n=e.hasValue,i=e.selectProps.controlShouldRenderValue;return je({alignItems:"center",display:a&&n&&i?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},t?{}:{padding:"".concat(r.baseUnit/2,"px ").concat(2*r.baseUnit,"px")})}},hn={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},yn={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:ra(),captureMenuScroll:!ra(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var r=je({ignoreCase:!0,ignoreAccents:!0,stringify:Xa,trim:!0,matchFrom:"any"},undefined),a=r.ignoreCase,n=r.ignoreAccents,i=r.stringify,o=r.trim,s=r.matchFrom,l=o?Ja(t):t,c=o?Ja(i(e)):i(e);return a&&(l=l.toLowerCase(),c=c.toLowerCase()),n&&(l=Ya(l),c=qa(c)),"start"===s?c.substr(0,l.length)===l:c.indexOf(l)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:function(e){return e.label},getOptionValue:function(e){return e.value},isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!function(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function mn(e,t,r,a){return{type:"option",data:t,isDisabled:En(e,t,r),isSelected:Dn(e,t,r),label:On(e,t),value:Cn(e,t),index:a}}function bn(e,t){return e.options.map((function(r,a){if("options"in r){var n=r.options.map((function(r,a){return mn(e,r,t,a)})).filter((function(t){return wn(e,t)}));return n.length>0?{type:"group",data:r,options:n,index:a}:void 0}var i=mn(e,r,t,a);return wn(e,i)?i:void 0})).filter(sa)}function Sn(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,Le(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function wn(e,t){var r=e.inputValue,a=void 0===r?"":r,n=t.data,i=t.isSelected,o=t.label,s=t.value;return(!xn(e)||!i)&&Nn(e,{label:o,value:s,data:n},a)}var On=function(e,t){return e.getOptionLabel(t)},Cn=function(e,t){return e.getOptionValue(t)};function En(e,t,r){return"function"==typeof e.isOptionDisabled&&e.isOptionDisabled(t,r)}function Dn(e,t,r){if(r.indexOf(t)>-1)return!0;if("function"==typeof e.isOptionSelected)return e.isOptionSelected(t,r);var a=Cn(e,t);return r.some((function(t){return Cn(e,t)===a}))}function Nn(e,t,r){return!e.filterOption||e.filterOption(t,r)}var xn=function(e){var t=e.hideSelectedOptions,r=e.isMulti;return void 0===t?r:t},Tn=1,In=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ue(e,t)}(r,e);var t=function(e){var t=Ke();return function(){var r,a=Ge(e);if(t){var i=Ge(this).constructor;r=Reflect.construct(a,arguments,i)}else r=a.apply(this,arguments);return function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,r)}}(r);function r(e){var a;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),(a=t.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0},a.blockOptionHover=!1,a.isComposing=!1,a.commonProps=void 0,a.initialTouchX=0,a.initialTouchY=0,a.instancePrefix="",a.openAfterFocus=!1,a.scrollToFocusedOptionOnUpdate=!1,a.userIsDragging=void 0,a.controlRef=null,a.getControlRef=function(e){a.controlRef=e},a.focusedOptionRef=null,a.getFocusedOptionRef=function(e){a.focusedOptionRef=e},a.menuListRef=null,a.getMenuListRef=function(e){a.menuListRef=e},a.inputRef=null,a.getInputRef=function(e){a.inputRef=e},a.focus=a.focusInput,a.blur=a.blurInput,a.onChange=function(e,t){var r=a.props,n=r.onChange,i=r.name;t.name=i,a.ariaOnChange(e,t),n(e,t)},a.setValue=function(e,t,r){var n=a.props,i=n.closeMenuOnSelect,o=n.isMulti,s=n.inputValue;a.onInputChange("",{action:"set-value",prevInputValue:s}),i&&(a.setState({inputIsHiddenAfterUpdate:!o}),a.onMenuClose()),a.setState({clearFocusValueOnUpdate:!0}),a.onChange(e,{action:t,option:r})},a.selectOption=function(e){var t=a.props,r=t.blurInputOnSelect,n=t.isMulti,i=t.name,o=a.state.selectValue,s=n&&a.isOptionSelected(e,o),l=a.isOptionDisabled(e,o);if(s){var c=a.getOptionValue(e);a.setValue(o.filter((function(e){return a.getOptionValue(e)!==c})),"deselect-option",e)}else{if(l)return void a.ariaOnChange(e,{action:"select-option",option:e,name:i});n?a.setValue([].concat(Le(o),[e]),"select-option",e):a.setValue(e,"select-option")}r&&a.blurInput()},a.removeValue=function(e){var t=a.props.isMulti,r=a.state.selectValue,n=a.getOptionValue(e),i=r.filter((function(e){return a.getOptionValue(e)!==n})),o=la(t,i,i[0]||null);a.onChange(o,{action:"remove-value",removedValue:e}),a.focusInput()},a.clearValue=function(){var e=a.state.selectValue;a.onChange(la(a.props.isMulti,[],null),{action:"clear",removedValues:e})},a.popValue=function(){var e=a.props.isMulti,t=a.state.selectValue,r=t[t.length-1],n=t.slice(0,t.length-1),i=la(e,n,n[0]||null);a.onChange(i,{action:"pop-value",removedValue:r})},a.getValue=function(){return a.state.selectValue},a.cx=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Wr.apply(void 0,[a.props.classNamePrefix].concat(t))},a.getOptionLabel=function(e){return On(a.props,e)},a.getOptionValue=function(e){return Cn(a.props,e)},a.getStyles=function(e,t){var r=a.props.unstyled,n=vn[e](t,r);n.boxSizing="border-box";var i=a.props.styles[e];return i?i(n,t):n},a.getClassNames=function(e,t){var r,n;return null===(r=(n=a.props.classNames)[e])||void 0===r?void 0:r.call(n,t)},a.getElementId=function(e){return"".concat(a.instancePrefix,"-").concat(e)},a.getComponents=function(){return e=a.props,je(je({},La),e.components);var e},a.buildCategorizedOptions=function(){return bn(a.props,a.state.selectValue)},a.getCategorizedOptions=function(){return a.props.menuIsOpen?a.buildCategorizedOptions():[]},a.buildFocusableOptions=function(){return Sn(a.buildCategorizedOptions())},a.getFocusableOptions=function(){return a.props.menuIsOpen?a.buildFocusableOptions():[]},a.ariaOnChange=function(e,t){a.setState({ariaSelection:je({value:e},t)})},a.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),a.focusInput())},a.onMenuMouseMove=function(e){a.blockOptionHover=!1},a.onControlMouseDown=function(e){if(!e.defaultPrevented){var t=a.props.openMenuOnClick;a.state.isFocused?a.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&a.onMenuClose():t&&a.openMenu("first"):(t&&(a.openAfterFocus=!0),a.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},a.onDropdownIndicatorMouseDown=function(e){if(!(e&&"mousedown"===e.type&&0!==e.button||a.props.isDisabled)){var t=a.props,r=t.isMulti,n=t.menuIsOpen;a.focusInput(),n?(a.setState({inputIsHiddenAfterUpdate:!r}),a.onMenuClose()):a.openMenu("first"),e.preventDefault()}},a.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(a.clearValue(),e.preventDefault(),a.openAfterFocus=!1,"touchend"===e.type?a.focusInput():setTimeout((function(){return a.focusInput()})))},a.onScroll=function(e){"boolean"==typeof a.props.closeMenuOnScroll?e.target instanceof HTMLElement&&Xr(e.target)&&a.props.onMenuClose():"function"==typeof a.props.closeMenuOnScroll&&a.props.closeMenuOnScroll(e)&&a.props.onMenuClose()},a.onCompositionStart=function(){a.isComposing=!0},a.onCompositionEnd=function(){a.isComposing=!1},a.onTouchStart=function(e){var t=e.touches,r=t&&t.item(0);r&&(a.initialTouchX=r.clientX,a.initialTouchY=r.clientY,a.userIsDragging=!1)},a.onTouchMove=function(e){var t=e.touches,r=t&&t.item(0);if(r){var n=Math.abs(r.clientX-a.initialTouchX),i=Math.abs(r.clientY-a.initialTouchY);a.userIsDragging=n>5||i>5}},a.onTouchEnd=function(e){a.userIsDragging||(a.controlRef&&!a.controlRef.contains(e.target)&&a.menuListRef&&!a.menuListRef.contains(e.target)&&a.blurInput(),a.initialTouchX=0,a.initialTouchY=0)},a.onControlTouchEnd=function(e){a.userIsDragging||a.onControlMouseDown(e)},a.onClearIndicatorTouchEnd=function(e){a.userIsDragging||a.onClearIndicatorMouseDown(e)},a.onDropdownIndicatorTouchEnd=function(e){a.userIsDragging||a.onDropdownIndicatorMouseDown(e)},a.handleInputChange=function(e){var t=a.props.inputValue,r=e.currentTarget.value;a.setState({inputIsHiddenAfterUpdate:!1}),a.onInputChange(r,{action:"input-change",prevInputValue:t}),a.props.menuIsOpen||a.onMenuOpen()},a.onInputFocus=function(e){a.props.onFocus&&a.props.onFocus(e),a.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(a.openAfterFocus||a.props.openMenuOnFocus)&&a.openMenu("first"),a.openAfterFocus=!1},a.onInputBlur=function(e){var t=a.props.inputValue;a.menuListRef&&a.menuListRef.contains(document.activeElement)?a.inputRef.focus():(a.props.onBlur&&a.props.onBlur(e),a.onInputChange("",{action:"input-blur",prevInputValue:t}),a.onMenuClose(),a.setState({focusedValue:null,isFocused:!1}))},a.onOptionHover=function(e){a.blockOptionHover||a.state.focusedOption===e||a.setState({focusedOption:e})},a.shouldHideSelectedOptions=function(){return xn(a.props)},a.onValueInputFocus=function(e){e.preventDefault(),e.stopPropagation(),a.focus()},a.onKeyDown=function(e){var t=a.props,r=t.isMulti,n=t.backspaceRemovesValue,i=t.escapeClearsValue,o=t.inputValue,s=t.isClearable,l=t.isDisabled,c=t.menuIsOpen,u=t.onKeyDown,g=t.tabSelectsValue,d=t.openMenuOnFocus,f=a.state,p=f.focusedOption,v=f.focusedValue,h=f.selectValue;if(!(l||"function"==typeof u&&(u(e),e.defaultPrevented))){switch(a.blockOptionHover=!0,e.key){case"ArrowLeft":if(!r||o)return;a.focusValue("previous");break;case"ArrowRight":if(!r||o)return;a.focusValue("next");break;case"Delete":case"Backspace":if(o)return;if(v)a.removeValue(v);else{if(!n)return;r?a.popValue():s&&a.clearValue()}break;case"Tab":if(a.isComposing)return;if(e.shiftKey||!c||!g||!p||d&&a.isOptionSelected(p,h))return;a.selectOption(p);break;case"Enter":if(229===e.keyCode)break;if(c){if(!p)return;if(a.isComposing)return;a.selectOption(p);break}return;case"Escape":c?(a.setState({inputIsHiddenAfterUpdate:!1}),a.onInputChange("",{action:"menu-close",prevInputValue:o}),a.onMenuClose()):s&&i&&a.clearValue();break;case" ":if(o)return;if(!c){a.openMenu("first");break}if(!p)return;a.selectOption(p);break;case"ArrowUp":c?a.focusOption("up"):a.openMenu("last");break;case"ArrowDown":c?a.focusOption("down"):a.openMenu("first");break;case"PageUp":if(!c)return;a.focusOption("pageup");break;case"PageDown":if(!c)return;a.focusOption("pagedown");break;case"Home":if(!c)return;a.focusOption("first");break;case"End":if(!c)return;a.focusOption("last");break;default:return}e.preventDefault()}},a.instancePrefix="react-select-"+(a.props.instanceId||++Tn),a.state.selectValue=qr(e.value),e.menuIsOpen&&a.state.selectValue.length){var n=a.buildFocusableOptions(),i=n.indexOf(a.state.selectValue[0]);a.state.focusedOption=n[i]}return a}return function(e,t,r){t&&ze(e.prototype,t),r&&ze(e,r),Object.defineProperty(e,"prototype",{writable:!1})}(r,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&ta(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isDisabled,a=t.menuIsOpen,n=this.state.isFocused;(n&&!r&&e.isDisabled||n&&a&&!e.menuIsOpen)&&this.focusInput(),n&&r&&!e.isDisabled?this.setState({isFocused:!1},this.onMenuClose):n||r||!e.isDisabled||this.inputRef!==document.activeElement||this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(ta(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,r=this.state,a=r.selectValue,n=r.isFocused,i=this.buildFocusableOptions(),o="first"===e?0:i.length-1;if(!this.props.isMulti){var s=i.indexOf(a[0]);s>-1&&(o=s)}this.scrollToFocusedOptionOnUpdate=!(n&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:i[o]},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,r=t.selectValue,a=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var n=r.indexOf(a);a||(n=-1);var i=r.length-1,o=-1;if(r.length){switch(e){case"previous":o=0===n?0:-1===n?i:n-1;break;case"next":n>-1&&n<i&&(o=n+1)}this.setState({inputIsHidden:-1!==o,focusedValue:r[o]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,r=this.state.focusedOption,a=this.getFocusableOptions();if(a.length){var n=0,i=a.indexOf(r);r||(i=-1),"up"===e?n=i>0?i-1:a.length-1:"down"===e?n=(i+1)%a.length:"pageup"===e?(n=i-t)<0&&(n=0):"pagedown"===e?(n=i+t)>a.length-1&&(n=a.length-1):"last"===e&&(n=a.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:a[n],focusedValue:null})}}},{key:"getTheme",value:function(){return this.props.theme?"function"==typeof this.props.theme?this.props.theme(hn):je(je({},hn),this.props.theme):hn}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,r=this.getStyles,a=this.getClassNames,n=this.getValue,i=this.selectOption,o=this.setValue,s=this.props,l=s.isMulti,c=s.isRtl,u=s.options;return{clearValue:e,cx:t,getStyles:r,getClassNames:a,getValue:n,hasValue:this.hasValue(),isMulti:l,isRtl:c,options:u,selectOption:i,selectProps:s,setValue:o,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,r=e.isMulti;return void 0===t?r:t}},{key:"isOptionDisabled",value:function(e,t){return En(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return Dn(this.props,e,t)}},{key:"filterOption",value:function(e,t){return Nn(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"==typeof this.props.formatOptionLabel){var r=this.props.inputValue,a=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:r,selectValue:a})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,r=e.isSearchable,a=e.inputId,n=e.inputValue,i=e.tabIndex,o=e.form,l=e.menuIsOpen,c=e.required,u=this.getComponents().Input,g=this.state,d=g.inputIsHidden,f=g.ariaSelection,p=this.commonProps,v=a||this.getElementId("input"),h=je(je(je({"aria-autocomplete":"list","aria-expanded":l,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":c,role:"combobox"},l&&{"aria-controls":this.getElementId("listbox"),"aria-owns":this.getElementId("listbox")}),!r&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null==f?void 0:f.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return r?s.createElement(u,He({},p,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:v,innerRef:this.getInputRef,isDisabled:t,isHidden:d,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:i,form:o,type:"text",value:n},h)):s.createElement(Qa,He({id:v,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:Kr,onFocus:this.onInputFocus,disabled:t,tabIndex:i,inputMode:"none",form:o,value:""},h))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),r=t.MultiValue,a=t.MultiValueContainer,n=t.MultiValueLabel,i=t.MultiValueRemove,o=t.SingleValue,l=t.Placeholder,c=this.commonProps,u=this.props,g=u.controlShouldRenderValue,d=u.isDisabled,f=u.isMulti,p=u.inputValue,v=u.placeholder,h=this.state,y=h.selectValue,m=h.focusedValue,b=h.isFocused;if(!this.hasValue()||!g)return p?null:s.createElement(l,He({},c,{key:"placeholder",isDisabled:d,isFocused:b,innerProps:{id:this.getElementId("placeholder")}}),v);if(f)return y.map((function(t,o){var l=t===m,u="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return s.createElement(r,He({},c,{components:{Container:a,Label:n,Remove:i},isFocused:l,isDisabled:d,key:u,index:o,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(p)return null;var S=y[0];return s.createElement(o,He({},c,{data:S,isDisabled:d}),this.formatOptionLabel(S,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,r=this.props,a=r.isDisabled,n=r.isLoading,i=this.state.isFocused;if(!this.isClearable()||!e||a||!this.hasValue()||n)return null;var o={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return s.createElement(e,He({},t,{innerProps:o,isFocused:i}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,r=this.props,a=r.isDisabled,n=r.isLoading,i=this.state.isFocused;return e&&n?s.createElement(e,He({},t,{innerProps:{"aria-hidden":"true"},isDisabled:a,isFocused:i})):null}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,r=e.IndicatorSeparator;if(!t||!r)return null;var a=this.commonProps,n=this.props.isDisabled,i=this.state.isFocused;return s.createElement(r,He({},a,{isDisabled:n,isFocused:i}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,r=this.props.isDisabled,a=this.state.isFocused,n={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return s.createElement(e,He({},t,{innerProps:n,isDisabled:r,isFocused:a}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),r=t.Group,a=t.GroupHeading,n=t.Menu,i=t.MenuList,o=t.MenuPortal,l=t.LoadingMessage,c=t.NoOptionsMessage,u=t.Option,g=this.commonProps,d=this.state.focusedOption,f=this.props,p=f.captureMenuScroll,v=f.inputValue,h=f.isLoading,y=f.loadingMessage,m=f.minMenuHeight,b=f.maxMenuHeight,S=f.menuIsOpen,w=f.menuPlacement,O=f.menuPosition,C=f.menuPortalTarget,E=f.menuShouldBlockScroll,D=f.menuShouldScrollIntoView,N=f.noOptionsMessage,x=f.onMenuScrollToTop,T=f.onMenuScrollToBottom;if(!S)return null;var I,M=function(t,r){var a=t.type,n=t.data,i=t.isDisabled,o=t.isSelected,l=t.label,c=t.value,f=d===n,p=i?void 0:function(){return e.onOptionHover(n)},v=i?void 0:function(){return e.selectOption(n)},h="".concat(e.getElementId("option"),"-").concat(r),y={id:h,onClick:v,onMouseMove:p,onMouseOver:p,tabIndex:-1};return s.createElement(u,He({},g,{innerProps:y,data:n,isDisabled:i,isSelected:o,key:h,label:l,type:a,value:c,isFocused:f,innerRef:f?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())I=this.getCategorizedOptions().map((function(t){if("group"===t.type){var n=t.data,i=t.options,o=t.index,l="".concat(e.getElementId("group"),"-").concat(o),c="".concat(l,"-heading");return s.createElement(r,He({},g,{key:l,data:n,options:i,Heading:a,headingProps:{id:c,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return M(e,"".concat(o,"-").concat(e.index))})))}if("option"===t.type)return M(t,"".concat(t.index))}));else if(h){var k=y({inputValue:v});if(null===k)return null;I=s.createElement(l,g,k)}else{var P=N({inputValue:v});if(null===P)return null;I=s.createElement(c,g,P)}var _={minMenuHeight:m,maxMenuHeight:b,menuPlacement:w,menuPosition:O,menuShouldScrollIntoView:D},R=s.createElement(ga,He({},g,_),(function(t){var r=t.ref,a=t.placerProps,o=a.placement,l=a.maxHeight;return s.createElement(n,He({},g,_,{innerRef:r,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove,id:e.getElementId("listbox")},isLoading:h,placement:o}),s.createElement(dn,{captureEnabled:p,onTopArrive:x,onBottomArrive:T,lockEnabled:E},(function(t){return s.createElement(i,He({},g,{innerRef:function(r){e.getMenuListRef(r),t(r)},isLoading:h,maxHeight:l,focusedOption:d}),I)})))}));return C||"fixed"===O?s.createElement(o,He({},g,{appendTo:C,controlElement:this.controlRef,menuPlacement:w,menuPosition:O}),R):R}},{key:"renderFormField",value:function(){var e=this,t=this.props,r=t.delimiter,a=t.isDisabled,n=t.isMulti,i=t.name,o=t.required,l=this.state.selectValue;if(o&&!this.hasValue()&&!a)return s.createElement(pn,{name:i,onFocus:this.onValueInputFocus});if(i&&!a){if(n){if(r){var c=l.map((function(t){return e.getOptionValue(t)})).join(r);return s.createElement("input",{name:i,type:"hidden",value:c})}var u=l.length>0?l.map((function(t,r){return s.createElement("input",{key:"i-".concat(r),name:i,type:"hidden",value:e.getOptionValue(t)})})):s.createElement("input",{name:i,type:"hidden",value:""});return s.createElement("div",null,u)}var g=l[0]?this.getOptionValue(l[0]):"";return s.createElement("input",{name:i,type:"hidden",value:g})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,r=t.ariaSelection,a=t.focusedOption,n=t.focusedValue,i=t.isFocused,o=t.selectValue,l=this.getFocusableOptions();return s.createElement(Ha,He({},e,{id:this.getElementId("live-region"),ariaSelection:r,focusedOption:a,focusedValue:n,isFocused:i,selectValue:o,focusableOptions:l}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,r=e.IndicatorsContainer,a=e.SelectContainer,n=e.ValueContainer,i=this.props,o=i.className,l=i.id,c=i.isDisabled,u=i.menuIsOpen,g=this.state.isFocused,d=this.commonProps=this.getCommonProps();return s.createElement(a,He({},d,{className:o,innerProps:{id:l,onKeyDown:this.onKeyDown},isDisabled:c,isFocused:g}),this.renderLiveRegion(),s.createElement(t,He({},d,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:c,isFocused:g,menuIsOpen:u}),s.createElement(n,He({},d,{isDisabled:c}),this.renderPlaceholderOrValue(),this.renderInput()),s.createElement(r,He({},d,{isDisabled:c}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var r=t.prevProps,a=t.clearFocusValueOnUpdate,n=t.inputIsHiddenAfterUpdate,i=t.ariaSelection,o=t.isFocused,s=t.prevWasFocused,l=e.options,c=e.value,u=e.menuIsOpen,g=e.inputValue,d=e.isMulti,f=qr(c),p={};if(r&&(c!==r.value||l!==r.options||u!==r.menuIsOpen||g!==r.inputValue)){var v=u?function(e,t){return Sn(bn(e,t))}(e,f):[],h=a?function(e,t){var r=e.focusedValue,a=e.selectValue.indexOf(r);if(a>-1){if(t.indexOf(r)>-1)return r;if(a<t.length)return t[a]}return null}(t,f):null,y=function(e,t){var r=e.focusedOption;return r&&t.indexOf(r)>-1?r:t[0]}(t,v);p={selectValue:f,focusedOption:y,focusedValue:h,clearFocusValueOnUpdate:!1}}var m=null!=n&&e!==r?{inputIsHidden:n,inputIsHiddenAfterUpdate:void 0}:{},b=i,S=o&&s;return o&&!S&&(b={value:la(d,f,f[0]||null),options:f,action:"initial-input-focus"},S=!s),"initial-input-focus"===(null==i?void 0:i.action)&&(b=null),je(je(je({},p),m),{},{prevProps:e,ariaSelection:b,prevWasFocused:S})}}]),r}(s.Component);In.defaultProps=yn;var Mn=(0,s.forwardRef)((function(e,t){var r=function(e){var t=e.defaultInputValue,r=void 0===t?"":t,a=e.defaultMenuIsOpen,n=void 0!==a&&a,i=e.defaultValue,o=void 0===i?null:i,l=e.inputValue,c=e.menuIsOpen,u=e.onChange,g=e.onInputChange,d=e.onMenuClose,f=e.onMenuOpen,p=e.value,v=Ve(e,Be),h=y((0,s.useState)(void 0!==l?l:r),2),m=h[0],b=h[1],S=y((0,s.useState)(void 0!==c?c:n),2),w=S[0],O=S[1],C=y((0,s.useState)(void 0!==p?p:o),2),E=C[0],D=C[1],N=(0,s.useCallback)((function(e,t){"function"==typeof u&&u(e,t),D(e)}),[u]),x=(0,s.useCallback)((function(e,t){var r;"function"==typeof g&&(r=g(e,t)),b(void 0!==r?r:e)}),[g]),T=(0,s.useCallback)((function(){"function"==typeof f&&f(),O(!0)}),[f]),I=(0,s.useCallback)((function(){"function"==typeof d&&d(),O(!1)}),[d]),M=void 0!==l?l:m,k=void 0!==c?c:w,P=void 0!==p?p:E;return je(je({},v),{},{inputValue:M,menuIsOpen:k,onChange:N,onInputChange:x,onMenuClose:I,onMenuOpen:T,value:P})}(e);return s.createElement(In,He({ref:t},r))})),kn=Mn;function Pn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Rn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Pn(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pn(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Ln=function(e){var t={className:"generate-advanced-select",classNamePrefix:"generate-advanced-select",isSearchable:!0,styles:{indicatorSeparator:function(){return{display:"none"}},indicatorsContainer:function(e){return Rn(Rn({},e),{},{maxHeight:"30px"})},menu:function(e){return Rn(Rn({},e),{},{zIndex:999999})}},instanceId:"input-field",maxMenuHeight:250,theme:function(e){return{borderRadius:2,colors:Rn(Rn({},e.colors),{},{primary:"var(--wp-admin-theme-color)",neutral20:"#757575",neutral30:"#757575"}),spacing:{controlHeight:30,baseUnit:3,menuGutter:3}}},menuPlacement:"auto"},r=Object.assign({},t,e);return Object.keys(r.options).forEach((function(e){var t=r.options[e].options;t?t.forEach((function(e){e.value===r.currentValue&&(r.value={label:e.label,value:r.currentValue})})):r.options[e].value===r.currentValue&&(r.value={label:r.options[e].label,value:r.currentValue})})),(0,s.createElement)(kn,Rn({},r))},An=window.wp.hooks;function Fn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function jn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fn(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fn(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Vn={html:{module:"core",group:"base",label:"HTML",placeholders:{}},body:{module:"core",group:"base",label:(0,m.__)("Body","generatepress"),placeholders:{fontSize:{value:"17px"},lineHeight:{value:"1.5"},marginBottom:{value:"1.5em"}}},"main-title":{module:"core",group:"header",label:(0,m.__)("Site Title","generatepress"),placeholders:{fontSize:{value:"25px"}}},"site-description":{module:"core",group:"header",label:(0,m.__)("Site Description","generatepress"),placeholders:{fontSize:{value:"15px"}}},"primary-menu-items":{module:"core",group:"primaryNavigation",label:(0,m.__)("Primary Menu Items","generatepress"),placeholders:{fontSize:{value:"15px"}}},"primary-sub-menu-items":{module:"core",group:"primaryNavigation",label:(0,m.__)("Primary Sub-Menu Items","generatepress"),placeholders:{fontSize:{value:"14px"}}},"primary-menu-toggle":{module:"core",group:"primaryNavigation",label:(0,m.__)("Primary Mobile Menu Toggle","generatepress"),placeholders:{fontSize:{value:"15px"}}},buttons:{module:"core",group:"content",label:(0,m.__)("Buttons","generatepress"),placeholders:{}},"all-headings":{module:"core",group:"content",label:(0,m.__)("All Headings","generatepress"),placeholders:{marginBottom:{value:"20px"}}},h1:{module:"core",group:"content",label:(0,m.__)("Heading 1 (H1)","generatepress"),placeholders:{fontSize:{value:"42px"},lineHeight:{value:"1.2em"},marginBottom:{value:"20px"}}},"single-content-title":{module:"core",group:"content",label:(0,m.__)("Single Content Title (H1)","generatepress"),placeholders:{}},h2:{module:"core",group:"content",label:(0,m.__)("Heading 2 (H2)","generatepress"),placeholders:{fontSize:{value:"35px"},lineHeight:{value:"1.2em"},marginBottom:{value:"20px"}}},"archive-content-title":{module:"core",group:"content",label:(0,m.__)("Archive Content Title (H2)","generatepress"),placeholders:{}},h3:{module:"core",group:"content",label:(0,m.__)("Heading 3 (H3)","generatepress"),placeholders:{fontSize:{value:"29px"},lineHeight:{value:"1.2em"},marginBottom:{value:"20px"}}},h4:{module:"core",group:"content",label:(0,m.__)("Heading 4 (H4)","generatepress"),placeholders:{fontSize:{value:"24px"},marginBottom:{value:"20px"}}},h5:{module:"core",group:"content",label:(0,m.__)("Heading 5 (H5)","generatepress"),placeholders:{fontSize:{value:"20px"},marginBottom:{value:"20px"}}},h6:{module:"core",group:"content",label:(0,m.__)("Heading 6 (H6)","generatepress"),placeholders:{marginBottom:{value:"20px"}}},"top-bar":{module:"core",group:"widgets",label:(0,m.__)("Top Bar","generatepress"),placeholders:{fontSize:{value:"13px"}}},"widget-titles":{module:"core",group:"widgets",label:(0,m.__)("Widget Titles","generatepress"),placeholders:{fontSize:{value:"20px"},marginBottom:{value:"30px"}}},footer:{module:"core",group:"footer",label:(0,m.__)("Footer Bar","generatepress"),placeholders:{fontSize:{value:"15px"}}},custom:{module:"core",group:"other",label:(0,m.__)("Custom","generatepress"),placeholders:{}}},Bn=function(){return(0,An.applyFilters)("generate_typography_elements",Vn)},Hn=function(e,t){var r=Bn(),a=void 0!==r[e.selector].placeholders[t]?r[e.selector].placeholders[t].value:"";if(t.includes("Tablet")){var n=t.replace("Tablet","");a=void 0!==r[e.selector].placeholders[n]?r[e.selector].placeholders[n].value:a,a=void 0!==e[n]&&e[n]?e[n]:a}if(t.includes("Mobile")){var i=t.replace("Mobile","Tablet"),o=t.replace("Mobile","");a=void 0!==r[e.selector].placeholders[o]?r[e.selector].placeholders[o].value:a,a=void 0!==r[e.selector].placeholders[i]?r[e.selector].placeholders[i].value:a,a=void 0!==e[o]&&e[o]?e[o]:a,a=void 0!==e[i]&&e[i]?e[i]:a}return a},zn=function(e){return["body","all-headings","h1","h2","h3","h4","h5","h6","widget-titles","custom"].includes(e)},Un=function(){var e=(0,An.applyFilters)("generate_typography_element_groups",{base:(0,m.__)("Base","generatepress"),header:(0,m.__)("Header","generatepress"),primaryNavigation:(0,m.__)("Primary Navigation","generatepress"),content:(0,m.__)("Content","generatepress"),widgets:(0,m.__)("Widgets","generatepress"),footer:(0,m.__)("Footer","generatepress")});return e.other=(0,m.__)("Other","generatepress"),e},Gn=function(){return wp.customize.control("generate_settings[font_manager]").setting.get()},Kn=function(e){var t=e.itemId,r=e.setOpen,a=e.isOpen,n=e.font,i=e.label;return(0,s.createElement)(u.Button,{className:"generate-font-manager--label",onClick:function(){r(t!==a&&t)}},!n.selector&&i,!!n.selector&&(0,s.createElement)(s.Fragment,null,function(e){var t=Bn(),r=void 0!==t[e.selector]?t[e.selector].label:e.selector;return"custom"===e.selector&&e.customSelector&&(r=e.customSelector),r}(n),!!n.fontFamily&&" / "+n.fontFamily,!!n.fontSize&&" / "+n.fontSize))},$n=function(e){var t=e.itemId,r=e.setOpen,a=e.isOpen;return(0,s.createElement)(u.Tooltip,{text:(0,m.__)("Open Typography Settings","generatepress")},(0,s.createElement)(u.Button,{className:"generate-font-manager--open",onClick:function(){r(t!==a&&t)}},xe(t===a?"chevron-up":"chevron-down")))},Wn=function(e){var t=e.onClick,r=(0,m.__)("This will permanently delete this typography element.","generatepress");return(0,s.createElement)(u.Tooltip,{text:(0,m.__)("Delete Typography Element","generatepress")},(0,s.createElement)(u.Button,{className:"generate-font-manager--delete-font",onClick:function(){window.confirm(r)&&t()},icon:xe("trash")}))},qn=function(e){var t,r,a,n,i=e.index,o=e.value,l=e.onChange;return(0,s.createElement)(u.BaseControl,{label:(0,m.__)("Target Element","generatepress"),id:"generate-typography-choose-element"},(0,s.createElement)(Ln,{options:(t=wp.customize.control("generate_settings[typography]").setting.get(),r=Bn(),a=Un(),n=[],Object.keys(a).forEach((function(e){n.push({label:a[e],options:Object.keys(r).filter((function(t){return e===r[t].group})).map((function(e){return{value:e,label:r[e].label,group:r[e].group,module:r[e].module||"core",isDisabled:(a=e,t.some((function(e){return e.selector===a}))&&"custom"!==a)};var a}))})})),n),placeholder:(0,m.__)("Search elements…","generatepress"),currentValue:o,onChange:function(e){l(e,i)}}))},Yn=function(e){var t=e.value,r=e.index,a=e.onChange;return(0,s.createElement)(u.TextControl,{help:(0,m.__)("Enter custom CSS selector.","generatepress"),value:t,onChange:function(e){a("customSelector",e,r)}})},Jn=function(e){var t,r,a,n=e.index,i=e.value,o=e.onChange;return(0,s.createElement)(u.SelectControl,{label:(0,m.__)("Font Family","generatepress"),value:i,options:(t=Gn(),r=[{value:"",label:(0,m.__)("-- Select --","generatepress")},{value:"inherit",label:(0,m.__)("Inherit","generatepress")},{value:"System Default",label:(0,m.__)("System Default","generatepress")}],a=generateCustomizerControls.gpFontLibrary,a&&a.length>0&&a.forEach((function(e){var t=e.alias?e.alias:e.name,a=e.fontFamily?e.fontFamily:t,n=e.cssVariable?"var(".concat(e.cssVariable,")"):a;r.push({value:n,label:t})})),t.length>0&&t.forEach((function(e,a){r.push({value:t[a].fontFamily,label:t[a].fontFamily})})),r),onChange:function(e){o("fontFamily",e,n)}})};function Xn(e,t){var r=[{value:"",label:(0,m.__)("Default","generatepress")},{value:"normal",label:(0,m.__)("Normal","generatepress")},{value:"bold",label:(0,m.__)("Bold","generatepress")},{value:"100",label:"100"},{value:"200",label:"200"},{value:"300",label:"300"},{value:"400",label:"400"},{value:"500",label:"500"},{value:"600",label:"600"},{value:"700",label:"700"},{value:"800",label:"800"},{value:"900",label:"900"}];return void 0!==Ae[e]&&"undefined"!==t.googleFontVariants&&(r=[{value:"",label:(0,m.__)("Default","generatepress")},{value:"normal",label:(0,m.__)("Normal","generatepress")},{value:"bold",label:(0,m.__)("Bold","generatepress")}],t.filter((function(t){return t.fontFamily===e})).forEach((function(e){var t=(e.googleFontVariants?e.googleFontVariants:"").replaceAll(" ","");(t=t.split(",")).filter((function(e){var t=e.match(/[a-z]/g),r=e.match(/[0-9]/g);return!(t&&r||"italic"===e||"regular"===e||""===e)})).forEach((function(e){r.push({value:e,label:e})}))}))),r}var Zn=function(e){var t=e.index,r=e.value,a=e.fontFamily,n=e.onChange;return(0,s.createElement)(u.SelectControl,{label:(0,m.__)("Font Weight","generatepress"),value:r,options:Xn(a,Gn()),onChange:function(e){n("fontWeight",e,t)}})},Qn=function(e){var t=e.index,r=e.value,a=e.onChange;return(0,s.createElement)(u.SelectControl,{label:(0,m.__)("Text Transform","generatepress"),value:r,options:[{value:"",label:(0,m.__)("Default","generatepress")},{value:"uppercase",label:(0,m.__)("Uppercase","generatepress")},{value:"lowercase",label:(0,m.__)("Lowercase","generatepress")},{value:"capitalize",label:(0,m.__)("Capitalize","generatepress")},{value:"initial",label:(0,m.__)("Normal","generatepress")}],onChange:function(e){a("textTransform",e,t)}})},ei=function(e){var t=e.index,r=e.value,a=e.onChange;return(0,s.createElement)(u.SelectControl,{label:(0,m.__)("Font Style","generatepress"),value:r,options:[{value:"",label:(0,m.__)("Default","generatepress")},{value:"normal",label:(0,m.__)("Normal","generatepress")},{value:"italic",label:(0,m.__)("Italic","generatepress")},{value:"oblique",label:(0,m.__)("Oblique","generatepress")},{value:"initial",label:(0,m.__)("Initial","generatepress")}],onChange:function(e){a("fontStyle",e,t)}})},ti=function(e){var t=y((0,l.useState)("desktop"),2),r=(t[0],t[1]),a=e.label,n=e.devices,i=e.onClick,o=void 0===i?function(){return null}:i;return(0,s.createElement)("div",{className:"components-generate-units-control-header__units"},(0,s.createElement)("div",{className:"components-generate-units-control-label__units"},a),(0,s.createElement)("div",{className:"components-generate-control__units"},(0,s.createElement)(u.ButtonGroup,{className:"components-generate-control-buttons__units","aria-label":(0,m.__)("Select Units","generatepress")},n.map((function(e){var t=(0,m.__)("Desktop","generatepress");return"tablet"===e&&(t=(0,m.__)("Tablet","generatepress")),"mobile"===e&&(t=(0,m.__)("Mobile","generatepress")),(0,s.createElement)(u.Tooltip
/* translators: Unit type (px, em, %) */,{text:(0,m.sprintf)((0,m.__)("%s Preview","generatepress"),t),key:e},(0,s.createElement)(u.Button,{key:e,className:"components-generate-control-button__units--"+e,isSmall:!0
/* translators: %s: values associated with CSS syntax, 'Pixel', 'Em', 'Percentage' */,"aria-label":t,onClick:function(){wp.customize.previewedDevice.set(e),r(e),o(e)}},xe(e)))})))))},ri=r(485),ai=r.n(ri);function ni(e){var t=e.value,r=e.onChange,a=e.units,n=void 0===a?[]:a,i=e.disabled;return n.length?(n.includes(t)||(n[n.length-1]=t),(0,s.createElement)(s.Fragment,null,(0,s.createElement)(u.DropdownMenu,{className:"gblocks-unit-control-units",label:(0,m.__)("Select a unit","generatepress"),icon:null,toggleProps:{children:t||String.fromCharCode(8212),disabled:i},popoverProps:{className:"gblocks-unit-control-popover",focusOnMount:!0,noArrow:!1}},(function(e){var a=e.onClose;return(0,s.createElement)(s.Fragment,null,(0,s.createElement)(u.MenuGroup,null,n.map((function(e){return(0,s.createElement)(u.MenuItem,{key:e,onClick:function(){r(e),a()},isSelected:e===t,variant:e===t?"primary":""},e||String.fromCharCode(8212))})),(0,s.createElement)(u.MenuItem,{onClick:function(){window.open("https://developer.mozilla.org/en-US/docs/Learn/CSS/Building_blocks/Values_and_units","_blank").focus()},label:(0,m.__)("Learn more about units","generatepress"),showTooltip:!0},xe("info"))))})))):null}var ii=["px","em","%","rem","vw","vh","ch","cm","mm","in","pt","pc","ex","lh","rlh","vmin","vmax","vb","vi","svw","svh","svb","svi","svmax","svmin","lvw","lvh","lvb","lvi","lvmax","lvmin","dvw","dvh","dvb","dvi","dvmax","dvmin","fr"];function oi(e){var t=e.label,r=e.units,a=void 0===r?[]:r,n=e.defaultUnit,i=void 0===n?"":n,o=e.unitCount,c=void 0===o?7:o,g=e.id,d=e.disabled,f=void 0!==d&&d,p=e.overrideValue,v=void 0===p?null:p,h=e.overrideAction,m=void 0===h?function(){return null}:h,b=e.onChange,S=e.value,w=e.placeholder,O=e.help,C=void 0===O?"":O,E=e.focusOnMount,D=void 0!==E&&E,N=e.onFocus,x=void 0===N?function(){return null}:N,T=a.concat(ii).slice(0,c),I=y((0,l.useState)(""),2),M=I[0],k=I[1],P=y((0,l.useState)(""),2),_=P[0],R=P[1],L=y((0,l.useState)(""),2),A=L[0],F=L[1],j=(0,l.useRef)(!1),V=(0,l.useRef)(!1),B=(0,l.useRef)(!1),H=function(e){var t=ii.join("|"),r=new RegExp("(".concat(t,")"));return e?e.toString().toLowerCase().split(r).filter((function(e){return""!==e})):[]},z=function(e){return e.length>0?e[0].trim():""},U=i||T[0],G=function(e){return e.length>1?e[1]:e.length>0?"":e.length?void 0:U},K=function(e){return/^([-]?\d|[-]?\.)/.test(e)},$=function(){if(!S){var e=H(v||w);F(z(e)),k(G(e))}};return(0,l.useEffect)((function(){var e=v&&f?v:S;if(K(e)){var t=H(e);R(z(t)),k(G(t))}else R(e),k("");$()}),[S,v]),(0,l.useEffect)((function(){if(j.current){var e=!!v&&!!f,t=K(_)?_+M:_;t||(M!==G(H(w))?F(""):$()),e||t===S||b(t)}else j.current=!0}),[_,M]),(0,l.useEffect)((function(){D&&null!=B&&B.current&&B.current.focus()}),[t]),(0,s.createElement)(u.BaseControl,{label:t,help:C,id:g,className:ai()({"gblocks-unit-control":!0,"gblocks-unit-control__disabled":!!f})},(0,s.createElement)("div",{className:"gblocks-unit-control__input",ref:V},(0,s.createElement)(u.TextControl,{type:"text",value:_,placeholder:A,id:g,autoComplete:"off",disabled:f,onChange:function(e){return R(e)},onFocus:function(){x()},ref:B}),(0,s.createElement)("div",{className:"gblocks-unit-control__input--action"},!!m&&(0,s.createElement)("div",{className:"gblocks-unit-control__override-action"},m()," "),(K(_)||!_&&(!A||K(A)))&&(0,s.createElement)(ni,{value:M,disabled:f||1===T.length,units:T,onChange:function(e){return k(e)}}))))}var si=function(e){var t=e.label,r=e.units,a=e.defaultUnit,n=void 0===a?"":a,i=e.desktopValue,o=e.desktopInitial,c=e.desktopOnChange,g=e.tabletValue,d=e.tabletInitial,f=e.tabletOnChange,p=e.mobileInitial,v=e.mobileValue,h=e.mobileOnChange,m=y((0,l.useState)("desktop"),2),b=m[0],S=m[1];return(0,s.createElement)(u.BaseControl,null,(0,s.createElement)(ti,{label:t,devices:["desktop","tablet","mobile"],onClick:function(e){return S(e)}}),(0,s.createElement)("div",{className:"generate-component-input-with-unit"},(0,s.createElement)("div",{className:"generate-component-device-field","data-device":"desktop"},(0,s.createElement)(oi,{key:b,units:r,value:i,placeholder:o,onChange:c,defaultUnit:n})),(0,s.createElement)("div",{className:"generate-component-device-field","data-device":"tablet"},(0,s.createElement)(oi,{key:b,units:r,value:g,placeholder:d,onChange:f,defaultUnit:n})),(0,s.createElement)("div",{className:"generate-component-device-field","data-device":"mobile"},(0,s.createElement)(oi,{key:b,units:r,value:v,placeholder:p,onChange:h,defaultUnit:n}))))},li=function(e){var t=e.font,r=e.onChange;return(0,s.createElement)(si,{label:(0,m.__)("Font size","generatepress"),desktopValue:t.fontSize,desktopInitial:Hn(t,"fontSize"),desktopOnChange:function(e){r("fontSize",e,t.index)},tabletValue:t.fontSizeTablet,tabletInitial:Hn(t,"fontSizeTablet"),tabletOnChange:function(e){r("fontSizeTablet",e,t.index)},mobileValue:t.fontSizeMobile,mobileInitial:Hn(t,"fontSizeMobile"),mobileOnChange:function(e){r("fontSizeMobile",e,t.index)}})},ci=function(e){var t=e.font,r=e.onChange;return(0,s.createElement)(si,{label:(0,m.__)("Line Height","generatepress"),units:[""],defaultUnit:"em",desktopValue:t.lineHeight,desktopInitial:Hn(t,"lineHeight"),desktopOnChange:function(e){r("lineHeight",e,t.index)},tabletValue:t.lineHeightTablet,tabletInitial:Hn(t,"lineHeightTablet"),tabletOnChange:function(e){r("lineHeightTablet",e,t.index)},mobileValue:t.lineHeightMobile,mobileInitial:Hn(t,"lineHeightMobile"),mobileOnChange:function(e){r("lineHeightMobile",e,t.index)}})},ui=function(e){var t=e.font,r=e.onChange;return(0,s.createElement)(si,{label:(0,m.__)("Letter Spacing","generatepress"),defaultUnit:"em",desktopValue:t.letterSpacing,desktopInitial:Hn(t,"letterSpacing"),desktopOnChange:function(e){r("letterSpacing",e,t.index)},tabletValue:t.letterSpacingTablet,tabletInitial:Hn(t,"letterSpacingTablet"),tabletOnChange:function(e){r("letterSpacingTablet",e,t.index)},mobileValue:t.letterSpacingMobile,mobileInitial:Hn(t,"letterSpacingMobile"),mobileOnChange:function(e){r("letterSpacingMobile",e,t.index)}})},gi=function(e){var t=e.font,r=e.onChange;return(0,s.createElement)(si,{label:"body"===t.selector?(0,m.__)("Paragraph Bottom Margin","generatepress"):(0,m.__)("Bottom Margin","generatepress"),defaultUnit:"em",desktopValue:t.marginBottom,desktopInitial:Hn(t,"marginBottom"),desktopOnChange:function(e){r("marginBottom",e,t.index)},tabletValue:t.marginBottomTablet,tabletInitial:Hn(t,"marginBottomTablet"),tabletOnChange:function(e){r("marginBottomTablet",e,t.index)},mobileValue:t.marginBottomMobile,mobileInitial:Hn(t,"marginBottomMobile"),mobileOnChange:function(e){r("marginBottomMobile",e,t.index)}})},di=function(e){var t=e.index,r=e.value,a=e.onChange;return(0,s.createElement)(u.SelectControl,{label:(0,m.__)("Text Decoration","generatepress"),value:r,options:[{value:"",label:(0,m.__)("Default","generatepress")},{value:"none",label:(0,m.__)("None","generatepress")},{value:"underline",label:(0,m.__)("Underline","generatepress")}],onChange:function(e){a("textDecoration",e,t)}})},fi=function(e){var t=e.font,r=e.toggleClose,a=e.onChangeFontValue,n=e.onChangeElement;return(0,s.createElement)("div",{className:"generate-customize-control--font-dropdown"},(0,s.createElement)(qn,{index:t.index,value:t.selector,onChange:n}),!!t.selector&&(0,s.createElement)(s.Fragment,null,"custom"===t.selector&&(0,s.createElement)(Yn,{index:t.index,value:t.customSelector,onChange:a}),(0,s.createElement)(Jn,{index:t.index,value:t.fontFamily,onChange:a}),(0,s.createElement)("div",{className:"components-base-control generate-font-manager--select-options"},(0,s.createElement)(Zn,{index:t.index,value:t.fontWeight,fontFamily:t.fontFamily,onChange:a}),(0,s.createElement)(Qn,{index:t.index,value:t.textTransform,onChange:a}),(0,s.createElement)(ei,{index:t.index,value:t.fontStyle,onChange:a}),(0,s.createElement)(di,{index:t.index,value:t.textDecoration,onChange:a})),(0,s.createElement)(li,{font:t,onChange:a}),(0,s.createElement)(ci,{font:t,onChange:a}),(0,s.createElement)(ui,{font:t,onChange:a}),zn(t.selector)&&(0,s.createElement)(gi,{font:t,onChange:a})),(0,s.createElement)("div",{className:"generate-font-manager--footer"},(0,s.createElement)(u.Button,{isSecondary:!0,isSmall:!0,onClick:r},(0,m.__)("Close","generatepress"))))},pi=function(e){var t=e.font,r=e.label,a=e.itemId,n=e.setOpen,i=e.isOpen,o=e.deleteFont,l=e.toggleClose,c=e.onChangeFontValue,u=e.onChangeElement;return(0,s.createElement)("div",{className:"generate-font-manager--item"},(0,s.createElement)("div",{className:"generate-font-manager--header"},(0,s.createElement)(Kn,{font:t,itemId:a,setOpen:n,isOpen:i,label:r}),(0,s.createElement)($n,{itemId:a,setOpen:n,isOpen:i}),(0,s.createElement)(Wn,{onClick:o.bind(null,t.index),isOpen:i,itemId:a})),a===i&&(0,s.createElement)(fi,{font:t,toggleClose:l,onChangeFontValue:c,onChangeElement:u}))};function vi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}var hi=function(e){var t,r,a=e.fontList,n=a.reduce((function(e,t,r){var a=t.group||"other";return t.index=r,e[a]=e[a]||[],e[a].push(t),e}),{});return t=Un(),r=function(t,r){var a,i=null!==(a=n[r])&&void 0!==a?a:[];if(0!==i.length)return(0,s.createElement)("div",{className:"generate-font-manager-group",key:r},(0,s.createElement)("h4",{className:"generate-font-manager-group__label"},t),i.map((function(t){return(0,s.createElement)(pi,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vi(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vi(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({key:t.index,itemId:t.index+1,font:t},e))})))},Object.values(Object.fromEntries(Object.entries(t).map((function(e){var t=y(e,2),a=t[0],n=t[1];return[a,r(n,a)]}))))},yi=window.lodash;function mi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function bi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mi(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mi(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Si=p.extend((function(e){var t=e.value,r=y((0,l.useState)(0),2),a=r[0],i=r[1],o=y((0,l.useState)([]),2),c=o[0],g=o[1];(0,l.useEffect)((function(){var e=[];Array.isArray(t)?e=t:"object"===n(t)&&(e=Object.values(t)),g(e)}),[]);var d=function(t){g(t),wp.customize.control(e.customizerSetting.id).setting.set(t)},f=function(e,t){var r=wp.customize.control("generate_settings[typography]"),a=r.setting.get(),n=Le(a);a.forEach((function(r,a){""===r.fontFamily&&""===t||r.fontFamily!==t||(n[a]=jn(jn({},n[a]),{},{fontFamily:e}))})),r.setting.set(n),r.renderContent()},p=function(){i(0)},v=(0,An.applyFilters)("generate_font_manager_system_fonts",[{value:"Arial",label:"Arial"},{value:"Helvetica",label:"Helvetica"},{value:"Times New Roman",label:"Times New Roman"},{value:"Georgia",label:"Georgia"}]),h=(0,An.applyFilters)("generate_font_manager_google_fonts",Object.keys(Ae).map((function(e){return{value:e,label:e}}))),b=[{label:(0,m.__)("System fonts","generatepress"),options:v}];generateCustomizerControls.showGoogleFonts&&h.length>0&&b.push({label:(0,m.__)("Google Fonts","generatepress"),options:Object.keys(Ae).map((function(e){return{value:e,label:e}}))}),c.forEach((function(e){var t=e.googleFont&&generateCustomizerControls.showGoogleFonts&&h.length>0?1:0;b[t].options=b[t].options.filter((function(t){return t.value!==e.fontFamily}))}));var S=generateCustomizerControls.gpFontLibrary,w=generateCustomizerControls.gpFontLibraryURI;return(0,s.createElement)("div",null,(0,s.createElement)("div",{className:"customize-control-notifications-container",ref:e.setNotificationContainer}),!!c.length>0&&c.map((function(t,r){var n=r+1,o=function(e){var t=Le(c),a=t[r];t[r]=jn(jn({},t[r]),{},{fontFamily:e}),d(t),void 0!==Ae[e]&&generateCustomizerControls.showGoogleFonts?(t[r]=jn(jn({},t[r]),{},{googleFont:!0,googleFontCategory:Ae[e].category,googleFontVariants:Ae[e].variants.join(", ")}),d(t)):(t[r]=jn(jn({},t[r]),{},{googleFont:!1,googleFontCategory:"",googleFontVariants:""}),d(t)),f(t[r].fontFamily,a.fontFamily)},l=c[r].fontFamily||"";return(0,s.createElement)("div",{className:"generate-font-manager--item",key:r},(0,s.createElement)("div",{className:"generate-font-manager--header"},(0,s.createElement)(u.Button,{className:"generate-font-manager--label",onClick:function(){i(n!==a&&n)}},c[r].fontFamily?c[r].fontFamily:e.label),(0,s.createElement)(u.Tooltip,{text:(0,m.__)("Open Font Family Settings","generatepress")},(0,s.createElement)(u.Button,{className:"generate-font-manager--open",onClick:function(){i(n!==a&&n)}},xe(n===a?"chevron-up":"chevron-down"))),(0,s.createElement)(u.Tooltip,{text:(0,m.__)("Delete Font Family","generatepress")},(0,s.createElement)(u.Button,{className:"generate-font-manager--delete-font",onClick:function(){if(window.confirm((0,m.__)("This will permanently delete this font family. Doing so will stop elements from displaying it as their font.","generatepress"))){var e=Le(c),t=e[r];f("",t.fontFamily),e.splice(r,1),d(e)}}},xe("trash")))),n===a&&(0,s.createElement)("div",{className:"generate-customize-control--font-dropdown"},(0,s.createElement)(u.BaseControl,{className:"generate-component-font-family-picker-wrapper",id:"generate-font-manager-family-name--input"},(0,s.createElement)(Ln,{options:b,placeholder:(0,m.__)("Search fonts…","generatepress"),onChange:function(e){return o(e.value)}}),(0,s.createElement)(u.TextControl,{id:"generate-font-manager-family-name--input",className:"generate-font-manager-family-name--input",label:(0,m.__)("Font family name","generatepress"),value:l,onChange:function(e){var t;t=e,c.filter((function(e){return e.fontFamily===t})).length>0&&(alert((0,m.__)("Font already selected","generatepress")),e=""),o(e)}}),!!c[r].fontFamily&&!!generateCustomizerControls.showGoogleFonts&&(0,s.createElement)("div",{className:"generate-font-manager--options"},(0,s.createElement)(u.ToggleControl,{className:"generate-font-manager-google-font--field",label:(0,m.__)("Use Google Fonts API","generatepress"),checked:!!c[r].googleFont,onChange:function(e){var t=Le(c);t[r]=jn(jn({},t[r]),{},{googleFont:e}),d(t)}}),!!c[r].googleFont&&(0,s.createElement)("div",{className:"generate-font-manager--google-font-options"},(0,s.createElement)(u.TextControl,{label:(0,m.__)("Category","generatepress"),value:c[r].googleFontCategory||"",onChange:function(e){var t=Le(c);t[r]=jn(jn({},t[r]),{},{googleFontCategory:e}),d(t)}}),(0,s.createElement)(u.TextControl,{label:(0,m.__)("Variants","generatepress"),value:c[r].googleFontVariants||"",onChange:function(e){var t=Le(c);t[r]=jn(jn({},t[r]),{},{googleFontVariants:e}),d(t)}}))),(0,s.createElement)("div",{className:"generate-font-manager--footer"},(0,s.createElement)(u.Button,{isSecondary:!0,isSmall:!0,onClick:p},(0,m.__)("Close","generatepress"))))))})),!!S.length>0&&S.map((function(e,t){return(0,s.createElement)("div",{className:"generate-font-manager--item",key:t},(0,s.createElement)("div",{className:"generate-font-manager--header"},(0,s.createElement)("span",{className:"generate-font-manager--label"},e.name),!!w&&(0,s.createElement)(u.Tooltip,{text:(0,m.__)("Font Library","generatepress")},(0,s.createElement)(u.Button,{className:"generate-font-manager--open",href:w},xe("chevron-right")))))})),(0,s.createElement)(u.Button,{isPrimary:!0,onClick:function(){var t=Le(e.value);t.push({fontFamily:"",googleFont:!1,googleFontApi:1,googleFontCategory:"",googleFontVariants:""}),d(t);var r=wp.customize.control(e.customizerSetting.id).setting.get().length;i(r)}},(0,m.__)("Add Font","generatepress")))})),wi=p.extend((function(e){var t=e.value,r=y((0,l.useState)([]),2),a=r[0],i=r[1],o=y((0,l.useState)(0),2),c=o[0],g=o[1];(0,l.useEffect)((function(){var e=[];Array.isArray(t)?e=t:"object"===n(t)&&(e=Object.values(t)),i(e),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=!1;e.forEach((function(r,a){var n=function(e){var t={};return["fontSize","lineHeight","letterSpacing","marginBottom"].forEach((function(r){var a=e[r+"Unit"]||"";["","Tablet","Mobile"].forEach((function(n){var i=r+n;"number"==typeof e[i]&&(t[i]=String(e[i]+a))})),a&&(t[r+"Unit"]="")})),t}(r);(0,yi.isEmpty)(n)||(e[a]=bi(bi({},e[a]),n),t=!0)})),t&&i(e)}(e)}),[]);var d=function(t){i(t),wp.customize.control(e.customizerSetting.id).setting.set(t)};return(0,s.createElement)("div",null,(0,s.createElement)("div",{className:"customize-control-notifications-container",ref:e.setNotificationContainer}),(0,s.createElement)(hi,{fontList:a,setOpen:g,isOpen:c,label:e.label,deleteFont:function(e){var t=Le(a);t.splice(e,1),d(t)},toggleClose:function(){return g(0)},onChangeFontValue:function(e,t,r){var n=Le(a);n[r]=bi({},n[r]),n[r][e]=t,d(n)},onChangeElement:function(e,t){var r=e.value,n=e.group,i=e.module,o=Le(a);o[t]=bi(bi({},o[t]),{},{selector:r,module:i,group:n}),zn(r)||(o[t]=bi(bi({},o[t]),{},{marginBottom:"",marginBottomTablet:"",marginBottomMobile:"",marginBottomUnit:""})),d(o)}}),(0,s.createElement)(u.Button,{isPrimary:!0,onClick:function(){var e=Le(a);e.push({selector:"",customSelector:"",fontFamily:"",fontWeight:"",textTransform:"",textDecoration:"",fontStyle:"",fontSize:"",fontSizeTablet:"",fontSizeMobile:"",lineHeight:"",lineHeightTablet:"",lineHeightMobile:"",letterSpacing:"",letterSpacingTablet:"",letterSpacingMobile:""}),d(e),g(e.length)}},(0,m.__)("Add Typography","generatepress")))}));function Oi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Ci(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Oi(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Oi(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}wp.customize.controlConstructor["generate-font-manager-control"]=Si,wp.customize.controlConstructor["generate-typography-control"]=wi;var Ei={slug:"",color:""},Di=[];function Ni(e,t){switch(t.type){case"SET_COLORS":return Le(t.payload);case"ADD_COLOR":return[].concat(Le(e),[t.payload]);case"UPDATE_COLOR_VALUE":return(0,yi.map)(e,(function(e){return e.slug===t.payload.slug?Ci(Ci({},e),{},{color:t.payload.value}):e}));case"UPDATE_COLOR_SLUG":return(0,yi.map)(e,(function(e){return e.slug===t.payload.slug?Ci(Ci({},e),{},{slug:t.payload.value}):e}));case"DELETE_COLOR":return(0,yi.filter)(e,(function(e){return e.slug!==t.payload}));default:return e}}function xi(e){var t=e.id,r=e.text,a=e.onClick,n=e.icon,i=e.disabled,o="generate-color-manager--".concat(t);return(0,s.createElement)(u.Tooltip,{text:r},(0,s.createElement)(u.Button,{className:o,onClick:a,disabled:i},xe(n)))}function Ti(e){var t=e.onClick;return(0,s.createElement)(xi,{id:"delete-color",text:(0,m.__)("Delete Color","generatepress"),icon:"x",onClick:t})}function Ii(e){var t=e.onClick,r=e.disabled;return(0,s.createElement)(xi,{id:"add-color",text:(0,m.__)("Add Global Color","generatepress"),icon:"plus",disabled:r,onClick:t})}var Mi=(0,l.memo)((function(e){var t=e.index,r=e.value,a=e.variableName,n=e.onChange,i=e.onChangeVariableName,o=e.checkVariableNameIsAvailable,c=y((0,l.useState)(!!r),2),u=c[0],g=c[1],d=y((0,l.useState)(""),2),f=d[0],p=d[1],v=y((0,l.useState)(!1),2),h=v[0],b=v[1],S=y((0,l.useState)(!1),2),w=S[0],O=S[1],C=y((0,l.useState)(""),2),E=C[0],D=C[1];(0,l.useEffect)((function(){p(a)}),[a]),(0,l.useEffect)((function(){b(o((0,yi.toLower)(f),t));var e=new RegExp(/[^a-z0-9\\-]/);O(e.test(f))}),[f]),(0,l.useEffect)((function(){var e=h?"":(0,m.__)("Variable name already used.","generatepress");D(e)}),[h]),(0,l.useEffect)((function(){w?D((0,m.__)("Variable name will be converted to kebab-case.","generatepress")):h&&!w&&D("")}),[w,h]);var N=(0,l.useCallback)((function(){if(h){var e=f.replace(/[^a-zA-Z0-9\s-_]/g,"").trim().replace(/[\s_]+/g,"-").replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();i(e),p(e)}else p(a)}),[h,f,a]);return(0,s.createElement)(_e,{value:r,variableName:f,hideLabel:!0,tooltipText:a||"",tooltipPosition:"bottom center",showAlpha:!0,showReset:!1,showVariableName:!0,showPalette:!1,variableNameIsDisabled:u,variableNameHelpText:E,onChange:n,onClosePanel:function(){g(!0)},onChangeVariableName:p,onBlurVariableName:N,onEnableVariableName:function(){return g(!1)}})}),(function(e,t){return e.value===t.value&&e.variableName===t.variableName&&e.checkVariableNameIsAvailable===t.checkVariableNameIsAvailable}));function ki(e){var t=e.colors,r=e.onChangeColor,a=e.onChangeSlug,n=e.onClickDeleteColor,i=(0,l.useCallback)((function(e,r){return!t.some((function(t,a){return e===t.slug&&a!==r}))}),[JSON.stringify(t)]);return(0,s.createElement)("div",{className:"generate-component-color-picker-wrapper generate-color-manager-wrapper"},t&&t.map((function(e,t){return(0,s.createElement)("div",{key:String(t),className:"generate-color-manager--item"},(0,s.createElement)(Mi,{index:t,value:e.color,variableName:e.slug,onChange:function(t){r(e.slug,t)},onChangeVariableName:function(t){a(e.slug,t)},checkVariableNameIsAvailable:i}),(0,s.createElement)(Ti,{onClick:function(){window.confirm((0,m.__)("This will permanently delete this color. Doing so will break styles that are using it to define their color.","generatepress"))&&n(e.slug)}}))})))}var Pi=r(848);function _i(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Ri="function"==typeof Symbol&&Symbol.observable||"@@observable",Li=function(){return Math.random().toString(36).substring(7).split("").join(".")},Ai={INIT:"@@redux/INIT"+Li(),REPLACE:"@@redux/REPLACE"+Li(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Li()}};function Fi(e,t,r){var a;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(_i(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(_i(1));return r(Fi)(e,t)}if("function"!=typeof e)throw new Error(_i(2));var n=e,i=t,o=[],s=o,l=!1;function c(){s===o&&(s=o.slice())}function u(){if(l)throw new Error(_i(3));return i}function g(e){if("function"!=typeof e)throw new Error(_i(4));if(l)throw new Error(_i(5));var t=!0;return c(),s.push(e),function(){if(t){if(l)throw new Error(_i(6));t=!1,c();var r=s.indexOf(e);s.splice(r,1),o=null}}}function d(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw new Error(_i(7));if(void 0===e.type)throw new Error(_i(8));if(l)throw new Error(_i(9));try{l=!0,i=n(i,e)}finally{l=!1}for(var t=o=s,r=0;r<t.length;r++)(0,t[r])();return e}return d({type:Ai.INIT}),(a={dispatch:d,subscribe:g,getState:u,replaceReducer:function(e){if("function"!=typeof e)throw new Error(_i(10));n=e,d({type:Ai.REPLACE})}})[Ri]=function(){var e,t=g;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(_i(11));function r(){e.next&&e.next(u())}return r(),{unsubscribe:t(r)}}})[Ri]=function(){return this},e},a}function ji(e,t,...r){if("undefined"!=typeof process&&void 0===t)throw new Error("invariant requires an error message argument");if(!e){let e;if(void 0===t)e=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let a=0;e=new Error(t.replace(/%s/g,(function(){return r[a++]}))),e.name="Invariant Violation"}throw e.framesToPop=1,e}}function Vi(e){return"object"==typeof e}const Bi="dnd-core/INIT_COORDS",Hi="dnd-core/BEGIN_DRAG",zi="dnd-core/PUBLISH_DRAG_SOURCE",Ui="dnd-core/HOVER",Gi="dnd-core/DROP",Ki="dnd-core/END_DRAG";function $i(e,t){return{type:Bi,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}const Wi={type:Bi,payload:{clientOffset:null,sourceClientOffset:null}};function qi(e){return function(t=[],r={publishSource:!0}){const{publishSource:a=!0,clientOffset:n,getSourceClientOffset:i}=r,o=e.getMonitor(),s=e.getRegistry();e.dispatch($i(n)),function(e,t,r){ji(!t.isDragging(),"Cannot call beginDrag while dragging."),e.forEach((function(e){ji(r.getSource(e),"Expected sourceIds to be registered.")}))}(t,o,s);const l=function(e,t){let r=null;for(let a=e.length-1;a>=0;a--)if(t.canDragSource(e[a])){r=e[a];break}return r}(t,o);if(null==l)return void e.dispatch(Wi);let c=null;if(n){if(!i)throw new Error("getSourceClientOffset must be defined");!function(e){ji("function"==typeof e,"When clientOffset is provided, getSourceClientOffset must be a function.")}(i),c=i(l)}e.dispatch($i(n,c));const u=s.getSource(l).beginDrag(o,l);if(null==u)return;!function(e){ji(Vi(e),"Item must be an object.")}(u),s.pinSource(l);const g=s.getSourceType(l);return{type:Hi,payload:{itemType:g,item:u,sourceId:l,clientOffset:n||null,sourceClientOffset:c||null,isSourcePublic:!!a}}}}function Yi(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ji(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),a.forEach((function(t){Yi(e,t,r[t])}))}return e}function Xi(e){return function(t={}){const r=e.getMonitor(),a=e.getRegistry();!function(e){ji(e.isDragging(),"Cannot call drop while not dragging."),ji(!e.didDrop(),"Cannot call drop twice during one drag operation.")}(r);const n=function(e){const t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t}(r);n.forEach(((n,i)=>{const o=function(e,t,r,a){const n=r.getTarget(e);let i=n?n.drop(a,e):void 0;return function(e){ji(void 0===e||Vi(e),"Drop result must either be an object or undefined.")}(i),void 0===i&&(i=0===t?{}:a.getDropResult()),i}(n,i,a,r),s={type:Gi,payload:{dropResult:Ji({},t,o)}};e.dispatch(s)}))}}function Zi(e){return function(){const t=e.getMonitor(),r=e.getRegistry();!function(e){ji(e.isDragging(),"Cannot call endDrag while not dragging.")}(t);const a=t.getSourceId();return null!=a&&(r.getSource(a,!0).endDrag(t,a),r.unpinSource()),{type:Ki}}}function Qi(e,t){return null===t?null===e:Array.isArray(e)?e.some((e=>e===t)):e===t}function eo(e){return function(t,{clientOffset:r}={}){!function(e){ji(Array.isArray(e),"Expected targetIds to be an array.")}(t);const a=t.slice(0),n=e.getMonitor(),i=e.getRegistry();return function(e,t,r){for(let a=e.length-1;a>=0;a--){const n=e[a];Qi(t.getTargetType(n),r)||e.splice(a,1)}}(a,i,n.getItemType()),function(e,t,r){ji(t.isDragging(),"Cannot call hover while not dragging."),ji(!t.didDrop(),"Cannot call hover after drop.");for(let t=0;t<e.length;t++){const a=e[t];ji(e.lastIndexOf(a)===t,"Expected targetIds to be unique in the passed array."),ji(r.getTarget(a),"Expected targetIds to be registered.")}}(a,n,i),function(e,t,r){e.forEach((function(e){r.getTarget(e).hover(t,e)}))}(a,n,i),{type:Ui,payload:{targetIds:a,clientOffset:r||null}}}}function to(e){return function(){if(e.getMonitor().isDragging())return{type:zi}}}class ro{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:t}=this.store,r=function(e){return{beginDrag:qi(e),publishDragSource:to(e),hover:eo(e),drop:Xi(e),endDrag:Zi(e)}}(this);return Object.keys(r).reduce(((a,n)=>{const i=r[n];var o;return a[n]=(o=i,(...r)=>{const a=o.apply(e,r);void 0!==a&&t(a)}),a}),{})}dispatch(e){this.store.dispatch(e)}constructor(e,t){this.isSetUp=!1,this.handleRefCountChange=()=>{const e=this.store.getState().refCount>0;this.backend&&(e&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!e&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=t,e.subscribe(this.handleRefCountChange)}}function ao(e,t){return{x:e.x-t.x,y:e.y-t.y}}const no=[],io=[];no.__IS_NONE__=!0,io.__IS_ALL__=!0;class oo{subscribeToStateChange(e,t={}){const{handlerIds:r}=t;ji("function"==typeof e,"listener must be a function."),ji(void 0===r||Array.isArray(r),"handlerIds, when specified, must be an array of strings.");let a=this.store.getState().stateId;return this.store.subscribe((()=>{const t=this.store.getState(),n=t.stateId;try{const i=n===a||n===a+1&&!function(e,t){if(e===no)return!1;if(e===io||void 0===t)return!0;const r=(a=e,t.filter((e=>a.indexOf(e)>-1)));var a;return r.length>0}(t.dirtyHandlerIds,r);i||e()}finally{a=n}}))}subscribeToOffsetChange(e){ji("function"==typeof e,"listener must be a function.");let t=this.store.getState().dragOffset;return this.store.subscribe((()=>{const r=this.store.getState().dragOffset;r!==t&&(t=r,e())}))}canDragSource(e){if(!e)return!1;const t=this.registry.getSource(e);return ji(t,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()&&t.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const t=this.registry.getTarget(e);return ji(t,`Expected to find a valid target. targetId=${e}`),!(!this.isDragging()||this.didDrop())&&(Qi(this.registry.getTargetType(e),this.getItemType())&&t.canDrop(this,e))}isDragging(){return Boolean(this.getItemType())}isDraggingSource(e){if(!e)return!1;const t=this.registry.getSource(e,!0);return ji(t,`Expected to find a valid source. sourceId=${e}`),!(!this.isDragging()||!this.isSourcePublic())&&(this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e))}isOverTarget(e,t={shallow:!1}){if(!e)return!1;const{shallow:r}=t;if(!this.isDragging())return!1;const a=this.registry.getTargetType(e),n=this.getItemType();if(n&&!Qi(a,n))return!1;const i=this.getTargetIds();if(!i.length)return!1;const o=i.indexOf(e);return r?o===i.length-1:o>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return Boolean(this.store.getState().dragOperation.isSourcePublic)}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return function(e){const{clientOffset:t,initialClientOffset:r,initialSourceClientOffset:a}=e;return t&&r&&a?ao(function(e,t){return{x:e.x+t.x,y:e.y+t.y}}(t,a),r):null}(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return function(e){const{clientOffset:t,initialClientOffset:r}=e;return t&&r?ao(t,r):null}(this.store.getState().dragOffset)}constructor(e,t){this.store=e,this.registry=t}}const so="undefined"!=typeof global?global:self,lo=so.MutationObserver||so.WebKitMutationObserver;function co(e){return function(){const t=setTimeout(a,0),r=setInterval(a,50);function a(){clearTimeout(t),clearInterval(r),e()}}}const uo="function"==typeof lo?function(e){let t=1;const r=new lo(e),a=document.createTextNode("");return r.observe(a,{characterData:!0}),function(){t=-t,a.data=t}}:co;class go{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,t){this.onError=e,this.release=t,this.task=null}}const fo=new class{enqueueTask(e){const{queue:t,requestFlush:r}=this;t.length||(r(),this.flushing=!0),t[t.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const t=this.index;if(this.index++,e[t].call(),this.index>this.capacity){for(let t=0,r=e.length-this.index;t<r;t++)e[t]=e[t+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=uo(this.flush),this.requestErrorThrow=co((()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()}))}},po=new class{create(e){const t=this.freeTasks,r=t.length?t.pop():new go(this.onError,(e=>t[t.length]=e));return r.task=e,r}constructor(e){this.onError=e,this.freeTasks=[]}}(fo.registerPendingError),vo="dnd-core/ADD_SOURCE",ho="dnd-core/ADD_TARGET",yo="dnd-core/REMOVE_SOURCE",mo="dnd-core/REMOVE_TARGET";function bo(e,t){t&&Array.isArray(e)?e.forEach((e=>bo(e,!1))):ji("string"==typeof e||"symbol"==typeof e,t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var So;!function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"}(So||(So={}));let wo=0;function Oo(e){switch(e[0]){case"S":return So.SOURCE;case"T":return So.TARGET;default:throw new Error(`Cannot parse handler ID: ${e}`)}}function Co(e,t){const r=e.entries();let a=!1;do{const{done:e,value:[,n]}=r.next();if(n===t)return!0;a=!!e}while(!a);return!1}class Eo{addSource(e,t){bo(e),function(e){ji("function"==typeof e.canDrag,"Expected canDrag to be a function."),ji("function"==typeof e.beginDrag,"Expected beginDrag to be a function."),ji("function"==typeof e.endDrag,"Expected endDrag to be a function.")}(t);const r=this.addHandler(So.SOURCE,e,t);return this.store.dispatch(function(e){return{type:vo,payload:{sourceId:e}}}(r)),r}addTarget(e,t){bo(e,!0),function(e){ji("function"==typeof e.canDrop,"Expected canDrop to be a function."),ji("function"==typeof e.hover,"Expected hover to be a function."),ji("function"==typeof e.drop,"Expected beginDrag to be a function.")}(t);const r=this.addHandler(So.TARGET,e,t);return this.store.dispatch(function(e){return{type:ho,payload:{targetId:e}}}(r)),r}containsHandler(e){return Co(this.dragSources,e)||Co(this.dropTargets,e)}getSource(e,t=!1){return ji(this.isSourceId(e),"Expected a valid source ID."),t&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return ji(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return ji(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return ji(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return Oo(e)===So.SOURCE}isTargetId(e){return Oo(e)===So.TARGET}removeSource(e){var t;ji(this.getSource(e),"Expected an existing source."),this.store.dispatch(function(e){return{type:yo,payload:{sourceId:e}}}(e)),t=()=>{this.dragSources.delete(e),this.types.delete(e)},fo.enqueueTask(po.create(t))}removeTarget(e){ji(this.getTarget(e),"Expected an existing target."),this.store.dispatch(function(e){return{type:mo,payload:{targetId:e}}}(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const t=this.getSource(e);ji(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}unpinSource(){ji(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,t,r){const a=function(e){const t=(wo++).toString();switch(e){case So.SOURCE:return`S${t}`;case So.TARGET:return`T${t}`;default:throw new Error(`Unknown Handler Role: ${e}`)}}(e);return this.types.set(a,t),e===So.SOURCE?this.dragSources.set(a,r):e===So.TARGET&&this.dropTargets.set(a,r),a}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const Do=(e,t)=>e===t;function No(e=no,t){switch(t.type){case Ui:break;case vo:case ho:case mo:case yo:return no;default:return io}const{targetIds:r=[],prevTargetIds:a=[]}=t.payload,n=function(e,t){const r=new Map,a=e=>{r.set(e,r.has(e)?r.get(e)+1:1)};e.forEach(a),t.forEach(a);const n=[];return r.forEach(((e,t)=>{1===e&&n.push(t)})),n}(r,a),i=n.length>0||!function(e,t,r=Do){if(e.length!==t.length)return!1;for(let a=0;a<e.length;++a)if(!r(e[a],t[a]))return!1;return!0}(r,a);if(!i)return no;const o=a[a.length-1],s=r[r.length-1];return o!==s&&(o&&n.push(o),s&&n.push(s)),n}function xo(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const To={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function Io(e=To,t){const{payload:r}=t;switch(t.type){case Bi:case Hi:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case Ui:return a=e.clientOffset,n=r.clientOffset,!a&&!n||a&&n&&a.x===n.x&&a.y===n.y?e:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),a.forEach((function(t){xo(e,t,r[t])}))}return e}({},e,{clientOffset:r.clientOffset});case Ki:case Gi:return To;default:return e}var a,n}function Mo(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ko(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),a.forEach((function(t){Mo(e,t,r[t])}))}return e}const Po={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function _o(e=Po,t){const{payload:r}=t;switch(t.type){case Hi:return ko({},e,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case zi:return ko({},e,{isSourcePublic:!0});case Ui:return ko({},e,{targetIds:r.targetIds});case mo:return-1===e.targetIds.indexOf(r.targetId)?e:ko({},e,{targetIds:(a=e.targetIds,n=r.targetId,a.filter((e=>e!==n)))});case Gi:return ko({},e,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case Ki:return ko({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}var a,n}function Ro(e=0,t){switch(t.type){case vo:case ho:return e+1;case yo:case mo:return e-1;default:return e}}function Lo(e=0){return e+1}function Ao(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),a.forEach((function(t){Ao(e,t,r[t])}))}return e}function jo(e={},t){return{dirtyHandlerIds:No(e.dirtyHandlerIds,{type:t.type,payload:Fo({},t.payload,{prevTargetIds:(r=e,a=[],"dragOperation.targetIds".split(".").reduce(((e,t)=>e&&e[t]?e[t]:a||null),r))})}),dragOffset:Io(e.dragOffset,t),refCount:Ro(e.refCount,t),dragOperation:_o(e.dragOperation,t),stateId:Lo(e.stateId)};var r,a}function Vo(e,t=void 0,r={},a=!1){const n=function(e){const t="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__;return Fi(jo,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}(a),i=new oo(n,new Eo(n)),o=new ro(n,i),s=e(o,t,r);return o.receiveBackend(s),o}const Bo=(0,s.createContext)({dragDropManager:void 0});let Ho=0;const zo=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var Uo=(0,s.memo)((function(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r,a,n={},i=Object.keys(e);for(a=0;a<i.length;a++)r=i[a],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)r=i[a],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,["children"]);const[a,n]=function(e){if("manager"in e)return[{dragDropManager:e.manager},!1];const t=function(e,t=Go(),r,a){const n=t;return n[zo]||(n[zo]={dragDropManager:Vo(e,t,r,a)}),n[zo]}(e.backend,e.context,e.options,e.debugMode);return[t,!e.context]}(r);return(0,s.useEffect)((()=>{if(n){const e=Go();return++Ho,()=>{0==--Ho&&(e[zo]=null)}}}),[]),(0,Pi.jsx)(Bo.Provider,{value:a,children:t})}));function Go(){return"undefined"!=typeof global?global:window}function Ko(e){let t=null;return()=>(null==t&&(t=e()),t)}class $o{enter(e){const t=this.entered.length;return this.entered=function(e,t){const r=new Set,a=e=>r.add(e);e.forEach(a),t.forEach(a);const n=[];return r.forEach((e=>n.push(e))),n}(this.entered.filter((t=>this.isNodeInDocument(t)&&(!t.contains||t.contains(e)))),[e]),0===t&&this.entered.length>0}leave(e){const t=this.entered.length;var r,a;return this.entered=(r=this.entered.filter(this.isNodeInDocument),a=e,r.filter((e=>e!==a))),t>0&&0===this.entered.length}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class Wo{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach((e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null}})}))}loadDataTransfer(e){if(e){const t={};Object.keys(this.config.exposeProperties).forEach((r=>{const a=this.config.exposeProperties[r];null!=a&&(t[r]={value:a(e,this.config.matchesTypes),configurable:!0,enumerable:!0})})),Object.defineProperties(this.item,t)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,t){return t===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}const qo="__NATIVE_FILE__",Yo="__NATIVE_URL__",Jo="__NATIVE_TEXT__",Xo="__NATIVE_HTML__";function Zo(e,t,r){const a=t.reduce(((t,r)=>t||e.getData(r)),"");return null!=a?a:r}const Qo={[qo]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items,dataTransfer:e=>e},matchesTypes:["Files"]},[Xo]:{exposeProperties:{html:(e,t)=>Zo(e,t,""),dataTransfer:e=>e},matchesTypes:["Html","text/html"]},[Yo]:{exposeProperties:{urls:(e,t)=>Zo(e,t,"").split("\n"),dataTransfer:e=>e},matchesTypes:["Url","text/uri-list"]},[Jo]:{exposeProperties:{text:(e,t)=>Zo(e,t,""),dataTransfer:e=>e},matchesTypes:["Text","text/plain"]}};function es(e){if(!e)return null;const t=Array.prototype.slice.call(e.types||[]);return Object.keys(Qo).filter((e=>{const r=Qo[e];return!!(null==r?void 0:r.matchesTypes)&&r.matchesTypes.some((e=>t.indexOf(e)>-1))}))[0]||null}const ts=Ko((()=>/firefox/i.test(navigator.userAgent))),rs=Ko((()=>Boolean(window.safari)));class as{interpolate(e){const{xs:t,ys:r,c1s:a,c2s:n,c3s:i}=this;let o=t.length-1;if(e===t[o])return r[o];let s,l=0,c=i.length-1;for(;l<=c;){s=Math.floor(.5*(l+c));const a=t[s];if(a<e)l=s+1;else{if(!(a>e))return r[s];c=s-1}}o=Math.max(0,c);const u=e-t[o],g=u*u;return r[o]+a[o]*u+n[o]*g+i[o]*u*g}constructor(e,t){const{length:r}=e,a=[];for(let e=0;e<r;e++)a.push(e);a.sort(((t,r)=>e[t]<e[r]?-1:1));const n=[],i=[],o=[];let s,l;for(let a=0;a<r-1;a++)s=e[a+1]-e[a],l=t[a+1]-t[a],i.push(s),n.push(l),o.push(l/s);const c=[o[0]];for(let e=0;e<i.length-1;e++){const t=o[e],r=o[e+1];if(t*r<=0)c.push(0);else{s=i[e];const a=i[e+1],n=s+a;c.push(3*n/((n+a)/t+(n+s)/r))}}c.push(o[o.length-1]);const u=[],g=[];let d;for(let e=0;e<c.length-1;e++){d=o[e];const t=c[e],r=1/i[e],a=t+c[e+1]-d-d;u.push((d-t-a)*r),g.push(a*r*r)}this.xs=e,this.ys=t,this.c1s=c,this.c2s=u,this.c3s=g}}const ns=1;function is(e){const t=e.nodeType===ns?e:e.parentElement;if(!t)return null;const{top:r,left:a}=t.getBoundingClientRect();return{x:a,y:r}}function os(e){return{x:e.clientX,y:e.clientY}}class ss{get window(){return this.globalContext?this.globalContext:"undefined"!=typeof window?window:void 0}get document(){var e;return(null===(e=this.globalContext)||void 0===e?void 0:e.document)?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return(null===(e=this.optionsArgs)||void 0===e?void 0:e.rootElement)||this.window}constructor(e,t){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=t}}function ls(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cs(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),a.forEach((function(t){ls(e,t,r[t])}))}return e}class us{profile(){var e,t;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:(null===(e=this.dragStartSourceIds)||void 0===e?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:(null===(t=this.dragOverTargetIds)||void 0===t?void 0:t.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const e=this.rootElement;if(void 0!==e){if(e.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){const e=this.rootElement;var t;void 0!==e&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId&&(null===(t=this.window)||void 0===t||t.cancelAnimationFrame(this.asyncEndDragFrameId)))}connectDragPreview(e,t,r){return this.sourcePreviewNodeOptions.set(e,r),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,t,r){this.sourceNodes.set(e,t),this.sourceNodeOptions.set(e,r);const a=t=>this.handleDragStart(t,e),n=e=>this.handleSelectStart(e);return t.setAttribute("draggable","true"),t.addEventListener("dragstart",a),t.addEventListener("selectstart",n),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),t.removeEventListener("dragstart",a),t.removeEventListener("selectstart",n),t.setAttribute("draggable","false")}}connectDropTarget(e,t){const r=t=>this.handleDragEnter(t,e),a=t=>this.handleDragOver(t,e),n=t=>this.handleDrop(t,e);return t.addEventListener("dragenter",r),t.addEventListener("dragover",a),t.addEventListener("drop",n),()=>{t.removeEventListener("dragenter",r),t.removeEventListener("dragover",a),t.removeEventListener("drop",n)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),t=this.sourceNodeOptions.get(e);return cs({dropEffect:this.altKeyPressed?"copy":"move"},t||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId();return cs({anchorX:.5,anchorY:.5,captureDraggingState:!1},this.sourcePreviewNodeOptions.get(e)||{})}isDraggingNativeItem(){const e=this.monitor.getItemType();return Object.keys(a).some((t=>a[t]===e))}beginDragNativeItem(e,t){this.clearCurrentDragSourceNode(),this.currentNativeSource=function(e,t){const r=Qo[e];if(!r)throw new Error(`native type ${e} has no configuration`);const a=new Wo(r);return a.loadDataTransfer(t),a}(e,t),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e,this.mouseMoveTimeoutTimer=setTimeout((()=>{var e;return null===(e=this.rootElement)||void 0===e?void 0:e.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}),1e3)}clearCurrentDragSourceNode(){var e;return!!this.currentDragSourceNode&&(this.currentDragSourceNode=null,this.rootElement&&(null===(e=this.window)||void 0===e||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)),this.mouseMoveTimeoutTimer=null,!0)}handleDragStart(e,t){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(t))}handleDragEnter(e,t){this.dragEnterTargetIds.unshift(t)}handleDragOver(e,t){null===this.dragOverTargetIds&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(t)}handleDrop(e,t){this.dropTargetIds.unshift(t)}constructor(e,t,r){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=e=>{const t=this.sourceNodes.get(e);return t&&is(t)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=e=>Boolean(e&&this.document&&this.document.body&&this.document.body.contains(e)),this.endDragIfSourceWasRemovedFromDOM=()=>{const e=this.currentDragSourceNode;null==e||this.isNodeInDocument(e)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=e=>{null===this.hoverRafId&&"undefined"!=typeof requestAnimationFrame&&(this.hoverRafId=requestAnimationFrame((()=>{this.monitor.isDragging()&&this.actions.hover(e||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null})))},this.cancelHover=()=>{null!==this.hoverRafId&&"undefined"!=typeof cancelAnimationFrame&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=e=>{if(e.defaultPrevented)return;const{dragStartSourceIds:t}=this;this.dragStartSourceIds=null;const r=os(e);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(t||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:r});const{dataTransfer:a}=e,n=es(a);if(this.monitor.isDragging()){if(a&&"function"==typeof a.setDragImage){const e=this.monitor.getSourceId(),t=this.sourceNodes.get(e),n=this.sourcePreviewNodes.get(e)||t;if(n){const{anchorX:e,anchorY:i,offsetX:o,offsetY:s}=this.getCurrentSourcePreviewNodeOptions(),l=function(e,t,r,a,n){const i=function(e){var t;return"IMG"===e.nodeName&&(ts()||!(null===(t=document.documentElement)||void 0===t?void 0:t.contains(e)))}(t),o=is(i?e:t),s={x:r.x-o.x,y:r.y-o.y},{offsetWidth:l,offsetHeight:c}=e,{anchorX:u,anchorY:g}=a,{dragPreviewWidth:d,dragPreviewHeight:f}=function(e,t,r,a){let n=e?t.width:r,i=e?t.height:a;return rs()&&e&&(i/=window.devicePixelRatio,n/=window.devicePixelRatio),{dragPreviewWidth:n,dragPreviewHeight:i}}(i,t,l,c),{offsetX:p,offsetY:v}=n,h=0===v||v;return{x:0===p||p?p:new as([0,.5,1],[s.x,s.x/l*d,s.x+d-l]).interpolate(u),y:h?v:(()=>{let e=new as([0,.5,1],[s.y,s.y/c*f,s.y+f-c]).interpolate(g);return rs()&&i&&(e+=(window.devicePixelRatio-1)*f),e})()}}(t,n,r,{anchorX:e,anchorY:i},{offsetX:o,offsetY:s});a.setDragImage(n,l.x,l.y)}}try{null==a||a.setData("application/json",{})}catch(e){}this.setCurrentDragSourceNode(e.target);const{captureDraggingState:t}=this.getCurrentSourcePreviewNodeOptions();t?this.actions.publishDragSource():setTimeout((()=>this.actions.publishDragSource()),0)}else if(n)this.beginDragNativeItem(n);else{if(a&&!a.types&&(e.target&&!e.target.hasAttribute||!e.target.hasAttribute("draggable")))return;e.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=e=>{var t;if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()&&(null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)),!this.enterLeaveCounter.enter(e.target)||this.monitor.isDragging())return;const{dataTransfer:r}=e,a=es(r);a&&this.beginDragNativeItem(a,r)},this.handleTopDragEnter=e=>{const{dragEnterTargetIds:t}=this;this.dragEnterTargetIds=[],this.monitor.isDragging()&&(this.altKeyPressed=e.altKey,t.length>0&&this.actions.hover(t,{clientOffset:os(e)}),t.some((e=>this.monitor.canDropOnTarget(e)))&&(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())))},this.handleTopDragOverCapture=e=>{var t;this.dragOverTargetIds=[],this.isDraggingNativeItem()&&(null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer))},this.handleTopDragOver=e=>{const{dragOverTargetIds:t}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging())return e.preventDefault(),void(e.dataTransfer&&(e.dataTransfer.dropEffect="none"));this.altKeyPressed=e.altKey,this.lastClientOffset=os(e),this.scheduleHover(t),(t||[]).some((e=>this.monitor.canDropOnTarget(e)))?(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?e.preventDefault():(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=e=>{this.isDraggingNativeItem()&&e.preventDefault(),this.enterLeaveCounter.leave(e.target)&&(this.isDraggingNativeItem()&&setTimeout((()=>this.endDragNativeItem()),0),this.cancelHover())},this.handleTopDropCapture=e=>{var t;this.dropTargetIds=[],this.isDraggingNativeItem()?(e.preventDefault(),null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)):es(e.dataTransfer)&&e.preventDefault(),this.enterLeaveCounter.reset()},this.handleTopDrop=e=>{const{dropTargetIds:t}=this;this.dropTargetIds=[],this.actions.hover(t,{clientOffset:os(e)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=e=>{const t=e.target;"function"==typeof t.dragDrop&&("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||(e.preventDefault(),t.dragDrop()))},this.options=new ss(t,r),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new $o(this.isNodeInDocument)}}const gs=function(e,t,r){return new us(e,t,r)};var ds=r(17);const fs="undefined"!=typeof window?s.useLayoutEffect:s.useEffect;function ps(e,t,r){return function(e,t,r){const[a,n]=function(e,t,r){const[a,n]=(0,s.useState)((()=>t(e))),i=(0,s.useCallback)((()=>{const i=t(e);ds(a,i)||(n(i),r&&r())}),[a,e,r]);return fs(i),[a,i]}(e,t,r);return fs((function(){const t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(n,{handlerIds:[t]})}),[e,n]),a}(t,e||(()=>({})),(()=>r.reconnect()))}function vs(e,t){const r=[...t||[]];return null==t&&"function"!=typeof e&&r.push(e),(0,s.useMemo)((()=>"function"==typeof e?e():e),r)}function hs(e){return(0,s.useMemo)((()=>e.hooks.dropTarget()),[e])}function ys(e,t,r,a){let n=r?r.call(a,e,t):void 0;if(void 0!==n)return!!n;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;const i=Object.keys(e),o=Object.keys(t);if(i.length!==o.length)return!1;const s=Object.prototype.hasOwnProperty.bind(t);for(let o=0;o<i.length;o++){const l=i[o];if(!s(l))return!1;const c=e[l],u=t[l];if(n=r?r.call(a,c,u,l):void 0,!1===n||void 0===n&&c!==u)return!1}return!0}function ms(e){return null!==e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function bs(e){const t={};return Object.keys(e).forEach((r=>{const a=e[r];if(r.endsWith("Ref"))t[r]=e[r];else{const e=function(e){return(t=null,r=null)=>{if(!(0,s.isValidElement)(t)){const a=t;return e(a,r),a}const a=t;!function(e){if("string"==typeof e.type)return;const t=e.type.displayName||e.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${t} into a <div>, or turn it into a drag source or a drop target itself.`)}(a);const n=r?t=>e(t,r):e;return function(e,t){const r=e.ref;return ji("string"!=typeof r,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?(0,s.cloneElement)(e,{ref:e=>{Ss(r,e),Ss(t,e)}}):(0,s.cloneElement)(e,{ref:t})}(a,n)}}(a);t[r]=()=>e}})),t}function Ss(e,t){"function"==typeof e?e(t):e.current=t}class ws{get connectTarget(){return this.dropTarget}reconnect(){const e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();const t=this.dropTarget;this.handlerId&&(t?e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=t,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,t,this.dropTargetOptions)):this.lastConnectedDropTarget=t)}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!ys(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(e){this.hooks=bs({dropTarget:(e,t)=>{this.clearDropTarget(),this.dropTargetOptions=t,ms(e)?this.dropTargetRef=e:this.dropTargetNode=e,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=e}}function Os(){const{dragDropManager:e}=(0,s.useContext)(Bo);return ji(null!=e,"Expected drag drop context"),e}let Cs=!1;class Es{receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}canDrop(){if(!this.targetId)return!1;ji(!Cs,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return Cs=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{Cs=!1}}isOver(e){return!!this.targetId&&this.internalMonitor.isOverTarget(this.targetId,e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}}class Ds{canDrop(){const e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}hover(){const e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}drop(){const e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}constructor(e,t){this.spec=e,this.monitor=t}}function Ns(e,t){const r=vs(e,t),a=function(){const e=Os();return(0,s.useMemo)((()=>new Es(e)),[e])}(),n=function(e){const t=Os(),r=(0,s.useMemo)((()=>new ws(t.getBackend())),[t]);return fs((()=>(r.dropTargetOptions=e||null,r.reconnect(),()=>r.disconnectDropTarget())),[e]),r}(r.options);return function(e,t,r){const a=Os(),n=function(e,t){const r=(0,s.useMemo)((()=>new Ds(e,t)),[t]);return(0,s.useEffect)((()=>{r.spec=e}),[e]),r}(e,t),i=function(e){const{accept:t}=e;return(0,s.useMemo)((()=>(ji(null!=e.accept,"accept must be defined"),Array.isArray(t)?t:[t])),[t])}(e);fs((function(){const[e,o]=function(e,t,r){const a=r.getRegistry(),n=a.addTarget(e,t);return[n,()=>a.removeTarget(n)]}(i,n,a);return t.receiveHandlerId(e),r.receiveHandlerId(e),o}),[a,t,n,r,i.map((e=>e.toString())).join("|")])}(r,a,n),[ps(r.collect,a,n),hs(n)]}function xs(e){return(0,s.useMemo)((()=>e.hooks.dragSource()),[e])}function Ts(e){return(0,s.useMemo)((()=>e.hooks.dragPreview()),[e])}class Is{receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){const e=this.reconnectDragSource();this.reconnectDragPreview(e)}reconnectDragSource(){const e=this.dragSource,t=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return t&&this.disconnectDragSource(),this.handlerId?e?(t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)),t):(this.lastConnectedDragSource=e,t):t}reconnectDragPreview(e=!1){const t=this.dragPreview,r=e||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();r&&this.disconnectDragPreview(),this.handlerId&&(t?r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=t,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,t,this.dragPreviewOptions)):this.lastConnectedDragPreview=t)}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!ys(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!ys(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(e){this.hooks=bs({dragSource:(e,t)=>{this.clearDragSource(),this.dragSourceOptions=t||null,ms(e)?this.dragSourceRef=e:this.dragSourceNode=e,this.reconnectDragSource()},dragPreview:(e,t)=>{this.clearDragPreview(),this.dragPreviewOptions=t||null,ms(e)?this.dragPreviewRef=e:this.dragPreviewNode=e,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=e}}let Ms=!1,ks=!1;class Ps{receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){ji(!Ms,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Ms=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{Ms=!1}}isDragging(){if(!this.sourceId)return!1;ji(!ks,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return ks=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{ks=!1}}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,t){return this.internalMonitor.isOverTarget(e,t)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}}class _s{beginDrag(){const e=this.spec,t=this.monitor;let r=null;return r="object"==typeof e.item?e.item:"function"==typeof e.item?e.item(t):{},null!=r?r:null}canDrag(){const e=this.spec,t=this.monitor;return"boolean"==typeof e.canDrag?e.canDrag:"function"!=typeof e.canDrag||e.canDrag(t)}isDragging(e,t){const r=this.spec,a=this.monitor,{isDragging:n}=r;return n?n(a):t===e.getSourceId()}endDrag(){const e=this.spec,t=this.monitor,r=this.connector,{end:a}=e;a&&a(t.getItem(),t),r.reconnect()}constructor(e,t,r){this.spec=e,this.monitor=t,this.connector=r}}function Rs(e,t){const r=vs(e,t);ji(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const a=function(){const e=Os();return(0,s.useMemo)((()=>new Ps(e)),[e])}(),n=function(e,t){const r=Os(),a=(0,s.useMemo)((()=>new Is(r.getBackend())),[r]);return fs((()=>(a.dragSourceOptions=e||null,a.reconnect(),()=>a.disconnectDragSource())),[a,e]),fs((()=>(a.dragPreviewOptions=t||null,a.reconnect(),()=>a.disconnectDragPreview())),[a,t]),a}(r.options,r.previewOptions);return function(e,t,r){const a=Os(),n=function(e,t,r){const a=(0,s.useMemo)((()=>new _s(e,t,r)),[t,r]);return(0,s.useEffect)((()=>{a.spec=e}),[e]),a}(e,t,r),i=function(e){return(0,s.useMemo)((()=>{const t=e.type;return ji(null!=t,"spec.type must be defined"),t}),[e])}(e);fs((function(){if(null!=i){const[e,o]=function(e,t,r){const a=r.getRegistry(),n=a.addSource(e,t);return[n,()=>a.removeSource(n)]}(i,n,a);return t.receiveHandlerId(e),r.receiveHandlerId(e),o}}),[a,t,r,n,i])}(r,a,n),[ps(r.collect,a,n),xs(n),Ts(n)]}function Ls(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function As(e){var t=e.id,r=e.itemType,a=e.onDrop,n=e.getIndex,i=e.className,l=e.innerComponentProps,c=e.InnerComponent,u=y(Rs((function(){return{type:r,item:{id:t},collect:function(e){return{isDragging:e.isDragging()}}}}),[t]),2),g=u[0].isDragging,d=u[1],f=Ns((function(){return{accept:r,hover:function(e){var r=e.id;if(r!==t){var i=n(t);a(r,i)}}}}),[n,a]),p=y(f,2)[1],v=g?"0.2":1;return(0,s.createElement)("div",{ref:function(e){return d(p(e))},className:i,style:{opacity:v}},c&&(0,s.createElement)(c,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ls(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ls(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},l)))}function Fs(e){var t=e.data,r=e.itemIdKey,a=e.onDrop,n=e.getIndex,i=e.itemType,o=e.listClassName,l=e.itemClassName,c=e.InnerComponent,u=y(Ns((function(){return{accept:i}})),2)[1];return(0,s.createElement)("div",{ref:u,className:o},t&&t.map((function(e){return(0,s.createElement)(As,{key:e[r],id:e[r],itemType:i,className:l,onDrop:a,getIndex:n,InnerComponent:c,innerComponentProps:{item:e}})})))}function js(e){var t=e.idKey,r=void 0===t?"id":t,a=e.acceptedType,n=void 0===a?"simple-list-item":a,i=e.listData,o=void 0===i?[]:i,c=e.onChangeData,u=e.listClassName,g=void 0===u?"":u,d=e.itemClassName,f=void 0===d?"":d,p=e.InnerComponent,v=y((0,l.useState)([]),2),h=v[0],m=v[1];(0,l.useEffect)((function(){m(o)}),[JSON.stringify(o)]),(0,l.useEffect)((function(){c(h)}),[JSON.stringify(h)]);var b=(0,l.useCallback)((function(e){var t=h.find((function(t){return e===t[r]}));return h.indexOf(t)}),[JSON.stringify(h)]),S=(0,l.useCallback)((function(e,t){var r=b(e);m(function(e,t,r){var a=Array.from(e),n=y(a.splice(t,1),1)[0];return a.splice(r,0,n),a}(h,r,t))}),[JSON.stringify(h)]);return(0,s.createElement)(Uo,{backend:gs},(0,s.createElement)(Fs,{data:h,itemIdKey:r,onDrop:S,getIndex:b,itemType:n,listClassName:g,itemClassName:f,InnerComponent:p}))}function Vs(e){var t=e.item;return(0,s.createElement)(u.Tooltip,{text:t.slug,position:"top center"},(0,s.createElement)("span",{style:{backgroundColor:t.color}}))}var Bs=p.extend((function(e){var t=function(){var e=y((0,l.useReducer)(Ni,Di),2),t=e[0],r=e[1];return{colors:t,setColors:function(e){return r({type:"SET_COLORS",payload:e})},addColor:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r({type:"ADD_COLOR",payload:Object.assign({},Ei,{slug:e,color:t})})},deleteColor:function(e){return r({type:"DELETE_COLOR",payload:e})},updateColorValue:function(e,t){return r({type:"UPDATE_COLOR_VALUE",payload:{slug:e,value:t}})},updateColorSlug:function(e,t){return r({type:"UPDATE_COLOR_SLUG",payload:{slug:e,value:t}})}}}(),r=t.colors,a=t.setColors,n=t.addColor,i=t.deleteColor,o=t.updateColorValue,c=t.updateColorSlug,u=y((0,l.useState)(!1),2),g=u[0],d=u[1],f=y((0,l.useState)(!1),2),p=f[0],v=f[1],h=y((0,l.useState)([]),2),b=h[0],S=h[1];(0,l.useEffect)((function(){var t=(0,yi.isObject)(e.value)?Object.values(e.value):e.value;a(t),d(!0)}),[]),(0,l.useEffect)((function(){var t,a;g&&(a=[],(t=r).length>0&&t.forEach((function(e){a.push({name:e.slug,slug:e.slug,color:"var(--"+e.slug+")"})})),window.sessionStorage.setItem("generateGlobalColors",JSON.stringify(a)),function(t){wp.customize.control(e.customizerSetting.id).setting.set(t);var r=":root {";t.length>0&&t.forEach((function(e){var t=e.slug.replace(" ","-").toLowerCase();r+="--"+t+": "+e.color+";"})),r+="}";var a=document.getElementById("generate-global-color-styles");a?a.innerHTML=r:document.body.insertAdjacentHTML("beforeend",'<style id="generate-global-color-styles">'+r+"</style>")}(r))}),[JSON.stringify(r),g]);var w=(0,l.useCallback)((function(){n(function e(t){var a="global-color-".concat(t+1);return-1===(0,yi.findIndex)(r,{slug:a})?a:e(t+1)}(r.length))}),[r.length]);return(0,s.createElement)(s.Fragment,null,(0,s.createElement)("div",{className:"customize-control-notifications-container",ref:e.setNotificationContainer}),(0,s.createElement)("div",{className:"generate-color-manager-wrapper"},(0,s.createElement)("div",{className:"generate-color-manager--item"},(0,s.createElement)(Ii,{onClick:w,disabled:p})),(0,s.createElement)("div",{className:"generate-color-manager--item"},(0,s.createElement)(xi,{id:"add-color",icon:p?"check":"reorder",text:p?(0,m.__)("Finish re-ordering","generatepress"):(0,m.__)("Re-order colors","generatepress"),onClick:function(e){e.preventDefault(),p&&(a(b),window.sessionStorage.setItem("generateGlobalColors",JSON.stringify(b))),v(!p)}}))),p?(0,s.createElement)(js,{listData:r,idKey:"slug",listClassName:"generate-color-manager-dnd-list",itemClassName:"generate-color-manager-dnd-list-item",InnerComponent:Vs,onChangeData:S}):(0,s.createElement)(ki,{colors:r,onChangeColor:o,onChangeSlug:c,onClickDeleteColor:i}))}));wp.customize.controlConstructor["generate-color-manager-control"]=Bs;var Hs=function(e){var t=y((0,l.useState)(!1),2),r=t[0],a=t[1],n=function(){e.choices.sectionRedirect?wp.customize.section(e.choices.toggleId).focus():document.querySelectorAll('[data-toggleId="'+e.choices.toggleId+'"]').forEach((function(e){r?(e.style.display="",a(!1)):(e.style.display="block",a(!0))}))},i=e.choices.tooltipText?e.choices.tooltipText:(0,m.sprintf)((0,m.__)("Open %s Settings","generatepress"),e.choices.title),o=e.choices.sectionRedirect?"chevron-right":"chevron-down";return(0,s.createElement)(s.Fragment,null,(0,s.createElement)("div",{className:"generate-customize-control-title"},!!e.choices.toggleId&&(0,s.createElement)(s.Fragment,null,(0,s.createElement)(u.Tooltip,{text:i},(0,s.createElement)(u.Button,{className:"generate-customize-control-title--label",onClick:n},e.choices.title)),(0,s.createElement)(u.Tooltip,{text:i},(0,s.createElement)(u.Button,{className:"generate-customize-control-title--toggle",onClick:n},xe(r?"chevron-up":o)))),!e.choices.toggleId&&(0,s.createElement)("h3",null,e.choices.title)))};function zs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function Us(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?zs(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zs(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Gs=wp.customize.Control.extend({ready:function(){},embed:function(){var e=this,t=e.section();t&&wp.customize.section(t,(function(t){t.expanded.bind((function(t){t&&e.actuallyEmbed()}))}))},actuallyEmbed:function(){var e=this;"resolved"!==e.deferred.embedded.state()&&(e.renderContent(),e.deferred.embedded.resolve())},initialize:function(e,t){var r=this;r.setNotificationContainer=r.setNotificationContainer.bind(r),wp.customize.Control.prototype.initialize.call(r,e,t),wp.customize.control.bind("removed",(function e(t){r===t&&(r.destroy(),r.container.remove(),wp.customize.control.unbind("removed",e))}))},setNotificationContainer:function(e){this.notifications.container=jQuery(e),this.notifications.render()},renderContent:function(){var e=this,t=(0,s.createElement)(u.SlotFillProvider,null,(0,s.createElement)(Hs,Us(Us({},e.params),{},{control:e,choices:e.params.choices,title:e.params.title})),(0,s.createElement)(u.Popover.Slot,null)),r=e.container[0];if(e.params.choices.wrapper){var a=document.getElementById(e.params.choices.wrapper+"--wrapper");a&&(r=a,e.container.hide())}c(t,r)},destroy:function(){(0,l.unmountComponentAtNode)(this.container[0]),wp.customize.Control.prototype.destroy&&wp.customize.Control.prototype.destroy.call(this)}});wp.customize.controlConstructor["generate-title-control"]=Gs,document.addEventListener("DOMContentLoaded",(function(){window.sessionStorage.removeItem("generateGlobalColors")}))},485:function(e,t){var r;!function(){"use strict";var a={}.hasOwnProperty;function n(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)){if(r.length){var o=n.apply(null,r);o&&e.push(o)}}else if("object"===i){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var s in r)a.call(r,s)&&r[s]&&e.push(s)}}}return e.join(" ")}e.exports?(n.default=n,e.exports=n):void 0===(r=function(){return n}.apply(t,[]))||(e.exports=r)}()},17:function(e){"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var a,n,i;if(Array.isArray(t)){if((a=t.length)!=r.length)return!1;for(n=a;0!=n--;)if(!e(t[n],r[n]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((a=(i=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(n=a;0!=n--;)if(!Object.prototype.hasOwnProperty.call(r,i[n]))return!1;for(n=a;0!=n--;){var o=i[n];if(!e(t[o],r[o]))return!1}return!0}return t!=t&&r!=r}},146:function(e,t,r){"use strict";var a=r(363),n={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return a.isMemo(e)?o:s[e.$$typeof]||n}s[a.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[a.Memo]=o;var c=Object.defineProperty,u=Object.getOwnPropertyNames,g=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,f=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,r,a){if("string"!=typeof r){if(p){var n=f(r);n&&n!==p&&e(t,n,a)}var o=u(r);g&&(o=o.concat(g(r)));for(var s=l(t),v=l(r),h=0;h<o.length;++h){var y=o[h];if(!(i[y]||a&&a[y]||v&&v[y]||s&&s[y])){var m=d(r,y);try{c(t,y,m)}catch(e){}}}}return t}},799:function(e,t){"use strict";var r="function"==typeof Symbol&&Symbol.for,a=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,s=r?Symbol.for("react.profiler"):60114,l=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,u=r?Symbol.for("react.async_mode"):60111,g=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,f=r?Symbol.for("react.suspense"):60113,p=r?Symbol.for("react.suspense_list"):60120,v=r?Symbol.for("react.memo"):60115,h=r?Symbol.for("react.lazy"):60116,y=r?Symbol.for("react.block"):60121,m=r?Symbol.for("react.fundamental"):60117,b=r?Symbol.for("react.responder"):60118,S=r?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case a:switch(e=e.type){case u:case g:case i:case s:case o:case f:return e;default:switch(e=e&&e.$$typeof){case c:case d:case h:case v:case l:return e;default:return t}}case n:return t}}}function O(e){return w(e)===g}t.AsyncMode=u,t.ConcurrentMode=g,t.ContextConsumer=c,t.ContextProvider=l,t.Element=a,t.ForwardRef=d,t.Fragment=i,t.Lazy=h,t.Memo=v,t.Portal=n,t.Profiler=s,t.StrictMode=o,t.Suspense=f,t.isAsyncMode=function(e){return O(e)||w(e)===u},t.isConcurrentMode=O,t.isContextConsumer=function(e){return w(e)===c},t.isContextProvider=function(e){return w(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===a},t.isForwardRef=function(e){return w(e)===d},t.isFragment=function(e){return w(e)===i},t.isLazy=function(e){return w(e)===h},t.isMemo=function(e){return w(e)===v},t.isPortal=function(e){return w(e)===n},t.isProfiler=function(e){return w(e)===s},t.isStrictMode=function(e){return w(e)===o},t.isSuspense=function(e){return w(e)===f},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===g||e===s||e===o||e===f||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===v||e.$$typeof===l||e.$$typeof===c||e.$$typeof===d||e.$$typeof===m||e.$$typeof===b||e.$$typeof===S||e.$$typeof===y)},t.typeOf=w},363:function(e,t,r){"use strict";e.exports=r(799)},20:function(e,t,r){"use strict";var a=r(609),n=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),o=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,r){var a,l={},c=null,u=null;for(a in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,a)&&!s.hasOwnProperty(a)&&(l[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===l[a]&&(l[a]=t[a]);return{$$typeof:n,type:e,key:c,ref:u,props:l,_owner:o.current}}},848:function(e,t,r){"use strict";e.exports=r(20)},609:function(e){"use strict";e.exports=window.React}},r={};function a(e){var n=r[e];if(void 0!==n)return n.exports;var i=r[e]={exports:{}};return t[e](i,i.exports,a),i.exports}a.m=t,e=[],a.O=function(t,r,n,i){if(!r){var o=1/0;for(u=0;u<e.length;u++){r=e[u][0],n=e[u][1],i=e[u][2];for(var s=!0,l=0;l<r.length;l++)(!1&i||o>=i)&&Object.keys(a.O).every((function(e){return a.O[e](r[l])}))?r.splice(l--,1):(s=!1,i<o&&(o=i));if(s){e.splice(u--,1);var c=n();void 0!==c&&(t=c)}}return t}i=i||0;for(var u=e.length;u>0&&e[u-1][2]>i;u--)e[u]=e[u-1];e[u]=[r,n,i]},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e={242:0,863:0};a.O.j=function(t){return 0===e[t]};var t=function(t,r){var n,i,o=r[0],s=r[1],l=r[2],c=0;if(o.some((function(t){return 0!==e[t]}))){for(n in s)a.o(s,n)&&(a.m[n]=s[n]);if(l)var u=l(a)}for(t&&t(r);c<o.length;c++)i=o[c],a.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return a.O(u)},r=self.webpackChunkgeneratepress=self.webpackChunkgeneratepress||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}(),a.nc=void 0;var n=a.O(void 0,[863],(function(){return a(822)}));n=a.O(n)}();