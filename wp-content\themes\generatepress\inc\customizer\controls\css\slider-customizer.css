.customize-control-generatepress-range-slider .generatepress-slider {
	position: relative;
	width: calc(100% - 60px);
	height: 6px;
	background-color: rgba(0,0,0,.10);
	cursor: pointer;
	-webkit-transition: background .5s;
	-moz-transition: background .5s;
	transition: background .5s;
}

.customize-control-generatepress-range-slider .has-unit .generatepress-slider {
	width: calc(100% - 90px);
}

.customize-control-generatepress-range-slider .gp_range_value.hide-value {
	display: none;
}

.customize-control-generatepress-range-slider .gp_range_value.hide-value + .generatepress-slider {
	width: 100%;
}

.customize-control-generatepress-range-slider .generatepress-slider .ui-slider-handle {
	height: 16px;
	width: 16px;
	background-color: #3498D9;
	display: inline-block;
	position: absolute;
	top: 50%;
	-webkit-transform: translateY(-50%) translateX(-4px);
	transform: translateY(-50%) translateX(-4px);
	border-radius: 50%;
	cursor: pointer;
}

.gp-range-title-area {
	display: flex;
}

.gp-range-slider-controls {
	margin-left: auto;
}

.customize-control-generatepress-range-slider .wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.customize-control-generatepress-range-slider .gp_range_value {
	font-size: 14px;
	padding: 0;
	font-weight: 400;
	width: 50px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.customize-control-generatepress-range-slider .has-unit .gp_range_value {
	width: 80px;
}

.customize-control-generatepress-range-slider .gp_range_value span.value {
    font-size: 12px;
    width: calc(100% - 2px);
    text-align: center;
    min-height: 30px;
    background: #FFF;
    line-height: 30px;
    border: 1px solid #DDD;
}

.customize-control-generatepress-range-slider .has-unit .gp_range_value span.value {
	width: calc(100% - 32px);
	display: block;
}

.customize-control-generatepress-range-slider .gp_range_value .unit {
    width: 29px;
    text-align: center;
    font-size: 12px;
    line-height: 30px;
	background: #fff;
	border: 1px solid #ddd;
	margin-left: 1px;
}

.customize-control-generatepress-range-slider .generatepress-range-slider-reset span {
    font-size: 16px;
    line-height: 22px;
}

.customize-control-generatepress-range-slider .gp_range_value input {
    font-size: 12px;
    padding: 0px;
    text-align: center;
    min-height: 30px;
	height: auto;
	border-radius: 0;
	border-color: #ddd;
}

.customize-control-generatepress-range-slider .has-unit .gp_range_value input {
    width: calc(100% - 30px);
}

.customize-control-generatepress-range-slider .gp-range-title-area .dashicons {
	cursor: pointer;
	font-size: 11px;
	width: 20px;
	height: 20px;
	line-height: 20px;
	color: #222;
	text-align: center;
	position: relative;
	top: 2px;
}

.customize-control-generatepress-range-slider .gp-range-title-area .dashicons:hover {
	background: #fafafa;
}

.customize-control-generatepress-range-slider .gp-range-title-area .dashicons.selected {
	background: #fff;
	color: #222;
}

.customize-control-generatepress-range-slider .gp-device-controls > span:first-child:last-child {
	display: none;
}

.customize-control-generatepress-range-slider .sub-description {
	margin-top: 10px;
}
