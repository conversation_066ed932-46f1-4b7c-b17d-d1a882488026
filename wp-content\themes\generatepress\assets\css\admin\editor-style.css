img {
	max-width: 100%;
	height: auto;
}

pre {
	background: rgba(0, 0, 0, 0.05);
	font-family: inherit;
	font-size: inherit;
	line-height: normal;
	margin-bottom: 1.5em;
	padding: 20px;
	overflow: auto;
	max-width: 100%;
}

blockquote {
	border-left: 5px solid rgba(0, 0, 0, 0.05);
	padding: 20px;
	font-size: 1.2em;
	font-style:italic;
	margin: 0 0 1.5em;
	position: relative;
}

blockquote p:last-child {
	margin: 0;
}

table, th, td {
    border: 1px solid rgba(0, 0, 0, 0.1);
}
table {
    border-collapse: separate;
    border-spacing: 0;
    border-width: 1px 0 0 1px;
    margin: 0 0 1.5em;
    table-layout: fixed;
    width: 100%;
}
th,
td {
	padding: 8px;
}
th {
    border-width: 0 1px 1px 0;
}
td {
    border-width: 0 1px 1px 0;
}

hr {
	background-color: rgba(0, 0, 0, 0.1);
	border: 0;
	height: 1px;
	margin-bottom: 40px;
	margin-top: 40px;
}

/* Make sure embeds and iframes fit their containers */
embed,
iframe,
object {
	max-width: 100%;
}