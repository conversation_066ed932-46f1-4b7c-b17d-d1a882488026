<?php
/**
 * Taxonomy Manager Class
 * Handles registration and management of rooftop taxonomies
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rooftop_Taxonomy_Manager {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_taxonomies'), 5);
        add_action('init', array($this, 'update_existing_taxonomies'), 10);
    }
    
    /**
     * Register all rooftop taxonomies
     */
    public function register_taxonomies() {
        $taxonomies = $this->get_taxonomy_definitions();
        
        foreach ($taxonomies as $taxonomy_key => $taxonomy_config) {
            if (!taxonomy_exists($taxonomy_key)) {
                register_taxonomy($taxonomy_key, array('rooftops'), $taxonomy_config);
            }
        }
    }
    
    /**
     * Update existing taxonomies to ensure they're properly configured
     */
    public function update_existing_taxonomies() {
        // Update the rooftops post type to include all taxonomies
        $post_type_object = get_post_type_object('rooftops');
        if ($post_type_object) {
            $taxonomies = array_keys($this->get_taxonomy_definitions());
            $post_type_object->taxonomies = $taxonomies;
        }
    }
    
    /**
     * Get all taxonomy definitions
     */
    public function get_taxonomy_definitions() {
        return array(
            // Location-Based Taxonomies
            'neighborhoods' => array(
                'hierarchical' => true,
                'labels' => array(
                    'name' => __('Neighborhoods', 'rooftop-json-importer'),
                    'singular_name' => __('Neighborhood', 'rooftop-json-importer'),
                    'search_items' => __('Search Neighborhoods', 'rooftop-json-importer'),
                    'all_items' => __('All Neighborhoods', 'rooftop-json-importer'),
                    'parent_item' => __('Parent Neighborhood', 'rooftop-json-importer'),
                    'parent_item_colon' => __('Parent Neighborhood:', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Neighborhood', 'rooftop-json-importer'),
                    'update_item' => __('Update Neighborhood', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Neighborhood', 'rooftop-json-importer'),
                    'new_item_name' => __('New Neighborhood Name', 'rooftop-json-importer'),
                    'menu_name' => __('Neighborhoods', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'neighborhoods'),
                'show_in_rest' => true,
            ),
            
            // Experience & Vibe Taxonomies
            'atmosphere' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Atmosphere', 'rooftop-json-importer'),
                    'singular_name' => __('Atmosphere', 'rooftop-json-importer'),
                    'search_items' => __('Search Atmosphere', 'rooftop-json-importer'),
                    'all_items' => __('All Atmosphere Types', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Atmosphere', 'rooftop-json-importer'),
                    'update_item' => __('Update Atmosphere', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Atmosphere', 'rooftop-json-importer'),
                    'new_item_name' => __('New Atmosphere Name', 'rooftop-json-importer'),
                    'menu_name' => __('Atmosphere', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'atmosphere'),
                'show_in_rest' => true,
            ),
            
            'best_for' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Best For', 'rooftop-json-importer'),
                    'singular_name' => __('Best For', 'rooftop-json-importer'),
                    'search_items' => __('Search Best For', 'rooftop-json-importer'),
                    'all_items' => __('All Best For Options', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Best For', 'rooftop-json-importer'),
                    'update_item' => __('Update Best For', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Best For', 'rooftop-json-importer'),
                    'new_item_name' => __('New Best For Name', 'rooftop-json-importer'),
                    'menu_name' => __('Best For', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'best-for'),
                'show_in_rest' => true,
            ),
            
            'view_type' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('View Types', 'rooftop-json-importer'),
                    'singular_name' => __('View Type', 'rooftop-json-importer'),
                    'search_items' => __('Search View Types', 'rooftop-json-importer'),
                    'all_items' => __('All View Types', 'rooftop-json-importer'),
                    'edit_item' => __('Edit View Type', 'rooftop-json-importer'),
                    'update_item' => __('Update View Type', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New View Type', 'rooftop-json-importer'),
                    'new_item_name' => __('New View Type Name', 'rooftop-json-importer'),
                    'menu_name' => __('View Types', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'view-type'),
                'show_in_rest' => true,
            ),
            
            'music_style' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Music Styles', 'rooftop-json-importer'),
                    'singular_name' => __('Music Style', 'rooftop-json-importer'),
                    'search_items' => __('Search Music Styles', 'rooftop-json-importer'),
                    'all_items' => __('All Music Styles', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Music Style', 'rooftop-json-importer'),
                    'update_item' => __('Update Music Style', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Music Style', 'rooftop-json-importer'),
                    'new_item_name' => __('New Music Style Name', 'rooftop-json-importer'),
                    'menu_name' => __('Music Styles', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'music-style'),
                'show_in_rest' => true,
            ),
            
            // Features & Amenities Taxonomies
            'amenities' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Amenities', 'rooftop-json-importer'),
                    'singular_name' => __('Amenity', 'rooftop-json-importer'),
                    'search_items' => __('Search Amenities', 'rooftop-json-importer'),
                    'all_items' => __('All Amenities', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Amenity', 'rooftop-json-importer'),
                    'update_item' => __('Update Amenity', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Amenity', 'rooftop-json-importer'),
                    'new_item_name' => __('New Amenity Name', 'rooftop-json-importer'),
                    'menu_name' => __('Amenities', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'amenities'),
                'show_in_rest' => true,
            ),
            
            'accessibility_features' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Accessibility Features', 'rooftop-json-importer'),
                    'singular_name' => __('Accessibility Feature', 'rooftop-json-importer'),
                    'search_items' => __('Search Accessibility Features', 'rooftop-json-importer'),
                    'all_items' => __('All Accessibility Features', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Accessibility Feature', 'rooftop-json-importer'),
                    'update_item' => __('Update Accessibility Feature', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Accessibility Feature', 'rooftop-json-importer'),
                    'new_item_name' => __('New Accessibility Feature Name', 'rooftop-json-importer'),
                    'menu_name' => __('Accessibility', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'accessibility-features'),
                'show_in_rest' => true,
            ),
            
            // Food & Drink Taxonomies
            'menu_type' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Menu Types', 'rooftop-json-importer'),
                    'singular_name' => __('Menu Type', 'rooftop-json-importer'),
                    'search_items' => __('Search Menu Types', 'rooftop-json-importer'),
                    'all_items' => __('All Menu Types', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Menu Type', 'rooftop-json-importer'),
                    'update_item' => __('Update Menu Type', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Menu Type', 'rooftop-json-importer'),
                    'new_item_name' => __('New Menu Type Name', 'rooftop-json-importer'),
                    'menu_name' => __('Menu Types', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'menu-type'),
                'show_in_rest' => true,
            ),
            
            'cuisine_style' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Cuisine Styles', 'rooftop-json-importer'),
                    'singular_name' => __('Cuisine Style', 'rooftop-json-importer'),
                    'search_items' => __('Search Cuisine Styles', 'rooftop-json-importer'),
                    'all_items' => __('All Cuisine Styles', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Cuisine Style', 'rooftop-json-importer'),
                    'update_item' => __('Update Cuisine Style', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Cuisine Style', 'rooftop-json-importer'),
                    'new_item_name' => __('New Cuisine Style Name', 'rooftop-json-importer'),
                    'menu_name' => __('Cuisine Styles', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'cuisine-style'),
                'show_in_rest' => true,
            ),
            
            'dietary_options' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Dietary Options', 'rooftop-json-importer'),
                    'singular_name' => __('Dietary Option', 'rooftop-json-importer'),
                    'search_items' => __('Search Dietary Options', 'rooftop-json-importer'),
                    'all_items' => __('All Dietary Options', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Dietary Option', 'rooftop-json-importer'),
                    'update_item' => __('Update Dietary Option', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Dietary Option', 'rooftop-json-importer'),
                    'new_item_name' => __('New Dietary Option Name', 'rooftop-json-importer'),
                    'menu_name' => __('Dietary Options', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'dietary-options'),
                'show_in_rest' => true,
            ),
            
            // Practical Taxonomies
            'price_range' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Price Ranges', 'rooftop-json-importer'),
                    'singular_name' => __('Price Range', 'rooftop-json-importer'),
                    'search_items' => __('Search Price Ranges', 'rooftop-json-importer'),
                    'all_items' => __('All Price Ranges', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Price Range', 'rooftop-json-importer'),
                    'update_item' => __('Update Price Range', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Price Range', 'rooftop-json-importer'),
                    'new_item_name' => __('New Price Range Name', 'rooftop-json-importer'),
                    'menu_name' => __('Price Ranges', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'price-range'),
                'show_in_rest' => true,
            ),
            
            'dress_code' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Dress Codes', 'rooftop-json-importer'),
                    'singular_name' => __('Dress Code', 'rooftop-json-importer'),
                    'search_items' => __('Search Dress Codes', 'rooftop-json-importer'),
                    'all_items' => __('All Dress Codes', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Dress Code', 'rooftop-json-importer'),
                    'update_item' => __('Update Dress Code', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Dress Code', 'rooftop-json-importer'),
                    'new_item_name' => __('New Dress Code Name', 'rooftop-json-importer'),
                    'menu_name' => __('Dress Codes', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'dress-code'),
                'show_in_rest' => true,
            ),
            
            'venue_type' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Venue Types', 'rooftop-json-importer'),
                    'singular_name' => __('Venue Type', 'rooftop-json-importer'),
                    'search_items' => __('Search Venue Types', 'rooftop-json-importer'),
                    'all_items' => __('All Venue Types', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Venue Type', 'rooftop-json-importer'),
                    'update_item' => __('Update Venue Type', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Venue Type', 'rooftop-json-importer'),
                    'new_item_name' => __('New Venue Type Name', 'rooftop-json-importer'),
                    'menu_name' => __('Venue Types', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'venue-type'),
                'show_in_rest' => true,
            ),
            
            // Legacy taxonomies (for backward compatibility)
            'popular_features' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Popular Features', 'rooftop-json-importer'),
                    'singular_name' => __('Popular Feature', 'rooftop-json-importer'),
                    'search_items' => __('Search Popular Features', 'rooftop-json-importer'),
                    'all_items' => __('All Popular Features', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Popular Feature', 'rooftop-json-importer'),
                    'update_item' => __('Update Popular Feature', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Popular Feature', 'rooftop-json-importer'),
                    'new_item_name' => __('New Popular Feature Name', 'rooftop-json-importer'),
                    'menu_name' => __('Popular Features', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'popular'),
                'show_in_rest' => true,
            ),
            
            'services' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Services', 'rooftop-json-importer'),
                    'singular_name' => __('Service', 'rooftop-json-importer'),
                    'search_items' => __('Search Services', 'rooftop-json-importer'),
                    'all_items' => __('All Services', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Service', 'rooftop-json-importer'),
                    'update_item' => __('Update Service', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Service', 'rooftop-json-importer'),
                    'new_item_name' => __('New Service Name', 'rooftop-json-importer'),
                    'menu_name' => __('Services', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'services'),
                'show_in_rest' => true,
            ),
            
            'specialties' => array(
                'hierarchical' => false,
                'labels' => array(
                    'name' => __('Specialties', 'rooftop-json-importer'),
                    'singular_name' => __('Specialty', 'rooftop-json-importer'),
                    'search_items' => __('Search Specialties', 'rooftop-json-importer'),
                    'all_items' => __('All Specialties', 'rooftop-json-importer'),
                    'edit_item' => __('Edit Specialty', 'rooftop-json-importer'),
                    'update_item' => __('Update Specialty', 'rooftop-json-importer'),
                    'add_new_item' => __('Add New Specialty', 'rooftop-json-importer'),
                    'new_item_name' => __('New Specialty Name', 'rooftop-json-importer'),
                    'menu_name' => __('Specialties', 'rooftop-json-importer'),
                ),
                'show_ui' => true,
                'show_admin_column' => true,
                'query_var' => true,
                'rewrite' => array('slug' => 'specialties'),
                'show_in_rest' => true,
            ),
        );
    }
}
